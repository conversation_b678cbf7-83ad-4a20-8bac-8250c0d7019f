# Render.com deployment configuration for PharmAccounts Monorepo
# This replaces the separate frontend and backend deployments with a single service

services:
  - type: web
    name: pharmaccounts-monorepo
    runtime: node
    plan: starter # Change to a higher plan as needed
    buildCommand: node scripts/build.js
    startCommand: cd backend && npm start
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 3000
      # Add your environment variables here
      # - key: DATABASE_URL
      #   fromDatabase:
      #     name: pharmaccounts-db
      #     property: connectionString
      # - key: FIREBASE_PROJECT_ID
      #   sync: false
      # - key: FIREBASE_PRIVATE_KEY
      #   sync: false
      # - key: FIREBASE_CLIENT_EMAIL
      #   sync: false
    healthCheckPath: /health

    # Auto-deploy settings
    autoDeploy: true

    # Custom domains (optional)
    # domains:
    #   - pharmaccounts.yourdomain.com

    # Disk storage for uploads
    disk:
      name: pharmaccounts-uploads
      mountPath: /app/backend/uploads
      sizeGB: 1

# Optional: Database service
# databases:
#   - name: pharmaccounts-db
#     databaseName: pharmaccounts
#     user: pharmaccounts_user
#     plan: starter

# Optional: Redis service for caching
# services:
#   - type: redis
#     name: pharmaccounts-redis
#     plan: starter