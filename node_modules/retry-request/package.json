{"name": "retry-request", "version": "5.0.2", "description": "Retry a request.", "main": "index.js", "repository": "stephenplusplus/retry-request", "scripts": {"docs": "jsdoc -c .jsdoc.js", "predocs-test": "npm run docs", "docs-test": "linkinator docs", "fix": "gts fix", "lint": "gts check", "test": "mocha --timeout 0", "system-test": ""}, "files": ["index.js", "index.d.ts", "license"], "types": "index.d.ts", "keywords": ["request", "retry", "stream"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "engines": {"node": ">=12"}, "dependencies": {"debug": "^4.1.1", "extend": "^3.0.2"}, "devDependencies": {"@types/request": "^2.48.8", "async": "^3.0.1", "gts": "^3.1.0", "jsdoc": "^3.6.3", "jsdoc-fresh": "^2.0.0", "jsdoc-region-tag": "^2.0.0", "linkinator": "^4.0.0", "lodash.range": "^3.2.0", "mocha": "^9.2.2", "request": "^2.87.0", "typescript": "^4.6.3"}}