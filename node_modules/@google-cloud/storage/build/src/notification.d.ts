import { Metadata, MetadataCallback, ServiceObject } from './nodejs-common';
import { ResponseBody } from './nodejs-common/util';
import { Bucket } from './bucket';
export interface DeleteNotificationOptions {
    userProject?: string;
}
export interface GetNotificationMetadataOptions {
    userProject?: string;
}
/**
 * @typedef {array} GetNotificationMetadataResponse
 * @property {object} 0 The notification metadata.
 * @property {object} 1 The full API response.
 */
export type GetNotificationMetadataResponse = [ResponseBody, Metadata];
/**
 * @callback GetNotificationMetadataCallback
 * @param {?Error} err Request error, if any.
 * @param {object} files The notification metadata.
 * @param {object} apiResponse The full API response.
 */
export interface GetNotificationMetadataCallback {
    (err: Error | null, metadata?: ResponseBody, apiResponse?: Metadata): void;
}
/**
 * @typedef {array} GetNotificationResponse
 * @property {Notification} 0 The {@link Notification}
 * @property {object} 1 The full API response.
 */
export type GetNotificationResponse = [Notification, Metadata];
export interface GetNotificationOptions {
    /**
     * Automatically create the object if it does not exist. Default: `false`.
     */
    autoCreate?: boolean;
    /**
     * The ID of the project which will be billed for the request.
     */
    userProject?: string;
}
/**
 * @callback GetNotificationCallback
 * @param {?Error} err Request error, if any.
 * @param {Notification} notification The {@link Notification}.
 * @param {object} apiResponse The full API response.
 */
export interface GetNotificationCallback {
    (err: Error | null, notification?: Notification | null, apiResponse?: Metadata): void;
}
/**
 * @callback DeleteNotificationCallback
 * @param {?Error} err Request error, if any.
 * @param {object} apiResponse The full API response.
 */
export interface DeleteNotificationCallback {
    (err: Error | null, apiResponse?: Metadata): void;
}
/**
 * The API-formatted resource description of the notification.
 *
 * Note: This is not guaranteed to be up-to-date when accessed. To get the
 * latest record, call the `getMetadata()` method.
 *
 * @name Notification#metadata
 * @type {object}
 */
/**
 * A Notification object is created from your {@link Bucket} object using
 * {@link Bucket#notification}. Use it to interact with Cloud Pub/Sub
 * notifications.
 *
 * See {@link https://cloud.google.com/storage/docs/pubsub-notifications| Cloud Pub/Sub Notifications for Google Cloud Storage}
 *
 * @class
 * @hideconstructor
 *
 * @param {Bucket} bucket The bucket instance this notification is attached to.
 * @param {string} id The ID of the notification.
 *
 * @example
 * ```
 * const {Storage} = require('@google-cloud/storage');
 * const storage = new Storage();
 * const myBucket = storage.bucket('my-bucket');
 *
 * const notification = myBucket.notification('1');
 * ```
 */
declare class Notification extends ServiceObject {
    constructor(bucket: Bucket, id: string);
    delete(options?: DeleteNotificationOptions): Promise<[Metadata]>;
    delete(options: DeleteNotificationOptions, callback: DeleteNotificationCallback): void;
    delete(callback: DeleteNotificationCallback): void;
    get(options?: GetNotificationOptions): Promise<GetNotificationResponse>;
    get(options: GetNotificationOptions, callback: GetNotificationCallback): void;
    get(callback: GetNotificationCallback): void;
    getMetadata(options?: GetNotificationMetadataOptions): Promise<GetNotificationMetadataResponse>;
    getMetadata(options: GetNotificationMetadataOptions, callback: MetadataCallback): void;
    getMetadata(callback: MetadataCallback): void;
}
/**
 * Reference to the {@link Notification} class.
 * @name module:@google-cloud/storage.Notification
 * @see Notification
 */
export { Notification };
