{"version": 3, "sources": ["lib/prelude.js", "../node_modules/@protobufjs/aspromise/index.js", "../node_modules/@protobufjs/base64/index.js", "../node_modules/@protobufjs/codegen/index.js", "../node_modules/@protobufjs/eventemitter/index.js", "../node_modules/@protobufjs/fetch/index.js", "../node_modules/@protobufjs/float/index.js", "../node_modules/@protobufjs/inquire/index.js", "../node_modules/@protobufjs/path/index.js", "../node_modules/@protobufjs/pool/index.js", "../node_modules/@protobufjs/utf8/index.js", "../src/common.js", "../src/converter.js", "../src/decoder.js", "../src/encoder.js", "../src/enum.js", "../src/field.js", "../src/index-light.js", "../src/index-minimal.js", "../src/index", "../src/mapfield.js", "../src/message.js", "../src/method.js", "../src/namespace.js", "../src/object.js", "../src/oneof.js", "../src/parse.js", "../src/reader.js", "../src/reader_buffer.js", "../src/root.js", "../src/roots.js", "../src/rpc.js", "../src/rpc/service.js", "../src/service.js", "../src/tokenize.js", "../src/type.js", "../src/types.js", "../src/util.js", "../src/util/longbits.js", "../src/util/minimal.js", "../src/verifier.js", "../src/wrappers.js", "../src/writer.js", "../src/writer_buffer.js"], "names": ["undefined", "modules", "cache", "entries", "protobuf", "$require", "name", "$module", "call", "exports", "util", "global", "define", "amd", "<PERSON>", "isLong", "configure", "module", "1", "require", "fn", "ctx", "params", "Array", "arguments", "length", "offset", "index", "pending", "Promise", "resolve", "reject", "err", "apply", "base64", "string", "p", "n", "Math", "ceil", "b64", "s64", "i", "encode", "buffer", "start", "end", "t", "parts", "chunk", "j", "b", "push", "String", "fromCharCode", "slice", "join", "invalidEncoding", "decode", "c", "charCodeAt", "Error", "test", "codegen", "functionParams", "functionName", "body", "Codegen", "formatStringOrScope", "source", "toString", "verbose", "console", "log", "scopeKeys", "Object", "keys", "scopeParams", "scopeValues", "scopeOffset", "Function", "formatParams", "formatOffset", "replace", "$0", "$1", "value", "Number", "floor", "JSON", "stringify", "functionNameOverride", "EventEmitter", "this", "_listeners", "prototype", "on", "evt", "off", "listeners", "splice", "emit", "args", "fetch", "<PERSON><PERSON><PERSON><PERSON>", "fs", "inquire", "filename", "options", "callback", "xhr", "readFile", "contents", "XMLHttpRequest", "binary", "onreadystatechange", "readyState", "status", "response", "responseText", "Uint8Array", "overrideMimeType", "responseType", "open", "send", "factory", "writeFloat_ieee754", "writeUint", "val", "buf", "pos", "sign", "isNaN", "round", "exponent", "LN2", "pow", "readFloat_ieee754", "readUint", "uint", "mantissa", "NaN", "Infinity", "writeFloat_f32_cpy", "f32", "f8b", "writeFloat_f32_rev", "readFloat_f32_cpy", "readFloat_f32_rev", "f64", "le", "writeDouble_ieee754", "off0", "off1", "readDouble_ieee754", "lo", "hi", "writeDouble_f64_cpy", "writeDouble_f64_rev", "readDouble_f64_cpy", "readDouble_f64_rev", "Float32Array", "writeFloatLE", "writeFloatBE", "readFloatLE", "readFloatBE", "bind", "writeUintLE", "writeUintBE", "readUintLE", "readUintBE", "Float64Array", "writeDoubleLE", "writeDoubleBE", "readDoubleLE", "readDoubleBE", "moduleName", "mod", "eval", "e", "isAbsolute", "path", "normalize", "split", "absolute", "prefix", "shift", "originPath", "include<PERSON>ath", "alreadyNormalized", "alloc", "size", "SIZE", "MAX", "slab", "utf8", "len", "read", "write", "c1", "c2", "common", "commonRe", "json", "nested", "google", "Any", "fields", "type_url", "type", "id", "Duration", "timeType", "seconds", "nanos", "Timestamp", "Empty", "Struct", "keyType", "Value", "oneofs", "kind", "oneof", "nullValue", "numberValue", "stringValue", "boolValue", "structValue", "listValue", "Null<PERSON><PERSON>ue", "values", "NULL_VALUE", "ListValue", "rule", "DoubleValue", "FloatValue", "Int64Value", "UInt64Value", "Int32Value", "UInt32Value", "BoolValue", "StringValue", "BytesValue", "FieldMask", "paths", "get", "file", "Enum", "genValuePartial_fromObject", "gen", "field", "fieldIndex", "prop", "resolvedType", "typeDefault", "repeated", "fullName", "isUnsigned", "genValuePartial_toObject", "converter", "fromObject", "mtype", "fieldsArray", "safeProp", "map", "toObject", "sort", "compareFieldsById", "repeatedFields", "mapFields", "normalFields", "partOf", "arrayDefault", "valuesById", "long", "low", "high", "unsigned", "toNumber", "bytes", "hasKs2", "_fieldsArray", "indexOf", "filter", "group", "ref", "types", "defaults", "basic", "packed", "rfield", "required", "wireType", "mapKey", "genTypePartial", "optional", "ReflectionObject", "Namespace", "create", "constructor", "className", "comment", "comments", "valuesOptions", "TypeError", "reserved", "fromJSON", "enm", "toJSON", "toJSONOptions", "keepComments", "add", "isString", "isInteger", "isReservedId", "isReservedName", "allow_alias", "remove", "Field", "Type", "ruleRe", "extend", "isObject", "toLowerCase", "message", "defaultValue", "extensionField", "declaringField", "_packed", "defineProperty", "getOption", "setOption", "ifNotSet", "resolved", "parent", "lookupTypeOrEnum", "proto3_optional", "fromNumber", "freeze", "new<PERSON>uffer", "emptyObject", "emptyArray", "ctor", "d", "fieldId", "fieldType", "fieldRule", "decorateType", "decorateEnum", "fieldName", "default", "_configure", "Type_", "build", "load", "root", "Root", "loadSync", "encoder", "decoder", "verifier", "OneOf", "MapField", "Service", "Method", "Message", "wrappers", "Writer", "BufferWriter", "Reader", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rpc", "roots", "tokenize", "parse", "resolvedKeyType", "fieldKeyType", "fieldValueType", "properties", "$type", "writer", "encodeDelimited", "reader", "decodeDelimited", "verify", "object", "requestType", "requestStream", "responseStream", "parsedOptions", "resolvedRequestType", "resolvedResponseType", "lookupType", "arrayToJSON", "array", "obj", "_nested<PERSON><PERSON>y", "clearCache", "namespace", "addJSON", "toArray", "nested<PERSON><PERSON><PERSON>", "nested<PERSON><PERSON>", "names", "methods", "getEnum", "prev", "setOptions", "onAdd", "onRemove", "isArray", "ptr", "part", "resolveAll", "lookup", "filterTypes", "parentAlreadyChecked", "found", "lookupEnum", "lookupService", "Service_", "Enum_", "defineProperties", "unshift", "_handleAdd", "_handleRemove", "setParsedOption", "propName", "opt", "newOpt", "find", "hasOwnProperty", "newValue", "setProperty", "Root_", "fieldNames", "addFieldsToParent", "oneofName", "oneOfGetter", "set", "oneOfSetter", "keepCase", "base10Re", "base10NegRe", "base16Re", "base16NegRe", "base8Re", "base8NegRe", "numberRe", "nameRe", "typeRefRe", "fqTypeRefRe", "pkg", "imports", "weakImports", "syntax", "token", "whichImports", "preferTrailingComment", "tn", "alternateCommentMode", "next", "peek", "skip", "cmnt", "head", "isProto3", "applyCase", "camelCase", "illegal", "insideTryCatch", "line", "readString", "readValue", "acceptTypeRef", "parseNumber", "substring", "parseInt", "parseFloat", "readRanges", "target", "acceptStrings", "parseId", "acceptNegative", "parse<PERSON><PERSON><PERSON>", "parseOption", "parseType", "parseEnum", "parseService", "service", "ifBlock", "parseMethod", "commentText", "method", "parseExtension", "reference", "parseField", "fnIf", "fnElse", "trailingLine", "parseMapField", "valueType", "parseInlineOptions", "extensions", "parseGroup", "lcFirst", "ucFirst", "parseEnumValue", "dummy", "isCustom", "option", "optionValue", "parseOptionValue", "objectResult", "lastValue", "prevValue", "concat", "simpleValue", "package", "LongBits", "indexOutOfRange", "write<PERSON><PERSON>th", "RangeError", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "create_array", "readLongVarint", "bits", "readFixed32_end", "readFixed64", "_slice", "subarray", "uint32", "int32", "sint32", "bool", "fixed32", "sfixed32", "float", "double", "skipType", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_", "merge", "int64", "uint64", "sint64", "zzDecode", "fixed64", "sfixed64", "utf8Slice", "min", "deferred", "files", "SYNC", "<PERSON><PERSON><PERSON>", "self", "sync", "finish", "cb", "getBundledFileName", "idx", "lastIndexOf", "altname", "process", "parsed", "queued", "weak", "setTimeout", "readFileSync", "isNode", "exposeRe", "tryHandleExtension", "sisterField", "extendedType", "parse_", "common_", "rpcImpl", "requestDelimited", "responseDelimited", "rpcCall", "requestCtor", "responseCtor", "request", "endedByRPC", "_methodsArray", "inherited", "methodsArray", "rpcService", "methodName", "isReserved", "m", "q", "s", "delimRe", "stringDoubleRe", "stringSingleRe", "setCommentRe", "setCommentAltRe", "setCommentSplitRe", "whitespaceRe", "unescapeRe", "unescapeMap", "0", "r", "unescape", "str", "lastCommentLine", "stack", "<PERSON><PERSON><PERSON><PERSON>", "subject", "char<PERSON>t", "setComment", "isLeading", "lineEmpty", "leading", "lookback", "commentOffset", "lines", "trim", "text", "isDoubleSlashCommentLine", "startOffset", "endOffset", "findEndOfLine", "lineText", "cursor", "re", "match", "lastIndex", "exec", "repeat", "curr", "isDoc", "isLeadingComment", "expected", "actual", "ret", "_fieldsById", "_oneofsArray", "_ctor", "fieldsById", "oneofsArray", "generateConstructor", "ctorProperties", "setup", "originalThis", "wrapper", "fork", "l<PERSON>im", "typeName", "bake", "o", "safePropBackslashRe", "key", "safePropQuoteRe", "camelCaseRe", "toUpperCase", "decorateEnumIndex", "a", "decorateRoot", "enumerable", "dst", "setProp", "zero", "zzEncode", "zeroHash", "from", "fromString", "toLong", "fromHash", "hash", "toHash", "mask", "part0", "part1", "part2", "src", "newError", "CustomError", "captureStackTrace", "writable", "configurable", "pool", "versions", "node", "window", "isFinite", "isset", "isSet", "utf8Write", "_B<PERSON>er_from", "_Buffer_allocUnsafe", "sizeOrArray", "dcodeIO", "key2Re", "key32Re", "key64Re", "longToHash", "longFromHash", "fromBits", "ProtocolError", "fieldMap", "longs", "enums", "encoding", "allocUnsafe", "seenFirstField", "oneofProp", "invalid", "genVerifyKey", "genVerifyValue", "messageName", "Op", "noop", "State", "tail", "states", "writeByte", "VarintOp", "writeVarint64", "writeFixed32", "_push", "writeBytes", "reset", "BufferWriter_", "writeStringBuffer", "writeBytesBuffer", "copy", "byteLength"], "mappings": ";;;;;;CAAA,SAAAA,kBAAA,SAAAC,EAAAC,EAAAC,GAcA,IAAAC,EAPA,SAAAC,EAAAC,GACA,IAAAC,EAAAL,EAAAI,GAGA,OAFAC,GACAN,EAAAK,GAAA,GAAAE,KAAAD,EAAAL,EAAAI,GAAA,CAAAG,QAAA,IAAAJ,EAAAE,EAAAA,EAAAE,SACAF,EAAAE,QAGAJ,CAAAF,EAAA,IAGAC,EAAAM,KAAAC,OAAAP,SAAAA,EAGA,mBAAAQ,QAAAA,OAAAC,KACAD,OAAA,CAAA,QAAA,SAAAE,GAKA,OAJAA,GAAAA,EAAAC,SACAX,EAAAM,KAAAI,KAAAA,EACAV,EAAAY,aAEAZ,IAIA,iBAAAa,QAAAA,QAAAA,OAAAR,UACAQ,OAAAR,QAAAL,GA/BA,CAiCA,CAAAc,EAAA,CAAA,SAAAC,EAAAF,EAAAR,GChCAQ,EAAAR,QAmBA,SAAAW,EAAAC,GACA,IAAAC,EAAAC,MAAAC,UAAAC,OAAA,GACAC,EAAA,EACAC,EAAA,EACAC,GAAA,EACA,KAAAD,EAAAH,UAAAC,QACAH,EAAAI,KAAAF,UAAAG,KACA,OAAA,IAAAE,QAAA,SAAAC,EAAAC,GACAT,EAAAI,GAAA,SAAAM,GACA,GAAAJ,EAEA,GADAA,GAAA,EACAI,EACAD,EAAAC,OACA,CAGA,IAFA,IAAAV,EAAAC,MAAAC,UAAAC,OAAA,GACAC,EAAA,EACAA,EAAAJ,EAAAG,QACAH,EAAAI,KAAAF,UAAAE,GACAI,EAAAG,MAAA,KAAAX,KAIA,IACAF,EAAAa,MAAAZ,GAAA,KAAAC,GACA,MAAAU,GACAJ,IACAA,GAAA,EACAG,EAAAC,S,uBCjCAE,EAAAT,OAAA,SAAAU,GACA,IAAAC,EAAAD,EAAAV,OACA,IAAAW,EACA,OAAA,EAEA,IADA,IAAAC,EAAA,EACA,IAAAD,EAAA,GAAA,MAAAD,EAAAA,EAAAC,IAAAD,OACAE,EACA,OAAAC,KAAAC,KAAA,EAAAJ,EAAAV,QAAA,EAAAY,GAUA,IAxBA,IAkBAG,EAAAjB,MAAA,IAGAkB,EAAAlB,MAAA,KAGAmB,EAAA,EAAAA,EAAA,IACAD,EAAAD,EAAAE,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,EAAAA,EAAA,GAAA,IAAAA,IASAR,EAAAS,OAAA,SAAAC,EAAAC,EAAAC,GAMA,IALA,IAIAC,EAJAC,EAAA,KACAC,EAAA,GACAP,EAAA,EACAQ,EAAA,EAEAL,EAAAC,GAAA,CACA,IAAAK,EAAAP,EAAAC,KACA,OAAAK,GACA,KAAA,EACAD,EAAAP,KAAAF,EAAAW,GAAA,GACAJ,GAAA,EAAAI,IAAA,EACAD,EAAA,EACA,MACA,KAAA,EACAD,EAAAP,KAAAF,EAAAO,EAAAI,GAAA,GACAJ,GAAA,GAAAI,IAAA,EACAD,EAAA,EACA,MACA,KAAA,EACAD,EAAAP,KAAAF,EAAAO,EAAAI,GAAA,GACAF,EAAAP,KAAAF,EAAA,GAAAW,GACAD,EAAA,EAGA,KAAAR,KACAM,EAAAA,GAAA,IAAAI,KAAAC,OAAAC,aAAArB,MAAAoB,OAAAJ,IACAP,EAAA,GASA,OANAQ,IACAD,EAAAP,KAAAF,EAAAO,GACAE,EAAAP,KAAA,GACA,IAAAQ,IACAD,EAAAP,KAAA,KAEAM,GACAN,GACAM,EAAAI,KAAAC,OAAAC,aAAArB,MAAAoB,OAAAJ,EAAAM,MAAA,EAAAb,KACAM,EAAAQ,KAAA,KAEAH,OAAAC,aAAArB,MAAAoB,OAAAJ,EAAAM,MAAA,EAAAb,KAGA,IAAAe,EAAA,mBAUAvB,EAAAwB,OAAA,SAAAvB,EAAAS,EAAAlB,GAIA,IAHA,IAEAqB,EAFAF,EAAAnB,EACAwB,EAAA,EAEAR,EAAA,EAAAA,EAAAP,EAAAV,QAAA,CACA,IAAAkC,EAAAxB,EAAAyB,WAAAlB,KACA,GAAA,IAAAiB,GAAA,EAAAT,EACA,MACA,IAAAS,EAAAlB,EAAAkB,MAAA3D,GACA,MAAA6D,MAAAJ,GACA,OAAAP,GACA,KAAA,EACAH,EAAAY,EACAT,EAAA,EACA,MACA,KAAA,EACAN,EAAAlB,KAAAqB,GAAA,GAAA,GAAAY,IAAA,EACAZ,EAAAY,EACAT,EAAA,EACA,MACA,KAAA,EACAN,EAAAlB,MAAA,GAAAqB,IAAA,GAAA,GAAAY,IAAA,EACAZ,EAAAY,EACAT,EAAA,EACA,MACA,KAAA,EACAN,EAAAlB,MAAA,EAAAqB,IAAA,EAAAY,EACAT,EAAA,GAIA,GAAA,IAAAA,EACA,MAAAW,MAAAJ,GACA,OAAA/B,EAAAmB,GAQAX,EAAA4B,KAAA,SAAA3B,GACA,MAAA,mEAAA2B,KAAA3B,K,uBC/HA,SAAA4B,EAAAC,EAAAC,GAGA,iBAAAD,IACAC,EAAAD,EACAA,EAAAhE,IAGA,IAAAkE,EAAA,GAYA,SAAAC,EAAAC,GAIA,GAAA,iBAAAA,EAAA,CACA,IAAAC,EAAAC,IAIA,GAHAP,EAAAQ,SACAC,QAAAC,IAAA,YAAAJ,GACAA,EAAA,UAAAA,EACAD,EAAA,CAKA,IAJA,IAAAM,EAAAC,OAAAC,KAAAR,GACAS,EAAAtD,MAAAmD,EAAAjD,OAAA,GACAqD,EAAAvD,MAAAmD,EAAAjD,QACAsD,EAAA,EACAA,EAAAL,EAAAjD,QACAoD,EAAAE,GAAAL,EAAAK,GACAD,EAAAC,GAAAX,EAAAM,EAAAK,MAGA,OADAF,EAAAE,GAAAV,EACAW,SAAA/C,MAAA,KAAA4C,GAAA5C,MAAA,KAAA6C,GAEA,OAAAE,SAAAX,EAAAW,GAMA,IAFA,IAAAC,EAAA1D,MAAAC,UAAAC,OAAA,GACAyD,EAAA,EACAA,EAAAD,EAAAxD,QACAwD,EAAAC,GAAA1D,YAAA0D,GAYA,GAXAA,EAAA,EACAd,EAAAA,EAAAe,QAAA,eAAA,SAAAC,EAAAC,GACA,IAAAC,EAAAL,EAAAC,KACA,OAAAG,GACA,IAAA,IAAA,IAAA,IAAA,MAAAhC,MAAAkC,GAAAD,GACA,IAAA,IAAA,MAAAjC,GAAAf,KAAAkD,MAAAF,GACA,IAAA,IAAA,OAAAG,KAAAC,UAAAJ,GACA,IAAA,IAAA,MAAAjC,GAAAiC,EAEA,MAAA,MAEAJ,IAAAD,EAAAxD,OACA,MAAAoC,MAAA,4BAEA,OADAK,EAAAd,KAAAgB,GACAD,EAGA,SAAAG,EAAAqB,GACA,MAAA,aAAAA,GAAA1B,GAAA,IAAA,KAAAD,GAAAA,EAAAR,KAAA,MAAA,IAAA,SAAAU,EAAAV,KAAA,QAAA,MAIA,OADAW,EAAAG,SAAAA,EACAH,GAhFAlD,EAAAR,QAAAsD,GAiGAQ,SAAA,G,uBCzFA,SAAAqB,IAOAC,KAAAC,EAAA,IAfA7E,EAAAR,QAAAmF,GAyBAG,UAAAC,GAAA,SAAAC,EAAA7E,EAAAC,GAKA,OAJAwE,KAAAC,EAAAG,KAAAJ,KAAAC,EAAAG,GAAA,KAAA7C,KAAA,CACAhC,GAAAA,EACAC,IAAAA,GAAAwE,OAEAA,MASAD,EAAAG,UAAAG,IAAA,SAAAD,EAAA7E,GACA,GAAA6E,IAAAjG,GACA6F,KAAAC,EAAA,QAEA,GAAA1E,IAAApB,GACA6F,KAAAC,EAAAG,GAAA,QAGA,IADA,IAAAE,EAAAN,KAAAC,EAAAG,GACAvD,EAAA,EAAAA,EAAAyD,EAAA1E,QACA0E,EAAAzD,GAAAtB,KAAAA,EACA+E,EAAAC,OAAA1D,EAAA,KAEAA,EAGA,OAAAmD,MASAD,EAAAG,UAAAM,KAAA,SAAAJ,GACA,IAAAE,EAAAN,KAAAC,EAAAG,GACA,GAAAE,EAAA,CAGA,IAFA,IAAAG,EAAA,GACA5D,EAAA,EACAA,EAAAlB,UAAAC,QACA6E,EAAAlD,KAAA5B,UAAAkB,MACA,IAAAA,EAAA,EAAAA,EAAAyD,EAAA1E,QACA0E,EAAAzD,GAAAtB,GAAAa,MAAAkE,EAAAzD,KAAArB,IAAAiF,GAEA,OAAAT,O,uBCzEA5E,EAAAR,QAAA8F,EAEA,IAAAC,EAAArF,EAAA,GAGAsF,EAFAtF,EAAA,EAEAuF,CAAA,MA2BA,SAAAH,EAAAI,EAAAC,EAAAC,GAOA,OAJAD,EAFA,mBAAAA,GACAC,EAAAD,EACA,IACAA,GACA,GAEAC,GAIAD,EAAAE,KAAAL,GAAAA,EAAAM,SACAN,EAAAM,SAAAJ,EAAA,SAAA3E,EAAAgF,GACA,OAAAhF,GAAA,oBAAAiF,eACAV,EAAAO,IAAAH,EAAAC,EAAAC,GACA7E,EACA6E,EAAA7E,GACA6E,EAAA,KAAAD,EAAAM,OAAAF,EAAAA,EAAA1C,SAAA,WAIAiC,EAAAO,IAAAH,EAAAC,EAAAC,GAbAL,EAAAD,EAAAV,KAAAc,EAAAC,GAqCAL,EAAAO,IAAA,SAAAH,EAAAC,EAAAC,GACA,IAAAC,EAAA,IAAAG,eACAH,EAAAK,mBAAA,WAEA,GAAA,IAAAL,EAAAM,WACA,OAAApH,GAKA,GAAA,IAAA8G,EAAAO,QAAA,MAAAP,EAAAO,OACA,OAAAR,EAAAhD,MAAA,UAAAiD,EAAAO,SAIA,GAAAT,EAAAM,OAAA,CAEA,KAAAtE,EADAkE,EAAAQ,UAGA,IAAA,IADA1E,EAAA,GACAF,EAAA,EAAAA,EAAAoE,EAAAS,aAAA9F,SAAAiB,EACAE,EAAAQ,KAAA,IAAA0D,EAAAS,aAAA3D,WAAAlB,IAEA,OAAAmE,EAAA,KAAA,oBAAAW,WAAA,IAAAA,WAAA5E,GAAAA,GAEA,OAAAiE,EAAA,KAAAC,EAAAS,eAGAX,EAAAM,SAEA,qBAAAJ,GACAA,EAAAW,iBAAA,sCACAX,EAAAY,aAAA,eAGAZ,EAAAa,KAAA,MAAAhB,GACAG,EAAAc,S,8BC1BA,SAAAC,EAAApH,GAsDA,SAAAqH,EAAAC,EAAAC,EAAAC,EAAAC,GACA,IAAAC,EAAAH,EAAA,EAAA,EAAA,EAIAD,EADA,KADAC,EADAG,GACAH,EACAA,GACA,EAAA,EAAAA,EAAA,EAAA,WACAI,MAAAJ,GACA,WACA,qBAAAA,GACAG,GAAA,GAAA,cAAA,EACAH,EAAA,uBACAG,GAAA,GAAA7F,KAAA+F,MAAAL,EAAA,yBAAA,GAIAG,GAAA,GAAA,KAFAG,EAAAhG,KAAAkD,MAAAlD,KAAAmC,IAAAuD,GAAA1F,KAAAiG,OAEA,GADA,QAAAjG,KAAA+F,MAAAL,EAAA1F,KAAAkG,IAAA,GAAAF,GAAA,YACA,EAVAL,EAAAC,GAiBA,SAAAO,EAAAC,EAAAT,EAAAC,GACAS,EAAAD,EAAAT,EAAAC,GACAC,EAAA,GAAAQ,GAAA,IAAA,EACAL,EAAAK,IAAA,GAAA,IACAC,GAAA,QACA,OAAA,KAAAN,EACAM,EACAC,IACAC,EAAAA,EAAAX,EACA,GAAAG,EACA,qBAAAH,EAAAS,EACAT,EAAA7F,KAAAkG,IAAA,EAAAF,EAAA,MAAA,QAAAM,GA9EA,SAAAG,EAAAf,EAAAC,EAAAC,GACAc,EAAA,GAAAhB,EACAC,EAAAC,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GAGA,SAAAC,EAAAlB,EAAAC,EAAAC,GACAc,EAAA,GAAAhB,EACAC,EAAAC,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GAQA,SAAAE,EAAAlB,EAAAC,GAKA,OAJAe,EAAA,GAAAhB,EAAAC,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAc,EAAA,GAGA,SAAAI,EAAAnB,EAAAC,GAKA,OAJAe,EAAA,GAAAhB,EAAAC,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAc,EAAA,GAxCA,IAEAA,EACAC,EA4FAI,EACAJ,EACAK,EA+DA,SAAAC,EAAAxB,EAAAyB,EAAAC,EAAAzB,EAAAC,EAAAC,GACA,IAaAU,EAbAT,EAAAH,EAAA,EAAA,EAAA,EAGA,KADAA,EADAG,GACAH,EACAA,IACAD,EAAA,EAAAE,EAAAC,EAAAsB,GACAzB,EAAA,EAAA,EAAAC,EAAA,EAAA,WAAAC,EAAAC,EAAAuB,IACArB,MAAAJ,IACAD,EAAA,EAAAE,EAAAC,EAAAsB,GACAzB,EAAA,WAAAE,EAAAC,EAAAuB,IACA,sBAAAzB,GACAD,EAAA,EAAAE,EAAAC,EAAAsB,GACAzB,GAAAI,GAAA,GAAA,cAAA,EAAAF,EAAAC,EAAAuB,IAGAzB,EAAA,wBAEAD,GADAa,EAAAZ,EAAA,UACA,EAAAC,EAAAC,EAAAsB,GACAzB,GAAAI,GAAA,GAAAS,EAAA,cAAA,EAAAX,EAAAC,EAAAuB,KAMA1B,EAAA,kBADAa,EAAAZ,EAAA1F,KAAAkG,IAAA,IADAF,EADA,QADAA,EAAAhG,KAAAkD,MAAAlD,KAAAmC,IAAAuD,GAAA1F,KAAAiG,MAEA,KACAD,OACA,EAAAL,EAAAC,EAAAsB,GACAzB,GAAAI,GAAA,GAAAG,EAAA,MAAA,GAAA,QAAAM,EAAA,WAAA,EAAAX,EAAAC,EAAAuB,IAQA,SAAAC,EAAAhB,EAAAc,EAAAC,EAAAxB,EAAAC,GACAyB,EAAAjB,EAAAT,EAAAC,EAAAsB,GACAI,EAAAlB,EAAAT,EAAAC,EAAAuB,GACAtB,EAAA,GAAAyB,GAAA,IAAA,EACAtB,EAAAsB,IAAA,GAAA,KACAhB,EAAA,YAAA,QAAAgB,GAAAD,EACA,OAAA,MAAArB,EACAM,EACAC,IACAC,EAAAA,EAAAX,EACA,GAAAG,EACA,OAAAH,EAAAS,EACAT,EAAA7F,KAAAkG,IAAA,EAAAF,EAAA,OAAAM,EAAA,kBA1GA,SAAAiB,EAAA7B,EAAAC,EAAAC,GACAmB,EAAA,GAAArB,EACAC,EAAAC,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GAGA,SAAAa,EAAA9B,EAAAC,EAAAC,GACAmB,EAAA,GAAArB,EACAC,EAAAC,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GAQA,SAAAc,EAAA9B,EAAAC,GASA,OARAe,EAAA,GAAAhB,EAAAC,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAmB,EAAA,GAGA,SAAAW,EAAA/B,EAAAC,GASA,OARAe,EAAA,GAAAhB,EAAAC,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAmB,EAAA,GAgEA,MArNA,oBAAAY,cAEAjB,EAAA,IAAAiB,aAAA,EAAA,IACAhB,EAAA,IAAAzB,WAAAwB,EAAApG,QACA0G,EAAA,MAAAL,EAAA,GAmBAxI,EAAAyJ,aAAAZ,EAAAP,EAAAG,EAEAzI,EAAA0J,aAAAb,EAAAJ,EAAAH,EAmBAtI,EAAA2J,YAAAd,EAAAH,EAAAC,EAEA3I,EAAA4J,YAAAf,EAAAF,EAAAD,IAwBA1I,EAAAyJ,aAAApC,EAAAwC,KAAA,KAAAC,GACA9J,EAAA0J,aAAArC,EAAAwC,KAAA,KAAAE,GAgBA/J,EAAA2J,YAAA3B,EAAA6B,KAAA,KAAAG,GACAhK,EAAA4J,YAAA5B,EAAA6B,KAAA,KAAAI,IAKA,oBAAAC,cAEAtB,EAAA,IAAAsB,aAAA,EAAA,IACA1B,EAAA,IAAAzB,WAAA6B,EAAAzG,QACA0G,EAAA,MAAAL,EAAA,GA2BAxI,EAAAmK,cAAAtB,EAAAO,EAAAC,EAEArJ,EAAAoK,cAAAvB,EAAAQ,EAAAD,EA2BApJ,EAAAqK,aAAAxB,EAAAS,EAAAC,EAEAvJ,EAAAsK,aAAAzB,EAAAU,EAAAD,IAmCAtJ,EAAAmK,cAAArB,EAAAe,KAAA,KAAAC,EAAA,EAAA,GACA9J,EAAAoK,cAAAtB,EAAAe,KAAA,KAAAE,EAAA,EAAA,GAiBA/J,EAAAqK,aAAApB,EAAAY,KAAA,KAAAG,EAAA,EAAA,GACAhK,EAAAsK,aAAArB,EAAAY,KAAA,KAAAI,EAAA,EAAA,IAIAjK,EAKA,SAAA8J,EAAAvC,EAAAC,EAAAC,GACAD,EAAAC,GAAA,IAAAF,EACAC,EAAAC,EAAA,GAAAF,IAAA,EAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,GAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,GAGA,SAAAwC,EAAAxC,EAAAC,EAAAC,GACAD,EAAAC,GAAAF,IAAA,GACAC,EAAAC,EAAA,GAAAF,IAAA,GAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,EAAA,IACAC,EAAAC,EAAA,GAAA,IAAAF,EAGA,SAAAyC,EAAAxC,EAAAC,GACA,OAAAD,EAAAC,GACAD,EAAAC,EAAA,IAAA,EACAD,EAAAC,EAAA,IAAA,GACAD,EAAAC,EAAA,IAAA,MAAA,EAGA,SAAAwC,EAAAzC,EAAAC,GACA,OAAAD,EAAAC,IAAA,GACAD,EAAAC,EAAA,IAAA,GACAD,EAAAC,EAAA,IAAA,EACAD,EAAAC,EAAA,MAAA,EA3UAjH,EAAAR,QAAAoH,EAAAA,I,uBCOA,SAAAnB,EAAAsE,GACA,IACA,IAAAC,EAAAC,KAAA,UAAAA,CAAAF,GACA,GAAAC,IAAAA,EAAAxJ,QAAAkD,OAAAC,KAAAqG,GAAAxJ,QACA,OAAAwJ,EACA,MAAAE,IACA,OAAA,KAdAlK,EAAAR,QAAAiG,G,uBCMA,IAEA0E,EAMAC,EAAAD,WAAA,SAAAC,GACA,MAAA,eAAAvH,KAAAuH,IAGAC,EAMAD,EAAAC,UAAA,SAAAD,GAGA,IAAArI,GAFAqI,EAAAA,EAAAlG,QAAA,MAAA,KACAA,QAAA,UAAA,MACAoG,MAAA,KACAC,EAAAJ,EAAAC,GACAI,EAAA,GACAD,IACAC,EAAAzI,EAAA0I,QAAA,KACA,IAAA,IAAAhJ,EAAA,EAAAA,EAAAM,EAAAvB,QACA,OAAAuB,EAAAN,GACA,EAAAA,GAAA,OAAAM,EAAAN,EAAA,GACAM,EAAAoD,SAAA1D,EAAA,GACA8I,EACAxI,EAAAoD,OAAA1D,EAAA,KAEAA,EACA,MAAAM,EAAAN,GACAM,EAAAoD,OAAA1D,EAAA,KAEAA,EAEA,OAAA+I,EAAAzI,EAAAQ,KAAA,MAUA6H,EAAAvJ,QAAA,SAAA6J,EAAAC,EAAAC,GAGA,OAFAA,IACAD,EAAAN,EAAAM,KACAR,EAAAQ,KAIAD,GADAA,EADAE,EAEAF,EADAL,EAAAK,IACAxG,QAAA,iBAAA,KAAA1D,OAAA6J,EAAAK,EAAA,IAAAC,GAHAA,I,uBC3DA3K,EAAAR,QA6BA,SAAAqL,EAAAvI,EAAAwI,GACA,IAAAC,EAAAD,GAAA,KACAE,EAAAD,IAAA,EACAE,EAAA,KACAxK,EAAAsK,EACA,OAAA,SAAAD,GACA,GAAAA,EAAA,GAAAE,EAAAF,EACA,OAAAD,EAAAC,GACAC,EAAAtK,EAAAqK,IACAG,EAAAJ,EAAAE,GACAtK,EAAA,GAEAuG,EAAA1E,EAAA/C,KAAA0L,EAAAxK,EAAAA,GAAAqK,GAGA,OAFA,EAAArK,IACAA,EAAA,GAAA,EAAAA,IACAuG,K,wBC/BAkE,EAAA1K,OAAA,SAAAU,GAGA,IAFA,IACAwB,EADAyI,EAAA,EAEA1J,EAAA,EAAAA,EAAAP,EAAAV,SAAAiB,GACAiB,EAAAxB,EAAAyB,WAAAlB,IACA,IACA0J,GAAA,EACAzI,EAAA,KACAyI,GAAA,EACA,QAAA,MAAAzI,IAAA,QAAA,MAAAxB,EAAAyB,WAAAlB,EAAA,OACAA,EACA0J,GAAA,GAEAA,GAAA,EAEA,OAAAA,GAUAD,EAAAE,KAAA,SAAAzJ,EAAAC,EAAAC,GAEA,GADAA,EAAAD,EACA,EACA,MAAA,GAKA,IAJA,IAGAE,EAHAC,EAAA,KACAC,EAAA,GACAP,EAAA,EAEAG,EAAAC,IACAC,EAAAH,EAAAC,MACA,IACAI,EAAAP,KAAAK,EACA,IAAAA,GAAAA,EAAA,IACAE,EAAAP,MAAA,GAAAK,IAAA,EAAA,GAAAH,EAAAC,KACA,IAAAE,GAAAA,EAAA,KACAA,IAAA,EAAAA,IAAA,IAAA,GAAAH,EAAAC,OAAA,IAAA,GAAAD,EAAAC,OAAA,EAAA,GAAAD,EAAAC,MAAA,MACAI,EAAAP,KAAA,OAAAK,GAAA,IACAE,EAAAP,KAAA,OAAA,KAAAK,IAEAE,EAAAP,MAAA,GAAAK,IAAA,IAAA,GAAAH,EAAAC,OAAA,EAAA,GAAAD,EAAAC,KACA,KAAAH,KACAM,EAAAA,GAAA,IAAAI,KAAAC,OAAAC,aAAArB,MAAAoB,OAAAJ,IACAP,EAAA,GAGA,OAAAM,GACAN,GACAM,EAAAI,KAAAC,OAAAC,aAAArB,MAAAoB,OAAAJ,EAAAM,MAAA,EAAAb,KACAM,EAAAQ,KAAA,KAEAH,OAAAC,aAAArB,MAAAoB,OAAAJ,EAAAM,MAAA,EAAAb,KAUAyJ,EAAAG,MAAA,SAAAnK,EAAAS,EAAAlB,GAIA,IAHA,IACA6K,EACAC,EAFA3J,EAAAnB,EAGAgB,EAAA,EAAAA,EAAAP,EAAAV,SAAAiB,GACA6J,EAAApK,EAAAyB,WAAAlB,IACA,IACAE,EAAAlB,KAAA6K,GACAA,EAAA,KACA3J,EAAAlB,KAAA6K,GAAA,EAAA,KAEA,QAAA,MAAAA,IAAA,QAAA,OAAAC,EAAArK,EAAAyB,WAAAlB,EAAA,QAEAA,EACAE,EAAAlB,MAFA6K,EAAA,QAAA,KAAAA,IAAA,KAAA,KAAAC,KAEA,GAAA,IACA5J,EAAAlB,KAAA6K,GAAA,GAAA,GAAA,KAIA3J,EAAAlB,KAAA6K,GAAA,GAAA,IAHA3J,EAAAlB,KAAA6K,GAAA,EAAA,GAAA,KANA3J,EAAAlB,KAAA,GAAA6K,EAAA,KAcA,OAAA7K,EAAAmB,I,wBCtGA5B,EAAAR,QAAAgM,EAEA,IAAAC,EAAA,QAsBA,SAAAD,EAAAnM,EAAAqM,GACAD,EAAA5I,KAAAxD,KACAA,EAAA,mBAAAA,EAAA,SACAqM,EAAA,CAAAC,OAAA,CAAAC,OAAA,CAAAD,OAAA,CAAAxM,SAAA,CAAAwM,OAAAD,QAEAF,EAAAnM,GAAAqM,EAYAF,EAAA,MAAA,CAUAK,IAAA,CACAC,OAAA,CACAC,SAAA,CACAC,KAAA,SACAC,GAAA,GAEA5H,MAAA,CACA2H,KAAA,QACAC,GAAA,OAQAT,EAAA,WAAA,CAUAU,SAAAC,EAAA,CACAL,OAAA,CACAM,QAAA,CACAJ,KAAA,QACAC,GAAA,GAEAI,MAAA,CACAL,KAAA,QACAC,GAAA,OAMAT,EAAA,YAAA,CAUAc,UAAAH,IAGAX,EAAA,QAAA,CAOAe,MAAA,CACAT,OAAA,MAIAN,EAAA,SAAA,CASAgB,OAAA,CACAV,OAAA,CACAA,OAAA,CACAW,QAAA,SACAT,KAAA,QACAC,GAAA,KAkBAS,MAAA,CACAC,OAAA,CACAC,KAAA,CACAC,MAAA,CACA,YACA,cACA,cACA,YACA,cACA,eAIAf,OAAA,CACAgB,UAAA,CACAd,KAAA,YACAC,GAAA,GAEAc,YAAA,CACAf,KAAA,SACAC,GAAA,GAEAe,YAAA,CACAhB,KAAA,SACAC,GAAA,GAEAgB,UAAA,CACAjB,KAAA,OACAC,GAAA,GAEAiB,YAAA,CACAlB,KAAA,SACAC,GAAA,GAEAkB,UAAA,CACAnB,KAAA,YACAC,GAAA,KAKAmB,UAAA,CACAC,OAAA,CACAC,WAAA,IAWAC,UAAA,CACAzB,OAAA,CACAuB,OAAA,CACAG,KAAA,WACAxB,KAAA,QACAC,GAAA,OAMAT,EAAA,WAAA,CASAiC,YAAA,CACA3B,OAAA,CACAzH,MAAA,CACA2H,KAAA,SACAC,GAAA,KAYAyB,WAAA,CACA5B,OAAA,CACAzH,MAAA,CACA2H,KAAA,QACAC,GAAA,KAYA0B,WAAA,CACA7B,OAAA,CACAzH,MAAA,CACA2H,KAAA,QACAC,GAAA,KAYA2B,YAAA,CACA9B,OAAA,CACAzH,MAAA,CACA2H,KAAA,SACAC,GAAA,KAYA4B,WAAA,CACA/B,OAAA,CACAzH,MAAA,CACA2H,KAAA,QACAC,GAAA,KAYA6B,YAAA,CACAhC,OAAA,CACAzH,MAAA,CACA2H,KAAA,SACAC,GAAA,KAYA8B,UAAA,CACAjC,OAAA,CACAzH,MAAA,CACA2H,KAAA,OACAC,GAAA,KAYA+B,YAAA,CACAlC,OAAA,CACAzH,MAAA,CACA2H,KAAA,SACAC,GAAA,KAYAgC,WAAA,CACAnC,OAAA,CACAzH,MAAA,CACA2H,KAAA,QACAC,GAAA,OAMAT,EAAA,aAAA,CASA0C,UAAA,CACApC,OAAA,CACAqC,MAAA,CACAX,KAAA,WACAxB,KAAA,SACAC,GAAA,OAqBAT,EAAA4C,IAAA,SAAAC,GACA,OAAA7C,EAAA6C,IAAA,O,wBCxYA,IAEAC,EAAApO,EAAA,IACAT,EAAAS,EAAA,IAWA,SAAAqO,EAAAC,EAAAC,EAAAC,EAAAC,GAEA,GAAAF,EAAAG,aACA,GAAAH,EAAAG,wBAAAN,EAAA,CAAAE,EACA,eAAAG,GACA,IAAA,IAAAtB,EAAAoB,EAAAG,aAAAvB,OAAA1J,EAAAD,OAAAC,KAAA0J,GAAA5L,EAAA,EAAAA,EAAAkC,EAAAnD,SAAAiB,EAEA4L,EAAA1J,EAAAlC,MAAAgN,EAAAI,cAAAL,EACA,WADAA,CAEA,4CAAAG,EAAAA,EAAAA,GACAF,EAAAK,UAAAN,EAEA,UAEAA,EACA,UAAA7K,EAAAlC,GADA+M,CAEA,WAAAnB,EAAA1J,EAAAlC,IAFA+M,CAGA,SAAAG,EAAAtB,EAAA1J,EAAAlC,IAHA+M,CAIA,SACAA,EACA,UACAA,EACA,4BAAAG,EADAH,CAEA,sBAAAC,EAAAM,SAAA,oBAFAP,CAGA,gCAAAG,EAAAD,EAAAC,OACA,CACA,IAAAK,GAAA,EACA,OAAAP,EAAAzC,MACA,IAAA,SACA,IAAA,QAAAwC,EACA,kBAAAG,EAAAA,GACA,MACA,IAAA,SACA,IAAA,UAAAH,EACA,cAAAG,EAAAA,GACA,MACA,IAAA,QACA,IAAA,SACA,IAAA,WAAAH,EACA,YAAAG,EAAAA,GACA,MACA,IAAA,SACAK,GAAA,EAEA,IAAA,QACA,IAAA,SACA,IAAA,UACA,IAAA,WAAAR,EACA,gBADAA,CAEA,6CAAAG,EAAAA,EAAAK,EAFAR,CAGA,iCAAAG,EAHAH,CAIA,uBAAAG,EAAAA,EAJAH,CAKA,iCAAAG,EALAH,CAMA,UAAAG,EAAAA,EANAH,CAOA,iCAAAG,EAPAH,CAQA,+DAAAG,EAAAA,EAAAA,EAAAK,EAAA,OAAA,IACA,MACA,IAAA,QAAAR,EACA,4BAAAG,EADAH,CAEA,wEAAAG,EAAAA,EAAAA,EAFAH,CAGA,2BAAAG,EAHAH,CAIA,UAAAG,EAAAA,GACA,MACA,IAAA,SAAAH,EACA,kBAAAG,EAAAA,GACA,MACA,IAAA,OAAAH,EACA,mBAAAG,EAAAA,IAOA,OAAAH,EAmEA,SAAAS,EAAAT,EAAAC,EAAAC,EAAAC,GAEA,GAAAF,EAAAG,aACAH,EAAAG,wBAAAN,EAAAE,EACA,yFAAAG,EAAAD,EAAAC,EAAAA,EAAAD,EAAAC,EAAAA,GACAH,EACA,gCAAAG,EAAAD,EAAAC,OACA,CACA,IAAAK,GAAA,EACA,OAAAP,EAAAzC,MACA,IAAA,SACA,IAAA,QAAAwC,EACA,6CAAAG,EAAAA,EAAAA,EAAAA,GACA,MACA,IAAA,SACAK,GAAA,EAEA,IAAA,QACA,IAAA,SACA,IAAA,UACA,IAAA,WAAAR,EACA,4BAAAG,EADAH,CAEA,uCAAAG,EAAAA,EAAAA,EAFAH,CAGA,OAHAA,CAIA,4IAAAG,EAAAA,EAAAA,EAAAA,EAAAK,EAAA,OAAA,GAAAL,GACA,MACA,IAAA,QAAAH,EACA,gHAAAG,EAAAA,EAAAA,EAAAA,EAAAA,GACA,MACA,QAAAH,EACA,UAAAG,EAAAA,IAIA,OAAAH,EA5FAU,EAAAC,WAAA,SAAAC,GAEA,IAAAtD,EAAAsD,EAAAC,YACAb,EAAA/O,EAAAqD,QAAA,CAAA,KAAAsM,EAAA/P,KAAA,cAAAI,CACA,6BADAA,CAEA,YACA,IAAAqM,EAAAtL,OAAA,OAAAgO,EACA,wBACAA,EACA,uBACA,IAAA,IAAA/M,EAAA,EAAAA,EAAAqK,EAAAtL,SAAAiB,EAAA,CACA,IAAAgN,EAAA3C,EAAArK,GAAAZ,UACA8N,EAAAlP,EAAA6P,SAAAb,EAAApP,MAGAoP,EAAAc,KAAAf,EACA,WAAAG,EADAH,CAEA,4BAAAG,EAFAH,CAGA,sBAAAC,EAAAM,SAAA,oBAHAP,CAIA,SAAAG,EAJAH,CAKA,oDAAAG,GACAJ,EAAAC,EAAAC,EAAAhN,EAAAkN,EAAA,UAAAJ,CACA,IADAA,CAEA,MAGAE,EAAAK,UAAAN,EACA,WAAAG,EADAH,CAEA,0BAAAG,EAFAH,CAGA,sBAAAC,EAAAM,SAAA,mBAHAP,CAIA,SAAAG,EAJAH,CAKA,iCAAAG,GACAJ,EAAAC,EAAAC,EAAAhN,EAAAkN,EAAA,MAAAJ,CACA,IADAA,CAEA,OAIAE,EAAAG,wBAAAN,GAAAE,EACA,iBAAAG,GACAJ,EAAAC,EAAAC,EAAAhN,EAAAkN,GACAF,EAAAG,wBAAAN,GAAAE,EACA,MAEA,OAAAA,EACA,aAwDAU,EAAAM,SAAA,SAAAJ,GAEA,IAAAtD,EAAAsD,EAAAC,YAAA/M,QAAAmN,KAAAhQ,EAAAiQ,mBACA,IAAA5D,EAAAtL,OACA,OAAAf,EAAAqD,SAAArD,CAAA,aAUA,IATA,IAAA+O,EAAA/O,EAAAqD,QAAA,CAAA,IAAA,KAAAsM,EAAA/P,KAAA,YAAAI,CACA,SADAA,CAEA,OAFAA,CAGA,YAEAkQ,EAAA,GACAC,EAAA,GACAC,EAAA,GACApO,EAAA,EACAA,EAAAqK,EAAAtL,SAAAiB,EACAqK,EAAArK,GAAAqO,SACAhE,EAAArK,GAAAZ,UAAAiO,SAAAa,EACA7D,EAAArK,GAAA8N,IAAAK,EACAC,GAAA1N,KAAA2J,EAAArK,IAEA,GAAAkO,EAAAnP,OAAA,CAEA,IAFAgO,EACA,6BACA/M,EAAA,EAAAA,EAAAkO,EAAAnP,SAAAiB,EAAA+M,EACA,SAAA/O,EAAA6P,SAAAK,EAAAlO,GAAApC,OACAmP,EACA,KAGA,GAAAoB,EAAApP,OAAA,CAEA,IAFAgO,EACA,8BACA/M,EAAA,EAAAA,EAAAmO,EAAApP,SAAAiB,EAAA+M,EACA,SAAA/O,EAAA6P,SAAAM,EAAAnO,GAAApC,OACAmP,EACA,KAGA,GAAAqB,EAAArP,OAAA,CAEA,IAFAgO,EACA,mBACA/M,EAAA,EAAAA,EAAAoO,EAAArP,SAAAiB,EAAA,CACA,IAWAsO,EAXAtB,EAAAoB,EAAApO,GACAkN,EAAAlP,EAAA6P,SAAAb,EAAApP,MACAoP,EAAAG,wBAAAN,EAAAE,EACA,6BAAAG,EAAAF,EAAAG,aAAAoB,WAAAvB,EAAAI,aAAAJ,EAAAI,aACAJ,EAAAwB,KAAAzB,EACA,iBADAA,CAEA,gCAAAC,EAAAI,YAAAqB,IAAAzB,EAAAI,YAAAsB,KAAA1B,EAAAI,YAAAuB,SAFA5B,CAGA,oEAAAG,EAHAH,CAIA,QAJAA,CAKA,6BAAAG,EAAAF,EAAAI,YAAAxL,WAAAoL,EAAAI,YAAAwB,YACA5B,EAAA6B,OACAP,EAAA,IAAAzP,MAAAwE,UAAAxC,MAAA/C,KAAAkP,EAAAI,aAAAtM,KAAA,KAAA,IACAiM,EACA,6BAAAG,EAAAvM,OAAAC,aAAArB,MAAAoB,OAAAqM,EAAAI,aADAL,CAEA,QAFAA,CAGA,SAAAG,EAAAoB,EAHAvB,CAIA,6CAAAG,EAAAA,EAJAH,CAKA,MACAA,EACA,SAAAG,EAAAF,EAAAI,aACAL,EACA,KAGA,IADA,IAAA+B,GAAA,EACA9O,EAAA,EAAAA,EAAAqK,EAAAtL,SAAAiB,EAAA,CACA,IAAAgN,EAAA3C,EAAArK,GACAf,EAAA0O,EAAAoB,EAAAC,QAAAhC,GACAE,EAAAlP,EAAA6P,SAAAb,EAAApP,MACAoP,EAAAc,KACAgB,IAAAA,GAAA,EAAA/B,EACA,YACAA,EACA,0CAAAG,EAAAA,EADAH,CAEA,SAAAG,EAFAH,CAGA,kCACAS,EAAAT,EAAAC,EAAA/N,EAAAiO,EAAA,WAAAM,CACA,MACAR,EAAAK,UAAAN,EACA,uBAAAG,EAAAA,EADAH,CAEA,SAAAG,EAFAH,CAGA,iCAAAG,GACAM,EAAAT,EAAAC,EAAA/N,EAAAiO,EAAA,MAAAM,CACA,OACAT,EACA,uCAAAG,EAAAF,EAAApP,MACA4P,EAAAT,EAAAC,EAAA/N,EAAAiO,GACAF,EAAAqB,QAAAtB,EACA,eADAA,CAEA,SAAA/O,EAAA6P,SAAAb,EAAAqB,OAAAzQ,MAAAoP,EAAApP,OAEAmP,EACA,KAEA,OAAAA,EACA,c,mCCvSAxO,EAAAR,QAeA,SAAA4P,GAEA,IAAAZ,EAAA/O,EAAAqD,QAAA,CAAA,IAAA,KAAAsM,EAAA/P,KAAA,UAAAI,CACA,6BADAA,CAEA,qBAFAA,CAGA,qDAAA2P,EAAAC,YAAAqB,OAAA,SAAAjC,GAAA,OAAAA,EAAAc,MAAA/O,OAAA,WAAA,IAHAf,CAIA,kBAJAA,CAKA,oBACA2P,EAAAuB,OAAAnC,EACA,gBADAA,CAEA,SACAA,EACA,kBAGA,IADA,IAAA/M,EAAA,EACAA,EAAA2N,EAAAC,YAAA7O,SAAAiB,EAAA,CACA,IAAAgN,EAAAW,EAAAoB,EAAA/O,GAAAZ,UACAmL,EAAAyC,EAAAG,wBAAAN,EAAA,QAAAG,EAAAzC,KACA4E,EAAA,IAAAnR,EAAA6P,SAAAb,EAAApP,MAAAmP,EACA,aAAAC,EAAAxC,IAGAwC,EAAAc,KAAAf,EACA,4BAAAoC,EADApC,CAEA,QAAAoC,EAFApC,CAGA,6BAEAqC,EAAAC,SAAArC,EAAAhC,WAAA1N,GAAAyP,EACA,OAAAqC,EAAAC,SAAArC,EAAAhC,UACA+B,EACA,UAEAqC,EAAAC,SAAA9E,KAAAjN,GAAAyP,EACA,WAAAqC,EAAAC,SAAA9E,IACAwC,EACA,cAEAA,EACA,mBADAA,CAEA,sBAFAA,CAGA,oBAHAA,CAIA,0BAAAC,EAAAhC,QAJA+B,CAKA,WAEAqC,EAAAE,MAAA/E,KAAAjN,GAAAyP,EACA,uCAAA/M,GACA+M,EACA,eAAAxC,GAEAwC,EACA,QADAA,CAEA,WAFAA,CAGA,qBAHAA,CAIA,QAJAA,CAKA,IALAA,CAMA,KAEAqC,EAAAZ,KAAAxB,EAAAhC,WAAA1N,GAAAyP,EACA,qDAAAoC,GACApC,EACA,cAAAoC,IAGAnC,EAAAK,UAAAN,EAEA,uBAAAoC,EAAAA,EAFApC,CAGA,QAAAoC,GAGAC,EAAAG,OAAAhF,KAAAjN,IAAAyP,EACA,iBADAA,CAEA,0BAFAA,CAGA,kBAHAA,CAIA,kBAAAoC,EAAA5E,EAJAwC,CAKA,SAGAqC,EAAAE,MAAA/E,KAAAjN,GAAAyP,EAAAC,EAAAG,aAAA+B,MACA,+BACA,0CAAAC,EAAAnP,GACA+M,EACA,kBAAAoC,EAAA5E,IAGA6E,EAAAE,MAAA/E,KAAAjN,GAAAyP,EAAAC,EAAAG,aAAA+B,MACA,yBACA,oCAAAC,EAAAnP,GACA+M,EACA,YAAAoC,EAAA5E,GACAwC,EACA,QADAA,CAEA,KAWA,IATAA,EACA,WADAA,CAEA,kBAFAA,CAGA,QAHAA,CAKA,IALAA,CAMA,KAGA/M,EAAA,EAAAA,EAAA2N,EAAAoB,EAAAhQ,SAAAiB,EAAA,CACA,IAAAwP,EAAA7B,EAAAoB,EAAA/O,GACAwP,EAAAC,UAAA1C,EACA,4BAAAyC,EAAA5R,KADAmP,CAEA,4CAlHA,qBAkHAyC,EAlHA5R,KAAA,KAqHA,OAAAmP,EACA,aA3HA,IAAAF,EAAApO,EAAA,IACA2Q,EAAA3Q,EAAA,IACAT,EAAAS,EAAA,K,yCCJAF,EAAAR,QA0BA,SAAA4P,GAWA,IATA,IAIAwB,EAJApC,EAAA/O,EAAAqD,QAAA,CAAA,IAAA,KAAAsM,EAAA/P,KAAA,UAAAI,CACA,SADAA,CAEA,qBAKAqM,EAAAsD,EAAAC,YAAA/M,QAAAmN,KAAAhQ,EAAAiQ,mBAEAjO,EAAA,EAAAA,EAAAqK,EAAAtL,SAAAiB,EAAA,CACA,IAAAgN,EAAA3C,EAAArK,GAAAZ,UACAH,EAAA0O,EAAAoB,EAAAC,QAAAhC,GACAzC,EAAAyC,EAAAG,wBAAAN,EAAA,QAAAG,EAAAzC,KACAmF,EAAAN,EAAAE,MAAA/E,GACA4E,EAAA,IAAAnR,EAAA6P,SAAAb,EAAApP,MAGAoP,EAAAc,KACAf,EACA,kDAAAoC,EAAAnC,EAAApP,KADAmP,CAEA,mDAAAoC,EAFApC,CAGA,4CAAAC,EAAAxC,IAAA,EAAA,KAAA,EAAA,EAAA4E,EAAAO,OAAA3C,EAAAhC,SAAAgC,EAAAhC,SACA0E,IAAApS,GAAAyP,EACA,oEAAA9N,EAAAkQ,GACApC,EACA,qCAAA,GAAA2C,EAAAnF,EAAA4E,GACApC,EACA,IADAA,CAEA,MAGAC,EAAAK,UAAAN,EACA,2BAAAoC,EAAAA,GAGAnC,EAAAuC,QAAAH,EAAAG,OAAAhF,KAAAjN,GAAAyP,EAEA,uBAAAC,EAAAxC,IAAA,EAAA,KAAA,EAFAuC,CAGA,+BAAAoC,EAHApC,CAIA,cAAAxC,EAAA4E,EAJApC,CAKA,eAGAA,EAEA,+BAAAoC,GACAO,IAAApS,GACAsS,EAAA7C,EAAAC,EAAA/N,EAAAkQ,EAAA,OACApC,EACA,0BAAAC,EAAAxC,IAAA,EAAAkF,KAAA,EAAAnF,EAAA4E,IAEApC,EACA,OAIAC,EAAA6C,UAAA9C,EACA,iDAAAoC,EAAAnC,EAAApP,MAEA8R,IAAApS,GACAsS,EAAA7C,EAAAC,EAAA/N,EAAAkQ,GACApC,EACA,uBAAAC,EAAAxC,IAAA,EAAAkF,KAAA,EAAAnF,EAAA4E,IAKA,OAAApC,EACA,aA9FA,IAAAF,EAAApO,EAAA,IACA2Q,EAAA3Q,EAAA,IACAT,EAAAS,EAAA,IAWA,SAAAmR,EAAA7C,EAAAC,EAAAC,EAAAkC,GACAnC,EAAAG,aAAA+B,MACAnC,EAAA,+CAAAE,EAAAkC,GAAAnC,EAAAxC,IAAA,EAAA,KAAA,GAAAwC,EAAAxC,IAAA,EAAA,KAAA,GACAuC,EAAA,oDAAAE,EAAAkC,GAAAnC,EAAAxC,IAAA,EAAA,KAAA,K,yCClBAjM,EAAAR,QAAA8O,EAGA,IAAAiD,EAAArR,EAAA,IAGAsR,KAFAlD,EAAAxJ,UAAApB,OAAA+N,OAAAF,EAAAzM,YAAA4M,YAAApD,GAAAqD,UAAA,OAEAzR,EAAA,KACAT,EAAAS,EAAA,IAcA,SAAAoO,EAAAjP,EAAAgO,EAAA1H,EAAAiM,EAAAC,EAAAC,GAGA,GAFAP,EAAAhS,KAAAqF,KAAAvF,EAAAsG,GAEA0H,GAAA,iBAAAA,EACA,MAAA0E,UAAA,4BA0CA,GApCAnN,KAAAoL,WAAA,GAMApL,KAAAyI,OAAA3J,OAAA+N,OAAA7M,KAAAoL,YAMApL,KAAAgN,QAAAA,EAMAhN,KAAAiN,SAAAA,GAAA,GAMAjN,KAAAkN,cAAAA,EAMAlN,KAAAoN,SAAAjT,GAMAsO,EACA,IAAA,IAAA1J,EAAAD,OAAAC,KAAA0J,GAAA5L,EAAA,EAAAA,EAAAkC,EAAAnD,SAAAiB,EACA,iBAAA4L,EAAA1J,EAAAlC,MACAmD,KAAAoL,WAAApL,KAAAyI,OAAA1J,EAAAlC,IAAA4L,EAAA1J,EAAAlC,KAAAkC,EAAAlC,IAiBA6M,EAAA2D,SAAA,SAAA5S,EAAAqM,GACAwG,EAAA,IAAA5D,EAAAjP,EAAAqM,EAAA2B,OAAA3B,EAAA/F,QAAA+F,EAAAkG,QAAAlG,EAAAmG,UAEA,OADAK,EAAAF,SAAAtG,EAAAsG,SACAE,GAQA5D,EAAAxJ,UAAAqN,OAAA,SAAAC,GACAC,IAAAD,KAAAA,EAAAC,aACA,OAAA5S,EAAA+P,SAAA,CACA,UAAA5K,KAAAe,QACA,gBAAAf,KAAAkN,cACA,SAAAlN,KAAAyI,OACA,WAAAzI,KAAAoN,UAAApN,KAAAoN,SAAAxR,OAAAoE,KAAAoN,SAAAjT,GACA,UAAAsT,EAAAzN,KAAAgN,QAAA7S,GACA,WAAAsT,EAAAzN,KAAAiN,SAAA9S,MAcAuP,EAAAxJ,UAAAwN,IAAA,SAAAjT,EAAA4M,EAAA2F,EAAAjM,GAGA,IAAAlG,EAAA8S,SAAAlT,GACA,MAAA0S,UAAA,yBAEA,IAAAtS,EAAA+S,UAAAvG,GACA,MAAA8F,UAAA,yBAEA,GAAAnN,KAAAyI,OAAAhO,KAAAN,GACA,MAAA6D,MAAA,mBAAAvD,EAAA,QAAAuF,MAEA,GAAAA,KAAA6N,aAAAxG,GACA,MAAArJ,MAAA,MAAAqJ,EAAA,mBAAArH,MAEA,GAAAA,KAAA8N,eAAArT,GACA,MAAAuD,MAAA,SAAAvD,EAAA,oBAAAuF,MAEA,GAAAA,KAAAoL,WAAA/D,KAAAlN,GAAA,CACA,IAAA6F,KAAAe,UAAAf,KAAAe,QAAAgN,YACA,MAAA/P,MAAA,gBAAAqJ,EAAA,OAAArH,MACAA,KAAAyI,OAAAhO,GAAA4M,OAEArH,KAAAoL,WAAApL,KAAAyI,OAAAhO,GAAA4M,GAAA5M,EASA,OAPAsG,IACAf,KAAAkN,gBAAA/S,KACA6F,KAAAkN,cAAA,IACAlN,KAAAkN,cAAAzS,GAAAsG,GAAA,MAGAf,KAAAiN,SAAAxS,GAAAuS,GAAA,KACAhN,MAUA0J,EAAAxJ,UAAA8N,OAAA,SAAAvT,GAEA,IAAAI,EAAA8S,SAAAlT,GACA,MAAA0S,UAAA,yBAEA,IAAAhL,EAAAnC,KAAAyI,OAAAhO,GACA,GAAA,MAAA0H,EACA,MAAAnE,MAAA,SAAAvD,EAAA,uBAAAuF,MAQA,cANAA,KAAAoL,WAAAjJ,UACAnC,KAAAyI,OAAAhO,UACAuF,KAAAiN,SAAAxS,GACAuF,KAAAkN,sBACAlN,KAAAkN,cAAAzS,GAEAuF,MAQA0J,EAAAxJ,UAAA2N,aAAA,SAAAxG,GACA,OAAAuF,EAAAiB,aAAA7N,KAAAoN,SAAA/F,IAQAqC,EAAAxJ,UAAA4N,eAAA,SAAArT,GACA,OAAAmS,EAAAkB,eAAA9N,KAAAoN,SAAA3S,K,yCCnMAW,EAAAR,QAAAqT,EAGA,IAOAC,EAPAvB,EAAArR,EAAA,IAGAoO,KAFAuE,EAAA/N,UAAApB,OAAA+N,OAAAF,EAAAzM,YAAA4M,YAAAmB,GAAAlB,UAAA,QAEAzR,EAAA,KACA2Q,EAAA3Q,EAAA,IACAT,EAAAS,EAAA,IAIA6S,EAAA,+BAyCA,SAAAF,EAAAxT,EAAA4M,EAAAD,EAAAwB,EAAAwF,EAAArN,EAAAiM,GAcA,GAZAnS,EAAAwT,SAAAzF,IACAoE,EAAAoB,EACArN,EAAA6H,EACAA,EAAAwF,EAAAjU,IACAU,EAAAwT,SAAAD,KACApB,EAAAjM,EACAA,EAAAqN,EACAA,EAAAjU,IAGAwS,EAAAhS,KAAAqF,KAAAvF,EAAAsG,IAEAlG,EAAA+S,UAAAvG,IAAAA,EAAA,EACA,MAAA8F,UAAA,qCAEA,IAAAtS,EAAA8S,SAAAvG,GACA,MAAA+F,UAAA,yBAEA,GAAAvE,IAAAzO,KAAAgU,EAAAlQ,KAAA2K,EAAAA,EAAAnK,WAAA6P,eACA,MAAAnB,UAAA,8BAEA,GAAAiB,IAAAjU,KAAAU,EAAA8S,SAAAS,GACA,MAAAjB,UAAA,2BASAnN,KAAA4I,MAFAA,EADA,oBAAAA,EACA,WAEAA,IAAA,aAAAA,EAAAA,EAAAzO,GAMA6F,KAAAoH,KAAAA,EAMApH,KAAAqH,GAAAA,EAMArH,KAAAoO,OAAAA,GAAAjU,GAMA6F,KAAAsM,SAAA,aAAA1D,EAMA5I,KAAA0M,UAAA1M,KAAAsM,SAMAtM,KAAAkK,SAAA,aAAAtB,EAMA5I,KAAA2K,KAAA,EAMA3K,KAAAuO,QAAA,KAMAvO,KAAAkL,OAAA,KAMAlL,KAAAiK,YAAA,KAMAjK,KAAAwO,aAAA,KAMAxO,KAAAqL,OAAAxQ,EAAAI,MAAAgR,EAAAZ,KAAAjE,KAAAjN,GAMA6F,KAAA0L,MAAA,UAAAtE,EAMApH,KAAAgK,aAAA,KAMAhK,KAAAyO,eAAA,KAMAzO,KAAA0O,eAAA,KAOA1O,KAAA2O,EAAA,KAMA3O,KAAAgN,QAAAA,EAhKAiB,EAAAZ,SAAA,SAAA5S,EAAAqM,GACA,OAAA,IAAAmH,EAAAxT,EAAAqM,EAAAO,GAAAP,EAAAM,KAAAN,EAAA8B,KAAA9B,EAAAsH,OAAAtH,EAAA/F,QAAA+F,EAAAkG,UAwKAlO,OAAA8P,eAAAX,EAAA/N,UAAA,SAAA,CACAsJ,IAAA,WAIA,OAFA,OAAAxJ,KAAA2O,IACA3O,KAAA2O,GAAA,IAAA3O,KAAA6O,UAAA,WACA7O,KAAA2O,KAOAV,EAAA/N,UAAA4O,UAAA,SAAArU,EAAAgF,EAAAsP,GAGA,MAFA,WAAAtU,IACAuF,KAAA2O,EAAA,MACAhC,EAAAzM,UAAA4O,UAAAnU,KAAAqF,KAAAvF,EAAAgF,EAAAsP,IAwBAd,EAAA/N,UAAAqN,OAAA,SAAAC,GACAC,IAAAD,KAAAA,EAAAC,aACA,OAAA5S,EAAA+P,SAAA,CACA,OAAA,aAAA5K,KAAA4I,MAAA5I,KAAA4I,MAAAzO,GACA,OAAA6F,KAAAoH,KACA,KAAApH,KAAAqH,GACA,SAAArH,KAAAoO,OACA,UAAApO,KAAAe,QACA,UAAA0M,EAAAzN,KAAAgN,QAAA7S,MASA8T,EAAA/N,UAAAjE,QAAA,WAEA,OAAA+D,KAAAgP,SACAhP,OAEAA,KAAAiK,YAAAgC,EAAAC,SAAAlM,KAAAoH,SAAAjN,IACA6F,KAAAgK,cAAAhK,KAAA0O,gBAAA1O,MAAAiP,OAAAC,iBAAAlP,KAAAoH,MACApH,KAAAgK,wBAAAkE,EACAlO,KAAAiK,YAAA,KAEAjK,KAAAiK,YAAAjK,KAAAgK,aAAAvB,OAAA3J,OAAAC,KAAAiB,KAAAgK,aAAAvB,QAAA,KACAzI,KAAAe,SAAAf,KAAAe,QAAAoO,kBAEAnP,KAAAiK,YAAA,MAIAjK,KAAAe,SAAA,MAAAf,KAAAe,QAAA,UACAf,KAAAiK,YAAAjK,KAAAe,QAAA,QACAf,KAAAgK,wBAAAN,GAAA,iBAAA1J,KAAAiK,cACAjK,KAAAiK,YAAAjK,KAAAgK,aAAAvB,OAAAzI,KAAAiK,eAIAjK,KAAAe,WACA,IAAAf,KAAAe,QAAAqL,SAAApM,KAAAe,QAAAqL,SAAAjS,KAAA6F,KAAAgK,cAAAhK,KAAAgK,wBAAAN,WACA1J,KAAAe,QAAAqL,OACAtN,OAAAC,KAAAiB,KAAAe,SAAAnF,SACAoE,KAAAe,QAAA5G,KAIA6F,KAAAqL,MACArL,KAAAiK,YAAApP,EAAAI,KAAAmU,WAAApP,KAAAiK,YAAA,MAAAjK,KAAAoH,KAAA,IAAApH,KAGAlB,OAAAuQ,QACAvQ,OAAAuQ,OAAArP,KAAAiK,cAEAjK,KAAA0L,OAAA,iBAAA1L,KAAAiK,cAEApP,EAAAwB,OAAA4B,KAAA+B,KAAAiK,aACApP,EAAAwB,OAAAwB,OAAAmC,KAAAiK,YAAA7H,EAAAvH,EAAAyU,UAAAzU,EAAAwB,OAAAT,OAAAoE,KAAAiK,cAAA,GAEApP,EAAAyL,KAAAG,MAAAzG,KAAAiK,YAAA7H,EAAAvH,EAAAyU,UAAAzU,EAAAyL,KAAA1K,OAAAoE,KAAAiK,cAAA,GACAjK,KAAAiK,YAAA7H,GAIApC,KAAA2K,IACA3K,KAAAwO,aAAA3T,EAAA0U,YACAvP,KAAAkK,SACAlK,KAAAwO,aAAA3T,EAAA2U,WAEAxP,KAAAwO,aAAAxO,KAAAiK,YAGAjK,KAAAiP,kBAAAf,IACAlO,KAAAiP,OAAAQ,KAAAvP,UAAAF,KAAAvF,MAAAuF,KAAAwO,cAEA7B,EAAAzM,UAAAjE,QAAAtB,KAAAqF,OA5BA,IAQAoC,GA2CA6L,EAAAyB,EAAA,SAAAC,EAAAC,EAAAC,EAAArB,GAUA,MAPA,mBAAAoB,EACAA,EAAA/U,EAAAiV,aAAAF,GAAAnV,KAGAmV,GAAA,iBAAAA,IACAA,EAAA/U,EAAAkV,aAAAH,GAAAnV,MAEA,SAAAyF,EAAA8P,GACAnV,EAAAiV,aAAA5P,EAAA4M,aACAY,IAAA,IAAAO,EAAA+B,EAAAL,EAAAC,EAAAC,EAAA,CAAAI,QAAAzB,OAkBAP,EAAAiC,EAAA,SAAAC,GACAjC,EAAAiC,I,+CCtXA,IAAA5V,EAAAa,EAAAR,QAAAU,EAAA,IAEAf,EAAA6V,MAAA,QAoDA7V,EAAA8V,KAjCA,SAAAvP,EAAAwP,EAAAtP,GAMA,OAHAsP,EAFA,mBAAAA,GACAtP,EAAAsP,EACA,IAAA/V,EAAAgW,MACAD,GACA,IAAA/V,EAAAgW,MACAF,KAAAvP,EAAAE,IA2CAzG,EAAAiW,SANA,SAAA1P,EAAAwP,GAGA,OADAA,EADAA,GACA,IAAA/V,EAAAgW,MACAC,SAAA1P,IAMAvG,EAAAkW,QAAAnV,EAAA,IACAf,EAAAmW,QAAApV,EAAA,IACAf,EAAAoW,SAAArV,EAAA,IACAf,EAAA+P,UAAAhP,EAAA,IAGAf,EAAAoS,iBAAArR,EAAA,IACAf,EAAAqS,UAAAtR,EAAA,IACAf,EAAAgW,KAAAjV,EAAA,IACAf,EAAAmP,KAAApO,EAAA,IACAf,EAAA2T,KAAA5S,EAAA,IACAf,EAAA0T,MAAA3S,EAAA,IACAf,EAAAqW,MAAAtV,EAAA,IACAf,EAAAsW,SAAAvV,EAAA,IACAf,EAAAuW,QAAAxV,EAAA,IACAf,EAAAwW,OAAAzV,EAAA,IAGAf,EAAAyW,QAAA1V,EAAA,IACAf,EAAA0W,SAAA3V,EAAA,IAGAf,EAAA0R,MAAA3Q,EAAA,IACAf,EAAAM,KAAAS,EAAA,IAGAf,EAAAoS,iBAAAuD,EAAA3V,EAAAgW,MACAhW,EAAAqS,UAAAsD,EAAA3V,EAAA2T,KAAA3T,EAAAuW,QAAAvW,EAAAmP,MACAnP,EAAAgW,KAAAL,EAAA3V,EAAA2T,MACA3T,EAAA0T,MAAAiC,EAAA3V,EAAA2T,O,yICtGA,IAAA3T,EAAAK,EA2BA,SAAAO,IACAZ,EAAAM,KAAAqV,IACA3V,EAAA2W,OAAAhB,EAAA3V,EAAA4W,cACA5W,EAAA6W,OAAAlB,EAAA3V,EAAA8W,cAtBA9W,EAAA6V,MAAA,UAGA7V,EAAA2W,OAAA5V,EAAA,IACAf,EAAA4W,aAAA7V,EAAA,IACAf,EAAA6W,OAAA9V,EAAA,IACAf,EAAA8W,aAAA/V,EAAA,IAGAf,EAAAM,KAAAS,EAAA,IACAf,EAAA+W,IAAAhW,EAAA,IACAf,EAAAgX,MAAAjW,EAAA,IACAf,EAAAY,UAAAA,EAcAA,K,iEClCAZ,EAAAa,EAAAR,QAAAU,EAAA,IAEAf,EAAA6V,MAAA,OAGA7V,EAAAiX,SAAAlW,EAAA,IACAf,EAAAkX,MAAAnW,EAAA,IACAf,EAAAqM,OAAAtL,EAAA,IAGAf,EAAAgW,KAAAL,EAAA3V,EAAA2T,KAAA3T,EAAAkX,MAAAlX,EAAAqM,S,+CCVAxL,EAAAR,QAAAiW,EAGA,IAAA5C,EAAA3S,EAAA,IAGA2Q,KAFA4E,EAAA3Q,UAAApB,OAAA+N,OAAAoB,EAAA/N,YAAA4M,YAAA+D,GAAA9D,UAAA,WAEAzR,EAAA,KACAT,EAAAS,EAAA,IAcA,SAAAuV,EAAApW,EAAA4M,EAAAQ,EAAAT,EAAArG,EAAAiM,GAIA,GAHAiB,EAAAtT,KAAAqF,KAAAvF,EAAA4M,EAAAD,EAAAjN,GAAAA,GAAA4G,EAAAiM,IAGAnS,EAAA8S,SAAA9F,GACA,MAAAsF,UAAA,4BAMAnN,KAAA6H,QAAAA,EAMA7H,KAAA0R,gBAAA,KAGA1R,KAAA2K,KAAA,EAwBAkG,EAAAxD,SAAA,SAAA5S,EAAAqM,GACA,OAAA,IAAA+J,EAAApW,EAAAqM,EAAAO,GAAAP,EAAAe,QAAAf,EAAAM,KAAAN,EAAA/F,QAAA+F,EAAAkG,UAQA6D,EAAA3Q,UAAAqN,OAAA,SAAAC,GACAC,IAAAD,KAAAA,EAAAC,aACA,OAAA5S,EAAA+P,SAAA,CACA,UAAA5K,KAAA6H,QACA,OAAA7H,KAAAoH,KACA,KAAApH,KAAAqH,GACA,SAAArH,KAAAoO,OACA,UAAApO,KAAAe,QACA,UAAA0M,EAAAzN,KAAAgN,QAAA7S,MAOA0W,EAAA3Q,UAAAjE,QAAA,WACA,GAAA+D,KAAAgP,SACA,OAAAhP,KAGA,GAAAiM,EAAAO,OAAAxM,KAAA6H,WAAA1N,GACA,MAAA6D,MAAA,qBAAAgC,KAAA6H,SAEA,OAAAoG,EAAA/N,UAAAjE,QAAAtB,KAAAqF,OAaA6Q,EAAAnB,EAAA,SAAAC,EAAAgC,EAAAC,GAUA,MAPA,mBAAAA,EACAA,EAAA/W,EAAAiV,aAAA8B,GAAAnX,KAGAmX,GAAA,iBAAAA,IACAA,EAAA/W,EAAAkV,aAAA6B,GAAAnX,MAEA,SAAAyF,EAAA8P,GACAnV,EAAAiV,aAAA5P,EAAA4M,aACAY,IAAA,IAAAmD,EAAAb,EAAAL,EAAAgC,EAAAC,O,yCC1HAxW,EAAAR,QAAAoW,EAEA,IAAAnW,EAAAS,EAAA,IASA,SAAA0V,EAAAa,GAEA,GAAAA,EACA,IAAA,IAAA9S,EAAAD,OAAAC,KAAA8S,GAAAhV,EAAA,EAAAA,EAAAkC,EAAAnD,SAAAiB,EACAmD,KAAAjB,EAAAlC,IAAAgV,EAAA9S,EAAAlC,IA0BAmU,EAAAnE,OAAA,SAAAgF,GACA,OAAA7R,KAAA8R,MAAAjF,OAAAgF,IAWAb,EAAAlU,OAAA,SAAAyR,EAAAwD,GACA,OAAA/R,KAAA8R,MAAAhV,OAAAyR,EAAAwD,IAWAf,EAAAgB,gBAAA,SAAAzD,EAAAwD,GACA,OAAA/R,KAAA8R,MAAAE,gBAAAzD,EAAAwD,IAYAf,EAAAnT,OAAA,SAAAoU,GACA,OAAAjS,KAAA8R,MAAAjU,OAAAoU,IAYAjB,EAAAkB,gBAAA,SAAAD,GACA,OAAAjS,KAAA8R,MAAAI,gBAAAD,IAUAjB,EAAAmB,OAAA,SAAA5D,GACA,OAAAvO,KAAA8R,MAAAK,OAAA5D,IAUAyC,EAAAzG,WAAA,SAAA6H,GACA,OAAApS,KAAA8R,MAAAvH,WAAA6H,IAWApB,EAAApG,SAAA,SAAA2D,EAAAxN,GACA,OAAAf,KAAA8R,MAAAlH,SAAA2D,EAAAxN,IAOAiQ,EAAA9Q,UAAAqN,OAAA,WACA,OAAAvN,KAAA8R,MAAAlH,SAAA5K,KAAAnF,EAAA2S,iB,6BCtIApS,EAAAR,QAAAmW,EAGA,IAAApE,EAAArR,EAAA,IAGAT,KAFAkW,EAAA7Q,UAAApB,OAAA+N,OAAAF,EAAAzM,YAAA4M,YAAAiE,GAAAhE,UAAA,SAEAzR,EAAA,KAiBA,SAAAyV,EAAAtW,EAAA2M,EAAAiL,EAAAxQ,EAAAyQ,EAAAC,EAAAxR,EAAAiM,EAAAwF,GAYA,GATA3X,EAAAwT,SAAAiE,IACAvR,EAAAuR,EACAA,EAAAC,EAAApY,IACAU,EAAAwT,SAAAkE,KACAxR,EAAAwR,EACAA,EAAApY,IAIAiN,IAAAjN,KAAAU,EAAA8S,SAAAvG,GACA,MAAA+F,UAAA,yBAGA,IAAAtS,EAAA8S,SAAA0E,GACA,MAAAlF,UAAA,gCAGA,IAAAtS,EAAA8S,SAAA9L,GACA,MAAAsL,UAAA,iCAEAR,EAAAhS,KAAAqF,KAAAvF,EAAAsG,GAMAf,KAAAoH,KAAAA,GAAA,MAMApH,KAAAqS,YAAAA,EAMArS,KAAAsS,gBAAAA,GAAAnY,GAMA6F,KAAA6B,aAAAA,EAMA7B,KAAAuS,iBAAAA,GAAApY,GAMA6F,KAAAyS,oBAAA,KAMAzS,KAAA0S,qBAAA,KAMA1S,KAAAgN,QAAAA,EAKAhN,KAAAwS,cAAAA,EAuBAzB,EAAA1D,SAAA,SAAA5S,EAAAqM,GACA,OAAA,IAAAiK,EAAAtW,EAAAqM,EAAAM,KAAAN,EAAAuL,YAAAvL,EAAAjF,aAAAiF,EAAAwL,cAAAxL,EAAAyL,eAAAzL,EAAA/F,QAAA+F,EAAAkG,QAAAlG,EAAA0L,gBAQAzB,EAAA7Q,UAAAqN,OAAA,SAAAC,GACAC,IAAAD,KAAAA,EAAAC,aACA,OAAA5S,EAAA+P,SAAA,CACA,OAAA,QAAA5K,KAAAoH,MAAApH,KAAAoH,MAAAjN,GACA,cAAA6F,KAAAqS,YACA,gBAAArS,KAAAsS,cACA,eAAAtS,KAAA6B,aACA,iBAAA7B,KAAAuS,eACA,UAAAvS,KAAAe,QACA,UAAA0M,EAAAzN,KAAAgN,QAAA7S,GACA,gBAAA6F,KAAAwS,iBAOAzB,EAAA7Q,UAAAjE,QAAA,WAGA,OAAA+D,KAAAgP,SACAhP,MAEAA,KAAAyS,oBAAAzS,KAAAiP,OAAA0D,WAAA3S,KAAAqS,aACArS,KAAA0S,qBAAA1S,KAAAiP,OAAA0D,WAAA3S,KAAA6B,cAEA8K,EAAAzM,UAAAjE,QAAAtB,KAAAqF,S,mCC7JA5E,EAAAR,QAAAgS,EAGA,IAOAsB,EACA4C,EACApH,EATAiD,EAAArR,EAAA,IAGA2S,KAFArB,EAAA1M,UAAApB,OAAA+N,OAAAF,EAAAzM,YAAA4M,YAAAF,GAAAG,UAAA,YAEAzR,EAAA,KACAT,EAAAS,EAAA,IACAsV,EAAAtV,EAAA,IAoCA,SAAAsX,EAAAC,EAAArF,GACA,IAAAqF,IAAAA,EAAAjX,OACA,OAAAzB,GAEA,IADA,IAAA2Y,EAAA,GACAjW,EAAA,EAAAA,EAAAgW,EAAAjX,SAAAiB,EACAiW,EAAAD,EAAAhW,GAAApC,MAAAoY,EAAAhW,GAAA0Q,OAAAC,GACA,OAAAsF,EA4CA,SAAAlG,EAAAnS,EAAAsG,GACA4L,EAAAhS,KAAAqF,KAAAvF,EAAAsG,GAMAf,KAAA+G,OAAA5M,GAOA6F,KAAA+S,EAAA,KAGA,SAAAC,EAAAC,GAEA,OADAA,EAAAF,EAAA,KACAE,EAhFArG,EAAAS,SAAA,SAAA5S,EAAAqM,GACA,OAAA,IAAA8F,EAAAnS,EAAAqM,EAAA/F,SAAAmS,QAAApM,EAAAC,SAmBA6F,EAAAgG,YAAAA,EAQAhG,EAAAiB,aAAA,SAAAT,EAAA/F,GACA,GAAA+F,EACA,IAAA,IAAAvQ,EAAA,EAAAA,EAAAuQ,EAAAxR,SAAAiB,EACA,GAAA,iBAAAuQ,EAAAvQ,IAAAuQ,EAAAvQ,GAAA,IAAAwK,GAAA+F,EAAAvQ,GAAA,GAAAwK,EACA,OAAA,EACA,OAAA,GASAuF,EAAAkB,eAAA,SAAAV,EAAA3S,GACA,GAAA2S,EACA,IAAA,IAAAvQ,EAAA,EAAAA,EAAAuQ,EAAAxR,SAAAiB,EACA,GAAAuQ,EAAAvQ,KAAApC,EACA,OAAA,EACA,OAAA,GA0CAqE,OAAA8P,eAAAhC,EAAA1M,UAAA,cAAA,CACAsJ,IAAA,WACA,OAAAxJ,KAAA+S,IAAA/S,KAAA+S,EAAAlY,EAAAsY,QAAAnT,KAAA+G,YA6BA6F,EAAA1M,UAAAqN,OAAA,SAAAC,GACA,OAAA3S,EAAA+P,SAAA,CACA,UAAA5K,KAAAe,QACA,SAAA6R,EAAA5S,KAAAoT,YAAA5F,MASAZ,EAAA1M,UAAAgT,QAAA,SAAAG,GAGA,GAAAA,EACA,IAAA,IAAAtM,EAAAuM,EAAAxU,OAAAC,KAAAsU,GAAAxW,EAAA,EAAAA,EAAAyW,EAAA1X,SAAAiB,EACAkK,EAAAsM,EAAAC,EAAAzW,IAJAmD,KAKA0N,KACA3G,EAAAG,SAAA/M,GACA+T,EACAnH,EAAA0B,SAAAtO,GACAuP,EACA3C,EAAAwM,UAAApZ,GACA2W,EACA/J,EAAAM,KAAAlN,GACA8T,EACArB,GAPAS,SAOAiG,EAAAzW,GAAAkK,IAIA,OAAA/G,MAQA4M,EAAA1M,UAAAsJ,IAAA,SAAA/O,GACA,OAAAuF,KAAA+G,QAAA/G,KAAA+G,OAAAtM,IACA,MAUAmS,EAAA1M,UAAAsT,QAAA,SAAA/Y,GACA,GAAAuF,KAAA+G,QAAA/G,KAAA+G,OAAAtM,aAAAiP,EACA,OAAA1J,KAAA+G,OAAAtM,GAAAgO,OACA,MAAAzK,MAAA,iBAAAvD,IAUAmS,EAAA1M,UAAAwN,IAAA,SAAA0E,GAEA,KAAAA,aAAAnE,GAAAmE,EAAAhE,SAAAjU,IAAAiY,aAAAlE,GAAAkE,aAAAxB,GAAAwB,aAAA1I,GAAA0I,aAAAtB,GAAAsB,aAAAxF,GACA,MAAAO,UAAA,wCAEA,GAAAnN,KAAA+G,OAEA,CACA,IAAA0M,EAAAzT,KAAAwJ,IAAA4I,EAAA3X,MACA,GAAAgZ,EAAA,CACA,KAAAA,aAAA7G,GAAAwF,aAAAxF,IAAA6G,aAAAvF,GAAAuF,aAAA3C,EAWA,MAAA9S,MAAA,mBAAAoU,EAAA3X,KAAA,QAAAuF,MARA,IADA,IAAA+G,EAAA0M,EAAAL,YACAvW,EAAA,EAAAA,EAAAkK,EAAAnL,SAAAiB,EACAuV,EAAA1E,IAAA3G,EAAAlK,IACAmD,KAAAgO,OAAAyF,GACAzT,KAAA+G,SACA/G,KAAA+G,OAAA,IACAqL,EAAAsB,WAAAD,EAAA1S,SAAA,SAZAf,KAAA+G,OAAA,GAoBA,OAFA/G,KAAA+G,OAAAqL,EAAA3X,MAAA2X,GACAuB,MAAA3T,MACAgT,EAAAhT,OAUA4M,EAAA1M,UAAA8N,OAAA,SAAAoE,GAEA,KAAAA,aAAAzF,GACA,MAAAQ,UAAA,qCACA,GAAAiF,EAAAnD,SAAAjP,KACA,MAAAhC,MAAAoU,EAAA,uBAAApS,MAOA,cALAA,KAAA+G,OAAAqL,EAAA3X,MACAqE,OAAAC,KAAAiB,KAAA+G,QAAAnL,SACAoE,KAAA+G,OAAA5M,IAEAiY,EAAAwB,SAAA5T,MACAgT,EAAAhT,OASA4M,EAAA1M,UAAAnF,OAAA,SAAAyK,EAAAsB,GAEA,GAAAjM,EAAA8S,SAAAnI,GACAA,EAAAA,EAAAE,MAAA,UACA,IAAAhK,MAAAmY,QAAArO,GACA,MAAA2H,UAAA,gBACA,GAAA3H,GAAAA,EAAA5J,QAAA,KAAA4J,EAAA,GACA,MAAAxH,MAAA,yBAGA,IADA,IAAA8V,EAAA9T,KACA,EAAAwF,EAAA5J,QAAA,CACA,IAAAmY,EAAAvO,EAAAK,QACA,GAAAiO,EAAA/M,QAAA+M,EAAA/M,OAAAgN,IAEA,MADAD,EAAAA,EAAA/M,OAAAgN,cACAnH,GACA,MAAA5O,MAAA,kDAEA8V,EAAApG,IAAAoG,EAAA,IAAAlH,EAAAmH,IAIA,OAFAjN,GACAgN,EAAAZ,QAAApM,GACAgN,GAOAlH,EAAA1M,UAAA8T,WAAA,WAEA,IADA,IAAAjN,EAAA/G,KAAAoT,YAAAvW,EAAA,EACAA,EAAAkK,EAAAnL,QACAmL,EAAAlK,aAAA+P,EACA7F,EAAAlK,KAAAmX,aAEAjN,EAAAlK,KAAAZ,UACA,OAAA+D,KAAA/D,WAUA2Q,EAAA1M,UAAA+T,OAAA,SAAAzO,EAAA0O,EAAAC,GASA,GANA,kBAAAD,GACAC,EAAAD,EACAA,EAAA/Z,IACA+Z,IAAAxY,MAAAmY,QAAAK,KACAA,EAAA,CAAAA,IAEArZ,EAAA8S,SAAAnI,IAAAA,EAAA5J,OAAA,CACA,GAAA,MAAA4J,EACA,OAAAxF,KAAAsQ,KACA9K,EAAAA,EAAAE,MAAA,UACA,IAAAF,EAAA5J,OACA,OAAAoE,KAGA,GAAA,KAAAwF,EAAA,GACA,OAAAxF,KAAAsQ,KAAA2D,OAAAzO,EAAA9H,MAAA,GAAAwW,GAGA,IAAAE,EAAApU,KAAAwJ,IAAAhE,EAAA,IACA,GAAA4O,GACA,GAAA,IAAA5O,EAAA5J,QACA,IAAAsY,IAAAA,EAAArI,QAAAuI,EAAAtH,aACA,OAAAsH,OACA,GAAAA,aAAAxH,IAAAwH,EAAAA,EAAAH,OAAAzO,EAAA9H,MAAA,GAAAwW,GAAA,IACA,OAAAE,OAIA,IAAA,IAAAvX,EAAA,EAAAA,EAAAmD,KAAAoT,YAAAxX,SAAAiB,EACA,GAAAmD,KAAA+S,EAAAlW,aAAA+P,IAAAwH,EAAApU,KAAA+S,EAAAlW,GAAAoX,OAAAzO,EAAA0O,GAAA,IACA,OAAAE,EAGA,OAAA,OAAApU,KAAAiP,QAAAkF,EACA,KACAnU,KAAAiP,OAAAgF,OAAAzO,EAAA0O,IAqBAtH,EAAA1M,UAAAyS,WAAA,SAAAnN,GACA,IAAA4O,EAAApU,KAAAiU,OAAAzO,EAAA,CAAA0I,IACA,GAAAkG,EAEA,OAAAA,EADA,MAAApW,MAAA,iBAAAwH,IAWAoH,EAAA1M,UAAAmU,WAAA,SAAA7O,GACA,IAAA4O,EAAApU,KAAAiU,OAAAzO,EAAA,CAAAkE,IACA,GAAA0K,EAEA,OAAAA,EADA,MAAApW,MAAA,iBAAAwH,EAAA,QAAAxF,OAWA4M,EAAA1M,UAAAgP,iBAAA,SAAA1J,GACA,IAAA4O,EAAApU,KAAAiU,OAAAzO,EAAA,CAAA0I,EAAAxE,IACA,GAAA0K,EAEA,OAAAA,EADA,MAAApW,MAAA,yBAAAwH,EAAA,QAAAxF,OAWA4M,EAAA1M,UAAAoU,cAAA,SAAA9O,GACA,IAAA4O,EAAApU,KAAAiU,OAAAzO,EAAA,CAAAsL,IACA,GAAAsD,EAEA,OAAAA,EADA,MAAApW,MAAA,oBAAAwH,EAAA,QAAAxF,OAKA4M,EAAAsD,EAAA,SAAAC,EAAAoE,EAAAC,GACAtG,EAAAiC,EACAW,EAAAyD,EACA7K,EAAA8K,I,gDC/aApZ,EAAAR,QAAA+R,GAEAI,UAAA,mBAEA,IAEAwD,EAFA1V,EAAAS,EAAA,IAYA,SAAAqR,EAAAlS,EAAAsG,GAEA,IAAAlG,EAAA8S,SAAAlT,GACA,MAAA0S,UAAA,yBAEA,GAAApM,IAAAlG,EAAAwT,SAAAtN,GACA,MAAAoM,UAAA,6BAMAnN,KAAAe,QAAAA,EAMAf,KAAAwS,cAAA,KAMAxS,KAAAvF,KAAAA,EAMAuF,KAAAiP,OAAA,KAMAjP,KAAAgP,UAAA,EAMAhP,KAAAgN,QAAA,KAMAhN,KAAAc,SAAA,KAGAhC,OAAA2V,iBAAA9H,EAAAzM,UAAA,CAQAoQ,KAAA,CACA9G,IAAA,WAEA,IADA,IAAAsK,EAAA9T,KACA,OAAA8T,EAAA7E,QACA6E,EAAAA,EAAA7E,OACA,OAAA6E,IAUA3J,SAAA,CACAX,IAAA,WAGA,IAFA,IAAAhE,EAAA,CAAAxF,KAAAvF,MACAqZ,EAAA9T,KAAAiP,OACA6E,GACAtO,EAAAkP,QAAAZ,EAAArZ,MACAqZ,EAAAA,EAAA7E,OAEA,OAAAzJ,EAAA7H,KAAA,SAUAgP,EAAAzM,UAAAqN,OAAA,WACA,MAAAvP,SAQA2O,EAAAzM,UAAAyT,MAAA,SAAA1E,GACAjP,KAAAiP,QAAAjP,KAAAiP,SAAAA,GACAjP,KAAAiP,OAAAjB,OAAAhO,MACAA,KAAAiP,OAAAA,EACAjP,KAAAgP,UAAA,EACAsB,EAAArB,EAAAqB,KACAA,aAAAC,GACAD,EAAAqE,EAAA3U,OAQA2M,EAAAzM,UAAA0T,SAAA,SAAA3E,GACAqB,EAAArB,EAAAqB,KACAA,aAAAC,GACAD,EAAAsE,EAAA5U,MACAA,KAAAiP,OAAA,KACAjP,KAAAgP,UAAA,GAOArC,EAAAzM,UAAAjE,QAAA,WACA,OAAA+D,KAAAgP,UAEAhP,KAAAsQ,gBAAAC,IACAvQ,KAAAgP,UAAA,GAFAhP,MAWA2M,EAAAzM,UAAA2O,UAAA,SAAApU,GACA,OAAAuF,KAAAe,QACAf,KAAAe,QAAAtG,GACAN,IAUAwS,EAAAzM,UAAA4O,UAAA,SAAArU,EAAAgF,EAAAsP,GAGA,OAFAA,GAAA/O,KAAAe,SAAAf,KAAAe,QAAAtG,KAAAN,MACA6F,KAAAe,UAAAf,KAAAe,QAAA,KAAAtG,GAAAgF,GACAO,MAUA2M,EAAAzM,UAAA2U,gBAAA,SAAApa,EAAAgF,EAAAqV,GACA9U,KAAAwS,gBACAxS,KAAAwS,cAAA,IAEA,IAIAuC,EAeAC,EAnBAxC,EAAAxS,KAAAwS,cAuBA,OAtBAsC,GAGAC,EAAAvC,EAAAyC,KAAA,SAAAF,GACA,OAAAjW,OAAAoB,UAAAgV,eAAAva,KAAAoa,EAAAta,OAIA0a,EAAAJ,EAAAta,GACAI,EAAAua,YAAAD,EAAAL,EAAArV,MAGAsV,EAAA,IACAta,GAAAI,EAAAua,YAAA,GAAAN,EAAArV,GACA+S,EAAAjV,KAAAwX,MAIAC,EAAA,IACAva,GAAAgF,EACA+S,EAAAjV,KAAAyX,IAEAhV,MASA2M,EAAAzM,UAAAwT,WAAA,SAAA3S,EAAAgO,GACA,GAAAhO,EACA,IAAA,IAAAhC,EAAAD,OAAAC,KAAAgC,GAAAlE,EAAA,EAAAA,EAAAkC,EAAAnD,SAAAiB,EACAmD,KAAA8O,UAAA/P,EAAAlC,GAAAkE,EAAAhC,EAAAlC,IAAAkS,GACA,OAAA/O,MAOA2M,EAAAzM,UAAAzB,SAAA,WACA,IAAAsO,EAAA/M,KAAA8M,YAAAC,UACA5C,EAAAnK,KAAAmK,SACA,OAAAA,EAAAvO,OACAmR,EAAA,IAAA5C,EACA4C,GAIAJ,EAAAuD,EAAA,SAAAmF,GACA9E,EAAA8E,I,6BChPAja,EAAAR,QAAAgW,EAGA,IAAAjE,EAAArR,EAAA,IAGA2S,KAFA2C,EAAA1Q,UAAApB,OAAA+N,OAAAF,EAAAzM,YAAA4M,YAAA8D,GAAA7D,UAAA,QAEAzR,EAAA,KACAT,EAAAS,EAAA,IAYA,SAAAsV,EAAAnW,EAAA6a,EAAAvU,EAAAiM,GAQA,GAPAtR,MAAAmY,QAAAyB,KACAvU,EAAAuU,EACAA,EAAAnb,IAEAwS,EAAAhS,KAAAqF,KAAAvF,EAAAsG,GAGAuU,IAAAnb,KAAAuB,MAAAmY,QAAAyB,GACA,MAAAnI,UAAA,+BAMAnN,KAAAiI,MAAAqN,GAAA,GAOAtV,KAAAyK,YAAA,GAMAzK,KAAAgN,QAAAA,EA0CA,SAAAuI,EAAAtN,GACA,GAAAA,EAAAgH,OACA,IAAA,IAAApS,EAAA,EAAAA,EAAAoL,EAAAwC,YAAA7O,SAAAiB,EACAoL,EAAAwC,YAAA5N,GAAAoS,QACAhH,EAAAgH,OAAAvB,IAAAzF,EAAAwC,YAAA5N,IA7BA+T,EAAAvD,SAAA,SAAA5S,EAAAqM,GACA,OAAA,IAAA8J,EAAAnW,EAAAqM,EAAAmB,MAAAnB,EAAA/F,QAAA+F,EAAAkG,UAQA4D,EAAA1Q,UAAAqN,OAAA,SAAAC,GACAC,IAAAD,KAAAA,EAAAC,aACA,OAAA5S,EAAA+P,SAAA,CACA,UAAA5K,KAAAe,QACA,QAAAf,KAAAiI,MACA,UAAAwF,EAAAzN,KAAAgN,QAAA7S,MAuBAyW,EAAA1Q,UAAAwN,IAAA,SAAA7D,GAGA,GAAAA,aAAAoE,EASA,OANApE,EAAAoF,QAAApF,EAAAoF,SAAAjP,KAAAiP,QACApF,EAAAoF,OAAAjB,OAAAnE,GACA7J,KAAAiI,MAAA1K,KAAAsM,EAAApP,MACAuF,KAAAyK,YAAAlN,KAAAsM,GAEA0L,EADA1L,EAAAqB,OAAAlL,MAEAA,KARA,MAAAmN,UAAA,0BAgBAyD,EAAA1Q,UAAA8N,OAAA,SAAAnE,GAGA,KAAAA,aAAAoE,GACA,MAAAd,UAAA,yBAEA,IAAArR,EAAAkE,KAAAyK,YAAAoB,QAAAhC,GAGA,GAAA/N,EAAA,EACA,MAAAkC,MAAA6L,EAAA,uBAAA7J,MAUA,OARAA,KAAAyK,YAAAlK,OAAAzE,EAAA,IAIA,GAHAA,EAAAkE,KAAAiI,MAAA4D,QAAAhC,EAAApP,QAIAuF,KAAAiI,MAAA1H,OAAAzE,EAAA,GAEA+N,EAAAqB,OAAA,KACAlL,MAMA4Q,EAAA1Q,UAAAyT,MAAA,SAAA1E,GACAtC,EAAAzM,UAAAyT,MAAAhZ,KAAAqF,KAAAiP,GAGA,IAFA,IAEApS,EAAA,EAAAA,EAAAmD,KAAAiI,MAAArM,SAAAiB,EAAA,CACA,IAAAgN,EAAAoF,EAAAzF,IAAAxJ,KAAAiI,MAAApL,IACAgN,IAAAA,EAAAqB,SACArB,EAAAqB,OALAlL,MAMAyK,YAAAlN,KAAAsM,GAIA0L,EAAAvV,OAMA4Q,EAAA1Q,UAAA0T,SAAA,SAAA3E,GACA,IAAA,IAAApF,EAAAhN,EAAA,EAAAA,EAAAmD,KAAAyK,YAAA7O,SAAAiB,GACAgN,EAAA7J,KAAAyK,YAAA5N,IAAAoS,QACApF,EAAAoF,OAAAjB,OAAAnE,GACA8C,EAAAzM,UAAA0T,SAAAjZ,KAAAqF,KAAAiP,IAmBA2B,EAAAlB,EAAA,WAGA,IAFA,IAAA4F,EAAA5Z,MAAAC,UAAAC,QACAE,EAAA,EACAA,EAAAH,UAAAC,QACA0Z,EAAAxZ,GAAAH,UAAAG,KACA,OAAA,SAAAoE,EAAAsV,GACA3a,EAAAiV,aAAA5P,EAAA4M,aACAY,IAAA,IAAAkD,EAAA4E,EAAAF,IACAxW,OAAA8P,eAAA1O,EAAAsV,EAAA,CACAhM,IAAA3O,EAAA4a,YAAAH,GACAI,IAAA7a,EAAA8a,YAAAL,Q,0CCtMAla,EAAAR,QAAA6W,IAEA3Q,SAAA,KACA2Q,GAAAvF,SAAA,CAAA0J,UAAA,GAEA,IAAApE,EAAAlW,EAAA,IACAiV,EAAAjV,EAAA,IACA4S,EAAA5S,EAAA,IACA2S,EAAA3S,EAAA,IACAuV,EAAAvV,EAAA,IACAsV,EAAAtV,EAAA,IACAoO,EAAApO,EAAA,IACAwV,EAAAxV,EAAA,IACAyV,EAAAzV,EAAA,IACA2Q,EAAA3Q,EAAA,IACAT,EAAAS,EAAA,IAEAua,EAAA,gBACAC,EAAA,kBACAC,EAAA,qBACAC,EAAA,uBACAC,EAAA,YACAC,EAAA,cACAC,EAAA,oDACAC,EAAA,2BACAC,EAAA,+DACAC,GAAA,kCAmCA,SAAA7E,GAAAjT,EAAA8R,EAAAvP,GAEAuP,aAAAC,IACAxP,EAAAuP,EACAA,EAAA,IAAAC,GAKA,IASAgG,EACAC,EACAC,EACAC,EAisBAC,EAxjBAA,EACAC,EAtJAC,GAFA9V,EADAA,GACA0Q,GAAAvF,UAEA2K,wBAAA,EACAC,EAAAtF,EAAAhT,EAAAuC,EAAAgW,uBAAA,GACAC,EAAAF,EAAAE,KACAzZ,EAAAuZ,EAAAvZ,KACA0Z,EAAAH,EAAAG,KACAC,EAAAJ,EAAAI,KACAC,EAAAL,EAAAK,KAEAC,GAAA,EAKAC,GAAA,EAEAvD,EAAAxD,EAEAgH,EAAAvW,EAAA6U,SAAA,SAAAnb,GAAA,OAAAA,GAAAI,EAAA0c,UAGA,SAAAC,EAAAb,EAAAlc,EAAAgd,GACA,IAAA3W,EAAA2Q,GAAA3Q,SAGA,OAFA2W,IACAhG,GAAA3Q,SAAA,MACA9C,MAAA,YAAAvD,GAAA,SAAA,KAAAkc,EAAA,OAAA7V,EAAAA,EAAA,KAAA,IAAA,QAAAgW,EAAAY,KAAA,KAGA,SAAAC,IACA,IACAhB,EADAlO,EAAA,GAEA,GAEA,GAAA,OAAAkO,EAAAK,MAAA,MAAAL,EACA,MAAAa,EAAAb,SAEAlO,EAAAlL,KAAAyZ,KACAE,EAAAP,GAEA,OADAA,EAAAM,MACA,MAAAN,GACA,OAAAlO,EAAA9K,KAAA,IAGA,SAAAia,EAAAC,GACA,IAAAlB,EAAAK,IACA,OAAAL,GACA,IAAA,IACA,IAAA,IAEA,OADApZ,EAAAoZ,GACAgB,IACA,IAAA,OAAA,IAAA,OACA,OAAA,EACA,IAAA,QAAA,IAAA,QACA,OAAA,EAEA,IACAG,IAuBAnB,EAvBAA,EAuBAc,GAvBA,EAwBAnV,EAAA,EAKA,OAJA,MAAAqU,EAAA,IAAAA,MACArU,GAAA,EACAqU,EAAAA,EAAAoB,UAAA,IAEApB,GACA,IAAA,MAAA,IAAA,MAAA,IAAA,MACA,OAAArU,GAAAW,EAAAA,GACA,IAAA,MAAA,IAAA,MAAA,IAAA,MAAA,IAAA,MACA,OAAAD,IACA,IAAA,IACA,OAAA,EAEA,GAAA6S,EAAA5X,KAAA0Y,GACA,OAAArU,EAAA0V,SAAArB,EAAA,IACA,GAAAZ,EAAA9X,KAAA0Y,GACA,OAAArU,EAAA0V,SAAArB,EAAA,IACA,GAAAV,EAAAhY,KAAA0Y,GACA,OAAArU,EAAA0V,SAAArB,EAAA,GAGA,GAAAR,EAAAlY,KAAA0Y,GACA,OAAArU,EAAA2V,WAAAtB,GAGA,MAAAa,EAAAb,EAAA,SAAAc,GAhDA,MAAAnS,GAGA,GAAAuS,GAAAxB,EAAApY,KAAA0Y,GACA,OAAAA,EAGA,MAAAa,EAAAb,EAAA,UAIA,SAAAuB,EAAAC,EAAAC,GAEA,IADA,IAAApb,GAEAob,GAAA,OAAAzB,EAAAM,MAAA,MAAAN,EAGAwB,EAAA5a,KAAA,CAAAP,EAAAqb,EAAArB,KAAAE,EAAA,MAAA,GAAAmB,EAAArB,KAAAha,IAFAmb,EAAA5a,KAAAoa,KAGAT,EAAA,KAAA,KACAA,EAAA,KAgCA,SAAAmB,EAAA1B,EAAA2B,GACA,OAAA3B,GACA,IAAA,MAAA,IAAA,MAAA,IAAA,MACA,OAAA,UACA,IAAA,IACA,OAAA,EAIA,IAAA2B,GAAA,MAAA3B,EAAA,IAAAA,IACA,MAAAa,EAAAb,EAAA,MAEA,GAAAb,EAAA7X,KAAA0Y,GACA,OAAAqB,SAAArB,EAAA,IACA,GAAAX,EAAA/X,KAAA0Y,GACA,OAAAqB,SAAArB,EAAA,IAGA,GAAAT,EAAAjY,KAAA0Y,GACA,OAAAqB,SAAArB,EAAA,GAGA,MAAAa,EAAAb,EAAA,MAmDA,SAAA4B,EAAAtJ,EAAA0H,GACA,OAAAA,GAEA,IAAA,SAGA,OAFA6B,EAAAvJ,EAAA0H,GACAO,EAAA,KACA,EAEA,IAAA,UAEA,OADAuB,EAAAxJ,GACA,EAEA,IAAA,OAEA,OADAyJ,EAAAzJ,GACA,EAEA,IAAA,UACA0J,IA+ZA1J,EA/ZAA,EA+ZA0H,EA/ZAA,EAkaA,IAAAP,EAAAnY,KAAA0Y,EAAAK,KACA,MAAAQ,EAAAb,EAAA,gBAEA,IAAAiC,EAAA,IAAA9H,EAAA6F,GApaA,OAqaAkC,EAAAD,EAAA,SAAAjC,GACA,IAAA4B,EAAAK,EAAAjC,GAAA,CAIA,GAAA,QAAAA,EAGA,MAAAa,EAAAb,GAFAmC,IAOA7J,EAPA2J,EAUAG,EAAA5B,IAEA/P,EAAAuP,EAGA,IAAAP,EAAAnY,KAAA0Y,EAAAK,KACA,MAAAQ,EAAAb,EAAA,QAEA,IACAtE,EAAAC,EACAC,EAFA9X,EAAAkc,EASA,GALAO,EAAA,KACAA,EAAA,UAAA,KACA5E,GAAA,IAGA+D,EAAApY,KAAA0Y,EAAAK,KACA,MAAAQ,EAAAb,GAQA,GANAtE,EAAAsE,EACAO,EAAA,KAAAA,EAAA,WAAAA,EAAA,KACAA,EAAA,UAAA,KACA3E,GAAA,IAGA8D,EAAApY,KAAA0Y,EAAAK,KACA,MAAAQ,EAAAb,GAEA9U,EAAA8U,EACAO,EAAA,KAEA,IAAA8B,EAAA,IAAAjI,EAAAtW,EAAA2M,EAAAiL,EAAAxQ,EAAAyQ,EAAAC,GACAyG,EAAAhM,QAAA+L,EACAF,EAAAG,EAAA,SAAArC,GAGA,GAAA,WAAAA,EAIA,MAAAa,EAAAb,GAHA6B,EAAAQ,EAAArC,GACAO,EAAA,OAKAjI,EAAAvB,IAAAsL,MAlDA/J,EAAAvB,IAAAkL,GA/aA,EAEA,IAAA,SACAK,IAieAhK,EAjeAA,EAieA0H,EAjeAA,EAoeA,IAAAN,EAAApY,KAAA0Y,EAAAK,KACA,MAAAQ,EAAAb,EAAA,aAEA,IAAAuC,EAAAvC,EAteA,OAueAkC,EAAA,KAAA,SAAAlC,GACA,OAAAA,GAEA,IAAA,WACA,IAAA,WACAwC,EAAAlK,EAAA0H,EAAAuC,GACA,MAEA,IAAA,WAGAC,EAAAlK,EADAoI,EACA,kBAEA,WAFA6B,GAIA,MAEA,QAEA,IAAA7B,IAAAhB,EAAApY,KAAA0Y,GACA,MAAAa,EAAAb,GACApZ,EAAAoZ,GACAwC,EAAAlK,EAAA,WAAAiK,MA7fA,GAKA,SAAAL,EAAA/F,EAAAsG,EAAAC,GACA,IAQA1C,EARA2C,EAAAxC,EAAAY,KAOA,GANA5E,IACA,iBAAAA,EAAA9F,UACA8F,EAAA9F,QAAAmK,KAEArE,EAAAhS,SAAA2Q,GAAA3Q,UAEAoW,EAAA,KAAA,GAAA,CAEA,KAAA,OAAAP,EAAAK,MACAoC,EAAAzC,GACAO,EAAA,KAAA,QAEAmC,GACAA,IACAnC,EAAA,KACApE,IAAA,iBAAAA,EAAA9F,SAAA6J,KACA/D,EAAA9F,QAAAmK,EAAAmC,IAAAxG,EAAA9F,SAIA,SAAAyL,EAAAxJ,EAAA0H,GAGA,IAAAP,EAAAnY,KAAA0Y,EAAAK,KACA,MAAAQ,EAAAb,EAAA,aAEA,IAAAvP,EAAA,IAAA8G,EAAAyI,GACAkC,EAAAzR,EAAA,SAAAuP,GACA,IAAA4B,EAAAnR,EAAAuP,GAGA,OAAAA,GAEA,IAAA,MACA4C,IAoJAtK,EApJA7H,EAsJAS,GADAqP,EAAA,KACAF,KAGA,GAAA/K,EAAAO,OAAA3E,KAAA1N,GACA,MAAAqd,EAAA3P,EAAA,QAEAqP,EAAA,KACA,IAAAsC,EAAAxC,IAGA,IAAAX,EAAApY,KAAAub,GACA,MAAAhC,EAAAgC,EAAA,QAEAtC,EAAA,KACA,IAAAzc,EAAAuc,IAGA,IAAAZ,EAAAnY,KAAAxD,GACA,MAAA+c,EAAA/c,EAAA,QAEAyc,EAAA,KACA,IAAArN,EAAA,IAAAgH,EAAAyG,EAAA7c,GAAA4d,EAAArB,KAAAnP,EAAA2R,GACAX,EAAAhP,EAAA,SAAA8M,GAGA,GAAA,WAAAA,EAIA,MAAAa,EAAAb,GAHA6B,EAAA3O,EAAA8M,GACAO,EAAA,MAIA,WACAuC,EAAA5P,KAEAoF,EAAAvB,IAAA7D,GAvLA,MAEA,IAAA,WACA,IAAA,WACAsP,EAAA/R,EAAAuP,GACA,MAEA,IAAA,WAGAwC,EAAA/R,EADAiQ,EACA,kBAEA,YAEA,MAEA,IAAA,QA0KApI,EAzKA7H,EAyKAuP,EAzKAA,EA4KA,IAAAP,EAAAnY,KAAA0Y,EAAAK,KACA,MAAAQ,EAAAb,EAAA,QAEA,IAAA1O,EAAA,IAAA2I,EAAA0G,EAAAX,IACAkC,EAAA5Q,EAAA,SAAA0O,GACA,WAAAA,GACA6B,EAAAvQ,EAAA0O,GACAO,EAAA,OAEA3Z,EAAAoZ,GACAwC,EAAAlR,EAAA,eAGAgH,EAAAvB,IAAAzF,GAxLA,MAEA,IAAA,aACAiQ,EAAA9Q,EAAAsS,aAAAtS,EAAAsS,WAAA,KACA,MAEA,IAAA,WACAxB,EAAA9Q,EAAAgG,WAAAhG,EAAAgG,SAAA,KAAA,GACA,MAEA,QAEA,IAAAiK,IAAAhB,EAAApY,KAAA0Y,GACA,MAAAa,EAAAb,GAEApZ,EAAAoZ,GACAwC,EAAA/R,EAAA,eAIA6H,EAAAvB,IAAAtG,GAGA,SAAA+R,EAAAlK,EAAArG,EAAAwF,GACA,IAAAhH,EAAA4P,IACA,GAAA,UAAA5P,EAAA,CACAuS,IAgDA1K,EAhDAA,EAgDArG,EAhDAA,EAiDAnO,EAAAuc,IAGA,IAAAZ,EAAAnY,KAAAxD,GACA,MAAA+c,EAAA/c,EAAA,QAEA,IAAAuV,EAAAnV,EAAA+e,QAAAnf,GAIA4M,GAHA5M,IAAAuV,IACAvV,EAAAI,EAAAgf,QAAApf,IACAyc,EAAA,KACAmB,EAAArB,MACA5P,EAAA,IAAA8G,EAAAzT,GA3DA,OA4DA2M,EAAA2E,OAAA,GAEAlC,EADA,IAAAoE,EAAA+B,EAAA3I,EAAA5M,EAAAmO,IACA9H,SAAA2Q,GAAA3Q,SACA+X,EAAAzR,EAAA,SAAAuP,GACA,OAAAA,GAEA,IAAA,SACA6B,EAAApR,EAAAuP,GACAO,EAAA,KACA,MAEA,IAAA,WACA,IAAA,WACAiC,EAAA/R,EAAAuP,GACA,MAEA,IAAA,WAGAwC,EAAA/R,EADAiQ,EACA,kBAEA,YAEA,MAEA,IAAA,UACAoB,EAAArR,GACA,MAEA,IAAA,OACAsR,EAAAtR,GACA,MAGA,QACA,MAAAoQ,EAAAb,WAGA1H,EAAAvB,IAAAtG,GACAsG,IAAA7D,GA/FA,IAAAwM,EAAApY,KAAAmJ,GACA,MAAAoQ,EAAApQ,EAAA,QAEA3M,EAAAuc,IAGA,IAAAZ,EAAAnY,KAAAxD,GACA,MAAA+c,EAAA/c,EAAA,QAEAA,EAAA6c,EAAA7c,GACAyc,EAAA,KAEA,IAAArN,EAAA,IAAAoE,EAAAxT,EAAA4d,EAAArB,KAAA5P,EAAAwB,EAAAwF,GACAyK,EAAAhP,EAAA,SAAA8M,GAGA,GAAA,WAAAA,EAIA,MAAAa,EAAAb,GAHA6B,EAAA3O,EAAA8M,GACAO,EAAA,MAIA,WACAuC,EAAA5P,KAGA,oBAAAjB,GAEAX,EAAA,IAAA2I,EAAA,IAAAnW,GACAoP,EAAAiF,UAAA,mBAAA,GACA7G,EAAAyF,IAAA7D,GACAoF,EAAAvB,IAAAzF,IAEAgH,EAAAvB,IAAA7D,GAMAwN,IAAAxN,EAAAK,UAAA+B,EAAAG,OAAAhF,KAAAjN,IAAA8R,EAAAE,MAAA/E,KAAAjN,IACA0P,EAAAiF,UAAA,UAAA,GAAA,GAoHA,SAAA4J,EAAAzJ,EAAA0H,GAGA,IAAAP,EAAAnY,KAAA0Y,EAAAK,KACA,MAAAQ,EAAAb,EAAA,QAEA,IAAArJ,EAAA,IAAA5D,EAAAiN,GACAkC,EAAAvL,EAAA,SAAAqJ,GACA,OAAAA,GACA,IAAA,SACA6B,EAAAlL,EAAAqJ,GACAO,EAAA,KACA,MAEA,IAAA,WACAgB,EAAA5K,EAAAF,WAAAE,EAAAF,SAAA,KAAA,GACA,MAEA,QACA0M,IAMA7K,EANA3B,EAMAqJ,EANAA,EASA,IAAAP,EAAAnY,KAAA0Y,GACA,MAAAa,EAAAb,EAAA,QAEAO,EAAA,KACA,IAAAzX,EAAA4Y,EAAArB,KAAA,GACA+C,EAAA,CACAhZ,QAAA5G,GAEA2U,UAAA,SAAArU,EAAAgF,GACAO,KAAAe,UAAA5G,KACA6F,KAAAe,QAAA,IACAf,KAAAe,QAAAtG,GAAAgF,IAEAoZ,EAAAkB,EAAA,SAAApD,GAGA,GAAA,WAAAA,EAIA,MAAAa,EAAAb,GAHA6B,EAAAuB,EAAApD,GACAO,EAAA,MAIA,WACAuC,EAAAM,KAEA9K,EAAAvB,IAAAiJ,EAAAlX,EAAAsa,EAAA/M,QAAA+M,EAAAhZ,YA/BAkO,EAAAvB,IAAAJ,GAkCA,SAAAkL,EAAAvJ,EAAA0H,GACA,IAAAqD,EAAA9C,EAAA,KAAA,GAGA,IAAAb,EAAApY,KAAA0Y,EAAAK,KACA,MAAAQ,EAAAb,EAAA,QAEA,IAEA7B,EAFAra,EAAAkc,EACAsD,EAAAxf,EAeAyf,GAZAF,IACA9C,EAAA,KAEA+C,EADAxf,EAAA,IAAAA,EAAA,IAEAkc,EAAAM,IACAX,GAAArY,KAAA0Y,KACA7B,EAAA6B,EAAAjZ,MAAA,GACAjD,GAAAkc,EACAK,MAGAE,EAAA,KAKA,SAAAiD,EAAAlL,EAAAxU,GAEA,GAAAyc,EAAA,KAAA,GAAA,CAGA,IAFA,IAAAkD,EAAA,IAEAlD,EAAA,KAAA,IAAA,CAEA,IAAAd,EAAAnY,KAAA0Y,EAAAK,KACA,MAAAQ,EAAAb,EAAA,QAGA,IAAAlX,EAYA4a,EAXAvF,EAAA6B,EAIA,GAFAO,EAAA,KAAA,GAEA,MAAAD,IACAxX,EAAA0a,EAAAlL,EAAAxU,EAAA,IAAAkc,QACA,GAAA,MAAAM,KAMA,GAFAxX,EAAA,GAEAyX,EAAA,KAAA,GAAA,CACA,KACAmD,EAAAzC,GAAA,GACAnY,EAAAlC,KAAA8c,GACAnD,EAAA,KAAA,KACAA,EAAA,UACA,IAAAmD,GACAvL,EAAAG,EAAAxU,EAAA,IAAAkc,EAAA0D,SAIA5a,EAAAmY,GAAA,GACA9I,EAAAG,EAAAxU,EAAA,IAAAkc,EAAAlX,GAGA,IAAA6a,EAAAF,EAAAtF,GAEAwF,IACA7a,EAAA,GAAA8a,OAAAD,GAAAC,OAAA9a,IAEA2a,EAAAtF,GAAArV,EAGAyX,EAAA,KAAA,GACAA,EAAA,KAAA,GAGA,OAAAkD,EAGA,IAAAI,EAAA5C,GAAA,GACA9I,EAAAG,EAAAxU,EAAA+f,GACA,OAAAA,EA5DAL,CAAAlL,EAAAxU,IAqEAA,EApEAwf,EAoEAxa,EApEAya,EAoEApF,EApEAA,GAoEA7F,EApEAA,GAqEA4F,iBACA5F,EAAA4F,gBAAApa,EAAAgF,EAAAqV,GAPA,SAAAhG,EAAAG,EAAAxU,EAAAgF,GACAwP,EAAAH,WACAG,EAAAH,UAAArU,EAAAgF,GAQA,SAAAga,EAAAxK,GACA,GAAAiI,EAAA,KAAA,GAAA,CACA,KACAsB,EAAAvJ,EAAA,UACAiI,EAAA,KAAA,KACAA,EAAA,MA+GA,KAAA,QAAAP,EAAAK,MACA,OAAAL,GAEA,IAAA,UAGA,IAAAS,EACA,MAAAI,EAAAb,GA9kBA,GAAAJ,IAAApc,GACA,MAAAqd,EAAA,WAKA,GAHAjB,EAAAS,KAGAX,EAAApY,KAAAsY,GACA,MAAAiB,EAAAjB,EAAA,QAEAzC,EAAAA,EAAA/Y,OAAAwb,GACAW,EAAA,KAukBA,MAEA,IAAA,SAGA,IAAAE,EACA,MAAAI,EAAAb,GAvkBA,OADAC,EADAD,OAAAA,EAAAM,KAGA,IAAA,OACAL,EAAAH,EAAAA,GAAA,GACAO,IACA,MACA,IAAA,SACAA,IAEA,QACAJ,EAAAJ,EAAAA,GAAA,GAGAG,EAAAgB,IACAT,EAAA,KACAN,EAAArZ,KAAAoZ,GA4jBA,MAEA,IAAA,SAGA,IAAAS,EACA,MAAAI,EAAAb,GAzjBA,GALAO,EAAA,KACAR,EAAAiB,MACAN,EAAA,WAAAX,IAGA,WAAAA,EACA,MAAAc,EAAAd,EAAA,UAEAQ,EAAA,KAyjBA,MAEA,IAAA,SAEAsB,EAAA1E,EAAA6C,GACAO,EAAA,KACA,MAEA,QAGA,GAAAqB,EAAAzE,EAAA6C,GAAA,CACAS,GAAA,EACA,SAIA,MAAAI,EAAAb,GAKA,OADAlF,GAAA3Q,SAAA,KACA,CACA2Z,QAAAlE,EACAC,QAAAA,EACAC,YAAAA,EACAC,OAAAA,EACApG,KAAAA,K,yFC30BAlV,EAAAR,QAAAwW,EAEA,IAEAC,EAFAxW,EAAAS,EAAA,IAIAof,EAAA7f,EAAA6f,SACApU,EAAAzL,EAAAyL,KAGA,SAAAqU,EAAA1I,EAAA2I,GACA,OAAAC,WAAA,uBAAA5I,EAAA5P,IAAA,OAAAuY,GAAA,GAAA,MAAA3I,EAAA1L,KASA,SAAA6K,EAAArU,GAMAiD,KAAAoC,IAAArF,EAMAiD,KAAAqC,IAAA,EAMArC,KAAAuG,IAAAxJ,EAAAnB,OAgBA,SAAAiR,IACA,OAAAhS,EAAAigB,OACA,SAAA/d,GACA,OAAAqU,EAAAvE,OAAA,SAAA9P,GACA,OAAAlC,EAAAigB,OAAAC,SAAAhe,GACA,IAAAsU,EAAAtU,GAEAie,EAAAje,KACAA,IAGAie,EAxBA,IA4CAvb,EA5CAub,EAAA,oBAAArZ,WACA,SAAA5E,GACA,GAAAA,aAAA4E,YAAAjG,MAAAmY,QAAA9W,GACA,OAAA,IAAAqU,EAAArU,GACA,MAAAiB,MAAA,mBAGA,SAAAjB,GACA,GAAArB,MAAAmY,QAAA9W,GACA,OAAA,IAAAqU,EAAArU,GACA,MAAAiB,MAAA,mBAsEA,SAAAid,IAEA,IAAAC,EAAA,IAAAR,EAAA,EAAA,GACA7d,EAAA,EACA,KAAA,EAAAmD,KAAAuG,IAAAvG,KAAAqC,KAaA,CACA,KAAAxF,EAAA,IAAAA,EAAA,CAEA,GAAAmD,KAAAqC,KAAArC,KAAAuG,IACA,MAAAoU,EAAA3a,MAGA,GADAkb,EAAApX,IAAAoX,EAAApX,IAAA,IAAA9D,KAAAoC,IAAApC,KAAAqC,OAAA,EAAAxF,KAAA,EACAmD,KAAAoC,IAAApC,KAAAqC,OAAA,IACA,OAAA6Y,EAIA,OADAA,EAAApX,IAAAoX,EAAApX,IAAA,IAAA9D,KAAAoC,IAAApC,KAAAqC,SAAA,EAAAxF,KAAA,EACAqe,EAxBA,KAAAre,EAAA,IAAAA,EAGA,GADAqe,EAAApX,IAAAoX,EAAApX,IAAA,IAAA9D,KAAAoC,IAAApC,KAAAqC,OAAA,EAAAxF,KAAA,EACAmD,KAAAoC,IAAApC,KAAAqC,OAAA,IACA,OAAA6Y,EAKA,GAFAA,EAAApX,IAAAoX,EAAApX,IAAA,IAAA9D,KAAAoC,IAAApC,KAAAqC,OAAA,MAAA,EACA6Y,EAAAnX,IAAAmX,EAAAnX,IAAA,IAAA/D,KAAAoC,IAAApC,KAAAqC,OAAA,KAAA,EACArC,KAAAoC,IAAApC,KAAAqC,OAAA,IACA,OAAA6Y,EAgBA,GAfAre,EAAA,EAeA,EAAAmD,KAAAuG,IAAAvG,KAAAqC,KACA,KAAAxF,EAAA,IAAAA,EAGA,GADAqe,EAAAnX,IAAAmX,EAAAnX,IAAA,IAAA/D,KAAAoC,IAAApC,KAAAqC,OAAA,EAAAxF,EAAA,KAAA,EACAmD,KAAAoC,IAAApC,KAAAqC,OAAA,IACA,OAAA6Y,OAGA,KAAAre,EAAA,IAAAA,EAAA,CAEA,GAAAmD,KAAAqC,KAAArC,KAAAuG,IACA,MAAAoU,EAAA3a,MAGA,GADAkb,EAAAnX,IAAAmX,EAAAnX,IAAA,IAAA/D,KAAAoC,IAAApC,KAAAqC,OAAA,EAAAxF,EAAA,KAAA,EACAmD,KAAAoC,IAAApC,KAAAqC,OAAA,IACA,OAAA6Y,EAIA,MAAAld,MAAA,2BAkCA,SAAAmd,EAAA/Y,EAAAnF,GACA,OAAAmF,EAAAnF,EAAA,GACAmF,EAAAnF,EAAA,IAAA,EACAmF,EAAAnF,EAAA,IAAA,GACAmF,EAAAnF,EAAA,IAAA,MAAA,EA+BA,SAAAme,IAGA,GAAApb,KAAAqC,IAAA,EAAArC,KAAAuG,IACA,MAAAoU,EAAA3a,KAAA,GAEA,OAAA,IAAA0a,EAAAS,EAAAnb,KAAAoC,IAAApC,KAAAqC,KAAA,GAAA8Y,EAAAnb,KAAAoC,IAAApC,KAAAqC,KAAA,IA3KA+O,EAAAvE,OAAAA,IAEAuE,EAAAlR,UAAAmb,EAAAxgB,EAAAa,MAAAwE,UAAAob,UAAAzgB,EAAAa,MAAAwE,UAAAxC,MAOA0T,EAAAlR,UAAAqb,QACA9b,EAAA,WACA,WACA,GAAAA,GAAA,IAAAO,KAAAoC,IAAApC,KAAAqC,QAAA,EAAArC,KAAAoC,IAAApC,KAAAqC,OAAA,IAAA,OAAA5C,EACA,GAAAA,GAAAA,GAAA,IAAAO,KAAAoC,IAAApC,KAAAqC,OAAA,KAAA,EAAArC,KAAAoC,IAAApC,KAAAqC,OAAA,IAAA,OAAA5C,EACA,GAAAA,GAAAA,GAAA,IAAAO,KAAAoC,IAAApC,KAAAqC,OAAA,MAAA,EAAArC,KAAAoC,IAAApC,KAAAqC,OAAA,IAAA,OAAA5C,EACA,GAAAA,GAAAA,GAAA,IAAAO,KAAAoC,IAAApC,KAAAqC,OAAA,MAAA,EAAArC,KAAAoC,IAAApC,KAAAqC,OAAA,IAAA,OAAA5C,EACA,GAAAA,GAAAA,GAAA,GAAAO,KAAAoC,IAAApC,KAAAqC,OAAA,MAAA,EAAArC,KAAAoC,IAAApC,KAAAqC,OAAA,IAAA,OAAA5C,EAGA,IAAAO,KAAAqC,KAAA,GAAArC,KAAAuG,IAEA,MADAvG,KAAAqC,IAAArC,KAAAuG,IACAoU,EAAA3a,KAAA,IAEA,OAAAP,IAQA2R,EAAAlR,UAAAsb,MAAA,WACA,OAAA,EAAAxb,KAAAub,UAOAnK,EAAAlR,UAAAub,OAAA,WACA,IAAAhc,EAAAO,KAAAub,SACA,OAAA9b,IAAA,IAAA,EAAAA,GAAA,GAqFA2R,EAAAlR,UAAAwb,KAAA,WACA,OAAA,IAAA1b,KAAAub,UAcAnK,EAAAlR,UAAAyb,QAAA,WAGA,GAAA3b,KAAAqC,IAAA,EAAArC,KAAAuG,IACA,MAAAoU,EAAA3a,KAAA,GAEA,OAAAmb,EAAAnb,KAAAoC,IAAApC,KAAAqC,KAAA,IAOA+O,EAAAlR,UAAA0b,SAAA,WAGA,GAAA5b,KAAAqC,IAAA,EAAArC,KAAAuG,IACA,MAAAoU,EAAA3a,KAAA,GAEA,OAAA,EAAAmb,EAAAnb,KAAAoC,IAAApC,KAAAqC,KAAA,IAmCA+O,EAAAlR,UAAA2b,MAAA,WAGA,GAAA7b,KAAAqC,IAAA,EAAArC,KAAAuG,IACA,MAAAoU,EAAA3a,KAAA,GAEA,IAAAP,EAAA5E,EAAAghB,MAAAtX,YAAAvE,KAAAoC,IAAApC,KAAAqC,KAEA,OADArC,KAAAqC,KAAA,EACA5C,GAQA2R,EAAAlR,UAAA4b,OAAA,WAGA,GAAA9b,KAAAqC,IAAA,EAAArC,KAAAuG,IACA,MAAAoU,EAAA3a,KAAA,GAEA,IAAAP,EAAA5E,EAAAghB,MAAA5W,aAAAjF,KAAAoC,IAAApC,KAAAqC,KAEA,OADArC,KAAAqC,KAAA,EACA5C,GAOA2R,EAAAlR,UAAAwL,MAAA,WACA,IAAA9P,EAAAoE,KAAAub,SACAve,EAAAgD,KAAAqC,IACApF,EAAA+C,KAAAqC,IAAAzG,EAGA,GAAAqB,EAAA+C,KAAAuG,IACA,MAAAoU,EAAA3a,KAAApE,GAGA,OADAoE,KAAAqC,KAAAzG,EACAF,MAAAmY,QAAA7T,KAAAoC,KACApC,KAAAoC,IAAA1E,MAAAV,EAAAC,GACAD,IAAAC,EACA,IAAA+C,KAAAoC,IAAA0K,YAAA,GACA9M,KAAAqb,EAAA1gB,KAAAqF,KAAAoC,IAAApF,EAAAC,IAOAmU,EAAAlR,UAAA5D,OAAA,WACA,IAAAoP,EAAA1L,KAAA0L,QACA,OAAApF,EAAAE,KAAAkF,EAAA,EAAAA,EAAA9P,SAQAwV,EAAAlR,UAAAgX,KAAA,SAAAtb,GACA,GAAA,iBAAAA,EAAA,CAEA,GAAAoE,KAAAqC,IAAAzG,EAAAoE,KAAAuG,IACA,MAAAoU,EAAA3a,KAAApE,GACAoE,KAAAqC,KAAAzG,OAEA,GAEA,GAAAoE,KAAAqC,KAAArC,KAAAuG,IACA,MAAAoU,EAAA3a,YACA,IAAAA,KAAAoC,IAAApC,KAAAqC,QAEA,OAAArC,MAQAoR,EAAAlR,UAAA6b,SAAA,SAAAxP,GACA,OAAAA,GACA,KAAA,EACAvM,KAAAkX,OACA,MACA,KAAA,EACAlX,KAAAkX,KAAA,GACA,MACA,KAAA,EACAlX,KAAAkX,KAAAlX,KAAAub,UACA,MACA,KAAA,EACA,KAAA,IAAAhP,EAAA,EAAAvM,KAAAub,WACAvb,KAAA+b,SAAAxP,GAEA,MACA,KAAA,EACAvM,KAAAkX,KAAA,GACA,MAGA,QACA,MAAAlZ,MAAA,qBAAAuO,EAAA,cAAAvM,KAAAqC,KAEA,OAAArC,MAGAoR,EAAAlB,EAAA,SAAA8L,GACA3K,EAAA2K,EACA5K,EAAAvE,OAAAA,IACAwE,EAAAnB,IAEA,IAAA3U,EAAAV,EAAAI,KAAA,SAAA,WACAJ,EAAAohB,MAAA7K,EAAAlR,UAAA,CAEAgc,MAAA,WACA,OAAAjB,EAAAtgB,KAAAqF,MAAAzE,IAAA,IAGA4gB,OAAA,WACA,OAAAlB,EAAAtgB,KAAAqF,MAAAzE,IAAA,IAGA6gB,OAAA,WACA,OAAAnB,EAAAtgB,KAAAqF,MAAAqc,WAAA9gB,IAAA,IAGA+gB,QAAA,WACA,OAAAlB,EAAAzgB,KAAAqF,MAAAzE,IAAA,IAGAghB,SAAA,WACA,OAAAnB,EAAAzgB,KAAAqF,MAAAzE,IAAA,Q,6BCrZAH,EAAAR,QAAAyW,EAGA,IAAAD,EAAA9V,EAAA,IAGAT,IAFAwW,EAAAnR,UAAApB,OAAA+N,OAAAuE,EAAAlR,YAAA4M,YAAAuE,EAEA/V,EAAA,KASA,SAAA+V,EAAAtU,GACAqU,EAAAzW,KAAAqF,KAAAjD,GASAsU,EAAAnB,EAAA,WAEArV,EAAAigB,SACAzJ,EAAAnR,UAAAmb,EAAAxgB,EAAAigB,OAAA5a,UAAAxC,QAOA2T,EAAAnR,UAAA5D,OAAA,WACA,IAAAiK,EAAAvG,KAAAub,SACA,OAAAvb,KAAAoC,IAAAoa,UACAxc,KAAAoC,IAAAoa,UAAAxc,KAAAqC,IAAArC,KAAAqC,IAAA5F,KAAAggB,IAAAzc,KAAAqC,IAAAkE,EAAAvG,KAAAuG,MACAvG,KAAAoC,IAAA3D,SAAA,QAAAuB,KAAAqC,IAAArC,KAAAqC,IAAA5F,KAAAggB,IAAAzc,KAAAqC,IAAAkE,EAAAvG,KAAAuG,OAUA8K,EAAAnB,K,mCCjDA9U,EAAAR,QAAA2V,EAGA,IAQArC,EACAuD,EACA7K,EAVAgG,EAAAtR,EAAA,IAGA2S,KAFAsC,EAAArQ,UAAApB,OAAA+N,OAAAD,EAAA1M,YAAA4M,YAAAyD,GAAAxD,UAAA,OAEAzR,EAAA,KACAoO,EAAApO,EAAA,IACAsV,EAAAtV,EAAA,IACAT,EAAAS,EAAA,IAaA,SAAAiV,EAAAxP,GACA6L,EAAAjS,KAAAqF,KAAA,GAAAe,GAMAf,KAAA0c,SAAA,GAMA1c,KAAA2c,MAAA,GAuCA,SAAAC,KA9BArM,EAAAlD,SAAA,SAAAvG,EAAAwJ,GAKA,OAHAA,EADAA,GACA,IAAAC,EACAzJ,EAAA/F,SACAuP,EAAAoD,WAAA5M,EAAA/F,SACAuP,EAAA4C,QAAApM,EAAAC,SAWAwJ,EAAArQ,UAAA2c,YAAAhiB,EAAA2K,KAAAvJ,QAUAsU,EAAArQ,UAAAQ,MAAA7F,EAAA6F,MAaA6P,EAAArQ,UAAAmQ,KAAA,SAAAA,EAAAvP,EAAAC,EAAAC,GACA,mBAAAD,IACAC,EAAAD,EACAA,EAAA5G,IAEA,IAAA2iB,EAAA9c,KACA,IAAAgB,EACA,OAAAnG,EAAA8F,UAAA0P,EAAAyM,EAAAhc,EAAAC,GAEA,IAAAgc,EAAA/b,IAAA4b,EAGA,SAAAI,EAAA7gB,EAAAmU,GAEA,GAAAtP,EAAA,CAEA,IAAAic,EAAAjc,EAEA,GADAA,EAAA,KACA+b,EACA,MAAA5gB,EACA8gB,EAAA9gB,EAAAmU,IAIA,SAAA4M,EAAApc,GACA,IAAAqc,EAAArc,EAAAsc,YAAA,oBACA,IAAA,EAAAD,EAAA,CACAE,EAAAvc,EAAAiX,UAAAoF,GACA,GAAAE,KAAAzW,EAAA,OAAAyW,EAEA,OAAA,KAIA,SAAAC,EAAAxc,EAAAtC,GACA,IAGA,GAFA3D,EAAA8S,SAAAnP,IAAA,MAAAA,EAAA,IAAAA,MACAA,EAAAoB,KAAA6R,MAAAjT,IACA3D,EAAA8S,SAAAnP,GAEA,CACAiT,EAAA3Q,SAAAA,EACA,IACAkO,EADAuO,EAAA9L,EAAAjT,EAAAse,EAAA/b,GAEAlE,EAAA,EACA,GAAA0gB,EAAA/G,QACA,KAAA3Z,EAAA0gB,EAAA/G,QAAA5a,SAAAiB,GACAmS,EAAAkO,EAAAK,EAAA/G,QAAA3Z,KAAAigB,EAAAD,YAAA/b,EAAAyc,EAAA/G,QAAA3Z,MACA6D,EAAAsO,GACA,GAAAuO,EAAA9G,YACA,IAAA5Z,EAAA,EAAAA,EAAA0gB,EAAA9G,YAAA7a,SAAAiB,GACAmS,EAAAkO,EAAAK,EAAA9G,YAAA5Z,KAAAigB,EAAAD,YAAA/b,EAAAyc,EAAA9G,YAAA5Z,MACA6D,EAAAsO,GAAA,QAbA8N,EAAApJ,WAAAlV,EAAAuC,SAAAmS,QAAA1U,EAAAuI,QAeA,MAAA5K,GACA6gB,EAAA7gB,GAEA4gB,GAAAS,GACAR,EAAA,KAAAF,GAIA,SAAApc,EAAAI,EAAA2c,GAGA,KAAAX,EAAAH,MAAA9Q,QAAA/K,GAKA,GAHAgc,EAAAH,MAAApf,KAAAuD,GAGAA,KAAA8F,EACAmW,EACAO,EAAAxc,EAAA8F,EAAA9F,OAEA0c,EACAE,WAAA,aACAF,EACAF,EAAAxc,EAAA8F,EAAA9F,YAOA,GAAAic,EAAA,CACA,IAAAve,EACA,IACAA,EAAA3D,EAAA+F,GAAA+c,aAAA7c,GAAArC,SAAA,QACA,MAAAtC,GAGA,YAFAshB,GACAT,EAAA7gB,IAGAmhB,EAAAxc,EAAAtC,SAEAgf,EACAV,EAAApc,MAAAI,EAAA,SAAA3E,EAAAqC,KACAgf,EAEAxc,IAEA7E,EAEAshB,EAEAD,GACAR,EAAA,KAAAF,GAFAE,EAAA7gB,GAKAmhB,EAAAxc,EAAAtC,MAIA,IAAAgf,EAAA,EAIA3iB,EAAA8S,SAAA7M,KACAA,EAAA,CAAAA,IACA,IAAA,IAAAkO,EAAAnS,EAAA,EAAAA,EAAAiE,EAAAlF,SAAAiB,GACAmS,EAAA8N,EAAAD,YAAA,GAAA/b,EAAAjE,MACA6D,EAAAsO,GAEA,OAAA+N,EACAD,GACAU,GACAR,EAAA,KAAAF,GACA3iB,KAgCAoW,EAAArQ,UAAAsQ,SAAA,SAAA1P,EAAAC,GACA,GAAAlG,EAAA+iB,OAEA,OAAA5d,KAAAqQ,KAAAvP,EAAAC,EAAA6b,GADA,MAAA5e,MAAA,kBAOAuS,EAAArQ,UAAA8T,WAAA,WACA,GAAAhU,KAAA0c,SAAA9gB,OACA,MAAAoC,MAAA,4BAAAgC,KAAA0c,SAAA/R,IAAA,SAAAd,GACA,MAAA,WAAAA,EAAAuE,OAAA,QAAAvE,EAAAoF,OAAA9E,WACAxM,KAAA,OACA,OAAAiP,EAAA1M,UAAA8T,WAAArZ,KAAAqF,OAIA,IAAA6d,EAAA,SAUA,SAAAC,EAAAxN,EAAAzG,GACA,IAEAkU,EAFAC,EAAAnU,EAAAoF,OAAAgF,OAAApK,EAAAuE,QACA,GAAA4P,EAKA,QAJAD,EAAA,IAAA9P,EAAApE,EAAAM,SAAAN,EAAAxC,GAAAwC,EAAAzC,KAAAyC,EAAAjB,KAAAzO,GAAA0P,EAAA9I,UACA2N,eAAA7E,GACA4E,eAAAsP,EACAC,EAAAtQ,IAAAqQ,GACA,EAWAxN,EAAArQ,UAAAyU,EAAA,SAAAvC,GACA,GAAAA,aAAAnE,EAEAmE,EAAAhE,SAAAjU,IAAAiY,EAAA3D,gBACAqP,EAAA9d,EAAAoS,IACApS,KAAA0c,SAAAnf,KAAA6U,QAEA,GAAAA,aAAA1I,EAEAmU,EAAA5f,KAAAmU,EAAA3X,QACA2X,EAAAnD,OAAAmD,EAAA3X,MAAA2X,EAAA3J,aAEA,KAAA2J,aAAAxB,GAAA,CAEA,GAAAwB,aAAAlE,EACA,IAAA,IAAArR,EAAA,EAAAA,EAAAmD,KAAA0c,SAAA9gB,QACAkiB,EAAA9d,EAAAA,KAAA0c,SAAA7f,IACAmD,KAAA0c,SAAAnc,OAAA1D,EAAA,KAEAA,EACA,IAAA,IAAAQ,EAAA,EAAAA,EAAA+U,EAAAgB,YAAAxX,SAAAyB,EACA2C,KAAA2U,EAAAvC,EAAAW,EAAA1V,IACAwgB,EAAA5f,KAAAmU,EAAA3X,QACA2X,EAAAnD,OAAAmD,EAAA3X,MAAA2X,KAcA7B,EAAArQ,UAAA0U,EAAA,SAAAxC,GAGA,IAKAtW,EAPA,GAAAsW,aAAAnE,EAEAmE,EAAAhE,SAAAjU,KACAiY,EAAA3D,gBACA2D,EAAA3D,eAAAQ,OAAAjB,OAAAoE,EAAA3D,gBACA2D,EAAA3D,eAAA,OAIA,GAFA3S,EAAAkE,KAAA0c,SAAA7Q,QAAAuG,KAGApS,KAAA0c,SAAAnc,OAAAzE,EAAA,SAIA,GAAAsW,aAAA1I,EAEAmU,EAAA5f,KAAAmU,EAAA3X,cACA2X,EAAAnD,OAAAmD,EAAA3X,WAEA,GAAA2X,aAAAxF,EAAA,CAEA,IAAA,IAAA/P,EAAA,EAAAA,EAAAuV,EAAAgB,YAAAxX,SAAAiB,EACAmD,KAAA4U,EAAAxC,EAAAW,EAAAlW,IAEAghB,EAAA5f,KAAAmU,EAAA3X,cACA2X,EAAAnD,OAAAmD,EAAA3X,QAMA8V,EAAAL,EAAA,SAAAC,EAAA8N,EAAAC,GACAhQ,EAAAiC,EACAsB,EAAAwM,EACArX,EAAAsX,I,qDCxWA9iB,EAAAR,QAAA,I,wBCKAA,EA6BAkW,QAAAxV,EAAA,K,6BClCAF,EAAAR,QAAAkW,EAEA,IAAAjW,EAAAS,EAAA,IAsCA,SAAAwV,EAAAqN,EAAAC,EAAAC,GAEA,GAAA,mBAAAF,EACA,MAAAhR,UAAA,8BAEAtS,EAAAkF,aAAApF,KAAAqF,MAMAA,KAAAme,QAAAA,EAMAne,KAAAoe,mBAAAA,EAMApe,KAAAqe,oBAAAA,IA1DAvN,EAAA5Q,UAAApB,OAAA+N,OAAAhS,EAAAkF,aAAAG,YAAA4M,YAAAgE,GAwEA5Q,UAAAoe,QAAA,SAAAA,EAAAtF,EAAAuF,EAAAC,EAAAC,EAAAzd,GAEA,IAAAyd,EACA,MAAAtR,UAAA,6BAEA,IAAA2P,EAAA9c,KACA,IAAAgB,EACA,OAAAnG,EAAA8F,UAAA2d,EAAAxB,EAAA9D,EAAAuF,EAAAC,EAAAC,GAEA,IAAA3B,EAAAqB,QAEA,OADAT,WAAA,WAAA1c,EAAAhD,MAAA,mBAAA,GACA7D,GAGA,IACA,OAAA2iB,EAAAqB,QACAnF,EACAuF,EAAAzB,EAAAsB,iBAAA,kBAAA,UAAAK,GAAAzB,SACA,SAAA7gB,EAAAsF,GAEA,GAAAtF,EAEA,OADA2gB,EAAAtc,KAAA,QAAArE,EAAA6c,GACAhY,EAAA7E,GAGA,GAAA,OAAAsF,EAEA,OADAqb,EAAA7f,KAAA,GACA9C,GAGA,KAAAsH,aAAA+c,GACA,IACA/c,EAAA+c,EAAA1B,EAAAuB,kBAAA,kBAAA,UAAA5c,GACA,MAAAtF,GAEA,OADA2gB,EAAAtc,KAAA,QAAArE,EAAA6c,GACAhY,EAAA7E,GAKA,OADA2gB,EAAAtc,KAAA,OAAAiB,EAAAuX,GACAhY,EAAA,KAAAS,KAGA,MAAAtF,GAGA,OAFA2gB,EAAAtc,KAAA,QAAArE,EAAA6c,GACA0E,WAAA,WAAA1c,EAAA7E,IAAA,GACAhC,KASA2W,EAAA5Q,UAAAjD,IAAA,SAAAyhB,GAOA,OANA1e,KAAAme,UACAO,GACA1e,KAAAme,QAAA,KAAA,KAAA,MACAne,KAAAme,QAAA,KACAne,KAAAQ,KAAA,OAAAH,OAEAL,O,6BC3IA5E,EAAAR,QAAAkW,EAGA,IAAAlE,EAAAtR,EAAA,IAGAyV,KAFAD,EAAA5Q,UAAApB,OAAA+N,OAAAD,EAAA1M,YAAA4M,YAAAgE,GAAA/D,UAAA,UAEAzR,EAAA,KACAT,EAAAS,EAAA,IACAgW,EAAAhW,EAAA,IAWA,SAAAwV,EAAArW,EAAAsG,GACA6L,EAAAjS,KAAAqF,KAAAvF,EAAAsG,GAMAf,KAAAuT,QAAA,GAOAvT,KAAA2e,EAAA,KAyDA,SAAA3L,EAAA4F,GAEA,OADAA,EAAA+F,EAAA,KACA/F,EA1CA9H,EAAAzD,SAAA,SAAA5S,EAAAqM,GACA,IAAA8R,EAAA,IAAA9H,EAAArW,EAAAqM,EAAA/F,SAEA,GAAA+F,EAAAyM,QACA,IAAA,IAAAD,EAAAxU,OAAAC,KAAA+H,EAAAyM,SAAA1W,EAAA,EAAAA,EAAAyW,EAAA1X,SAAAiB,EACA+b,EAAAlL,IAAAqD,EAAA1D,SAAAiG,EAAAzW,GAAAiK,EAAAyM,QAAAD,EAAAzW,MAIA,OAHAiK,EAAAC,QACA6R,EAAA1F,QAAApM,EAAAC,QACA6R,EAAA5L,QAAAlG,EAAAkG,QACA4L,GAQA9H,EAAA5Q,UAAAqN,OAAA,SAAAC,GACA,IAAAoR,EAAAhS,EAAA1M,UAAAqN,OAAA5S,KAAAqF,KAAAwN,GACAC,IAAAD,KAAAA,EAAAC,aACA,OAAA5S,EAAA+P,SAAA,CACA,UAAAgU,GAAAA,EAAA7d,SAAA5G,GACA,UAAAyS,EAAAgG,YAAA5S,KAAA6e,aAAArR,IAAA,GACA,SAAAoR,GAAAA,EAAA7X,QAAA5M,GACA,UAAAsT,EAAAzN,KAAAgN,QAAA7S,MAUA2E,OAAA8P,eAAAkC,EAAA5Q,UAAA,eAAA,CACAsJ,IAAA,WACA,OAAAxJ,KAAA2e,IAAA3e,KAAA2e,EAAA9jB,EAAAsY,QAAAnT,KAAAuT,aAYAzC,EAAA5Q,UAAAsJ,IAAA,SAAA/O,GACA,OAAAuF,KAAAuT,QAAA9Y,IACAmS,EAAA1M,UAAAsJ,IAAA7O,KAAAqF,KAAAvF,IAMAqW,EAAA5Q,UAAA8T,WAAA,WAEA,IADA,IAAAT,EAAAvT,KAAA6e,aACAhiB,EAAA,EAAAA,EAAA0W,EAAA3X,SAAAiB,EACA0W,EAAA1W,GAAAZ,UACA,OAAA2Q,EAAA1M,UAAAjE,QAAAtB,KAAAqF,OAMA8Q,EAAA5Q,UAAAwN,IAAA,SAAA0E,GAGA,GAAApS,KAAAwJ,IAAA4I,EAAA3X,MACA,MAAAuD,MAAA,mBAAAoU,EAAA3X,KAAA,QAAAuF,MAEA,OAAAoS,aAAArB,EAGAiC,GAFAhT,KAAAuT,QAAAnB,EAAA3X,MAAA2X,GACAnD,OAAAjP,MAGA4M,EAAA1M,UAAAwN,IAAA/S,KAAAqF,KAAAoS,IAMAtB,EAAA5Q,UAAA8N,OAAA,SAAAoE,GACA,GAAAA,aAAArB,EAAA,CAGA,GAAA/Q,KAAAuT,QAAAnB,EAAA3X,QAAA2X,EACA,MAAApU,MAAAoU,EAAA,uBAAApS,MAIA,cAFAA,KAAAuT,QAAAnB,EAAA3X,MACA2X,EAAAnD,OAAA,KACA+D,EAAAhT,MAEA,OAAA4M,EAAA1M,UAAA8N,OAAArT,KAAAqF,KAAAoS,IAUAtB,EAAA5Q,UAAA2M,OAAA,SAAAsR,EAAAC,EAAAC,GAEA,IADA,IACArF,EADA8F,EAAA,IAAAxN,EAAAR,QAAAqN,EAAAC,EAAAC,GACAxhB,EAAA,EAAAA,EAAAmD,KAAA6e,aAAAjjB,SAAAiB,EAAA,CACA,IAAAkiB,EAAAlkB,EAAA+e,SAAAZ,EAAAhZ,KAAA2e,EAAA9hB,IAAAZ,UAAAxB,MAAA6E,QAAA,WAAA,IACAwf,EAAAC,GAAAlkB,EAAAqD,QAAA,CAAA,IAAA,KAAArD,EAAAmkB,WAAAD,GAAAA,EAAA,IAAAA,EAAAlkB,CAAA,iCAAAA,CAAA,CACAokB,EAAAjG,EACAkG,EAAAlG,EAAAvG,oBAAAhD,KACA0P,EAAAnG,EAAAtG,qBAAAjD,OAGA,OAAAqP,I,+CCpKA1jB,EAAAR,QAAA4W,EAEA,IAAA4N,EAAA,uBACAC,EAAA,kCACAC,EAAA,kCAEAC,EAAA,aACAC,EAAA,aACAC,EAAA,MACAC,EAAA,KACAC,EAAA,UAEAC,EAAA,CACAC,EAAA,KACAC,EAAA,KACAtjB,EAAA,KACAU,EAAA,MAUA,SAAA6iB,EAAAC,GACA,OAAAA,EAAA1gB,QAAAqgB,EAAA,SAAApgB,EAAAC,GACA,OAAAA,GACA,IAAA,KACA,IAAA,GACA,OAAAA,EACA,QACA,OAAAogB,EAAApgB,IAAA,MAgEA,SAAAgS,EAAAhT,EAAAuY,GAEAvY,EAAAA,EAAAC,WAEA,IAAA5C,EAAA,EACAD,EAAA4C,EAAA5C,OACA8b,EAAA,EACAuI,EAAA,EACAhT,EAAA,GAEAiT,EAAA,GAEAC,EAAA,KASA,SAAA3I,EAAA4I,GACA,OAAApiB,MAAA,WAAAoiB,EAAA,UAAA1I,EAAA,KA0BA,SAAA2I,EAAAhe,GACA,OAAA7D,EAAAA,EAAA6D,IAAA7D,GAWA,SAAA8hB,EAAAtjB,EAAAC,EAAAsjB,GACA,IAYAziB,EAZAkP,EAAA,CACA5F,KAAA5I,EAAAA,EAAAxB,MAAAwB,GACAgiB,WAAA,EACAC,QAAAF,GAIAG,EADA3J,EACA,EAEA,EAEA4J,EAAA3jB,EAAA0jB,EAEA,GACA,KAAAC,EAAA,GACA,OAAA7iB,EAAAU,EAAAA,EAAAmiB,IAAAniB,IAAA,CACAwO,EAAAwT,WAAA,EACA,aAEA,MAAA1iB,GAAA,OAAAA,GAIA,IAHA,IAAA8iB,EAAApiB,EACAuZ,UAAA/a,EAAAC,GACAyI,MAAA+Z,GACA5iB,EAAA,EAAAA,EAAA+jB,EAAAhlB,SAAAiB,EACA+jB,EAAA/jB,GAAA+jB,EAAA/jB,GACAyC,QAAAyX,EAAAyI,EAAAD,EAAA,IACAsB,OACA7T,EAAA8T,KAAAF,EACAjjB,KAAA,MACAkjB,OAEA5T,EAAAyK,GAAA1K,EACAiT,EAAAvI,EAGA,SAAAqJ,EAAAC,GACA,IAAAC,EAAAC,EAAAF,GAGAG,EAAA3iB,EAAAuZ,UAAAiJ,EAAAC,GAIA,MADA,cAAAhjB,KAAAkjB,GAIA,SAAAD,EAAAE,GAGA,IADA,IAAAH,EAAAG,EACAH,EAAArlB,GAAA,OAAAykB,EAAAY,IACAA,IAEA,OAAAA,EAQA,SAAAjK,IACA,GAAA,EAAAkJ,EAAAtkB,OACA,OAAAskB,EAAAra,QACA,GAAAsa,EAAA,CA7FA,IAAAkB,EAAA,MAAAlB,EAAAb,EAAAD,EAEAiC,GADAD,EAAAE,UAAA1lB,EAAA,EACAwlB,EAAAG,KAAAhjB,IACA,GAAA8iB,EAKA,OAHAzlB,EAAAwlB,EAAAE,UACAhkB,EAAA4iB,GACAA,EAAA,KACAJ,EAAAuB,EAAA,IAJA,MAAA9J,EAAA,UA2FA,IAAAiK,EACAhO,EACAiO,EACA1kB,EACA2kB,EACAC,EAAA,IAAA/lB,EACA,EAAA,CACA,GAAAA,IAAAD,EACA,OAAA,KAEA,IADA6lB,GAAA,EACA/B,EAAAzhB,KAAAyjB,EAAArB,EAAAxkB,KAKA,GAJA,OAAA6lB,IACAE,GAAA,IACAlK,KAEA7b,IAAAD,EACA,OAAA,KAGA,GAAA,MAAAykB,EAAAxkB,GAAA,CACA,KAAAA,IAAAD,EACA,MAAA4b,EAAA,WAEA,GAAA,MAAA6I,EAAAxkB,GACA,GAAAkb,EAkBA,CAIA,GADA4K,GAAA,EACAZ,EAFA/jB,EAAAnB,GAIA,IADA8lB,GAAA,GAEA9lB,EAAAqlB,EAAArlB,MACAD,IAGAC,IACA+lB,GAIAb,EAAAllB,WAEAA,EAAAY,KAAAggB,IAAA7gB,EAAAslB,EAAArlB,GAAA,GAEA8lB,IACArB,EAAAtjB,EAAAnB,EAAA+lB,GACAA,GAAA,GAEAlK,IACA+J,GAAA,MA3CA,CAIA,IAFAE,EAAA,MAAAtB,EAAArjB,EAAAnB,EAAA,GAEA,OAAAwkB,IAAAxkB,IACA,GAAAA,IAAAD,EACA,OAAA,OAGAC,EACA8lB,IACArB,EAAAtjB,EAAAnB,EAAA,EAAA+lB,GAGAA,GAAA,KAEAlK,EACA+J,GAAA,MA4BA,CAAA,GAAA,OAAAC,EAAArB,EAAAxkB,IAqBA,MAAA,IAnBAmB,EAAAnB,EAAA,EACA8lB,EAAA5K,GAAA,MAAAsJ,EAAArjB,GACA,GAIA,GAHA,OAAA0kB,KACAhK,IAEA7b,IAAAD,EACA,MAAA4b,EAAA,iBAEA/D,EAAAiO,EACAA,EAAArB,EAAAxkB,GACA,MAAA4X,GAAA,MAAAiO,KACA7lB,EACA8lB,IACArB,EAAAtjB,EAAAnB,EAAA,EAAA+lB,GACAA,GAAA,GAEAH,GAAA,UAKAA,GAIA,IAAAxkB,EAAApB,EAGA,GAFAujB,EAAAmC,UAAA,GACAnC,EAAAnhB,KAAAoiB,EAAApjB,MAEA,KAAAA,EAAArB,IAAAwjB,EAAAnhB,KAAAoiB,EAAApjB,OACAA,EACA0Z,EAAAnY,EAAAuZ,UAAAlc,EAAAA,EAAAoB,GAGA,MAFA,KAAA0Z,GAAA,KAAAA,IACAwJ,EAAAxJ,GACAA,EASA,SAAApZ,EAAAoZ,GACAuJ,EAAA3iB,KAAAoZ,GAQA,SAAAM,IACA,IAAAiJ,EAAAtkB,OAAA,CACA,IAAA+a,EAAAK,IACA,GAAA,OAAAL,EACA,OAAA,KACApZ,EAAAoZ,GAEA,OAAAuJ,EAAA,GAoDA,OAAAphB,OAAA8P,eAAA,CACAoI,KAAAA,EACAC,KAAAA,EACA1Z,KAAAA,EACA2Z,KA7CA,SAAA2K,EAAAnV,GACA,IAAAoV,EAAA7K,IAEA,GADA6K,IAAAD,EAGA,OADA7K,KACA,EAEA,GAAAtK,EAEA,OAAA,EADA,MAAA8K,EAAA,UAAAsK,EAAA,OAAAD,EAAA,eAsCA1K,KA5BA,SAAAmC,GACA,IACAtM,EADA+U,EAAA,KAmBA,OAjBAzI,IAAAnf,IACA6S,EAAAC,EAAAyK,EAAA,UACAzK,EAAAyK,EAAA,GACA1K,IAAA+J,GAAA,MAAA/J,EAAA5F,MAAA4F,EAAAwT,aACAuB,EAAA/U,EAAAyT,QAAAzT,EAAA8T,KAAA,QAIAb,EAAA3G,GACArC,IAEAjK,EAAAC,EAAAqM,UACArM,EAAAqM,IACAtM,GAAAA,EAAAwT,YAAAzJ,GAAA,MAAA/J,EAAA5F,OACA2a,EAAA/U,EAAAyT,QAAA,KAAAzT,EAAA8T,OAGAiB,IASA,OAAA,CACAvY,IAAA,WAAA,OAAAkO,KAvXAlG,EAAAuO,SAAAA,G,wBCtCA3kB,EAAAR,QAAAsT,EAGA,IAAAtB,EAAAtR,EAAA,IAGAoO,KAFAwE,EAAAhO,UAAApB,OAAA+N,OAAAD,EAAA1M,YAAA4M,YAAAoB,GAAAnB,UAAA,OAEAzR,EAAA,KACAsV,EAAAtV,EAAA,IACA2S,EAAA3S,EAAA,IACAuV,EAAAvV,EAAA,IACAwV,EAAAxV,EAAA,IACA0V,EAAA1V,EAAA,IACA8V,EAAA9V,EAAA,IACA4V,EAAA5V,EAAA,IACAT,EAAAS,EAAA,IACAmV,EAAAnV,EAAA,IACAoV,EAAApV,EAAA,IACAqV,EAAArV,EAAA,IACAgP,EAAAhP,EAAA,IACA2V,EAAA3V,EAAA,IAUA,SAAA4S,EAAAzT,EAAAsG,GACA6L,EAAAjS,KAAAqF,KAAAvF,EAAAsG,GAMAf,KAAAkH,OAAA,GAMAlH,KAAA+H,OAAA5N,GAMA6F,KAAA0Z,WAAAvf,GAMA6F,KAAAoN,SAAAjT,GAMA6F,KAAA+L,MAAA5R,GAOA6F,KAAAgiB,EAAA,KAOAhiB,KAAA4L,EAAA,KAOA5L,KAAAiiB,EAAA,KAOAjiB,KAAAkiB,EAAA,KA0HA,SAAAlP,EAAA5L,GAKA,OAJAA,EAAA4a,EAAA5a,EAAAwE,EAAAxE,EAAA6a,EAAA,YACA7a,EAAAtK,cACAsK,EAAAvJ,cACAuJ,EAAA+K,OACA/K,EA5HAtI,OAAA2V,iBAAAvG,EAAAhO,UAAA,CAQAiiB,WAAA,CACA3Y,IAAA,WAGA,GAAAxJ,KAAAgiB,EACA,OAAAhiB,KAAAgiB,EAEAhiB,KAAAgiB,EAAA,GACA,IAAA,IAAA1O,EAAAxU,OAAAC,KAAAiB,KAAAkH,QAAArK,EAAA,EAAAA,EAAAyW,EAAA1X,SAAAiB,EAAA,CACA,IAAAgN,EAAA7J,KAAAkH,OAAAoM,EAAAzW,IACAwK,EAAAwC,EAAAxC,GAGA,GAAArH,KAAAgiB,EAAA3a,GACA,MAAArJ,MAAA,gBAAAqJ,EAAA,OAAArH,MAEAA,KAAAgiB,EAAA3a,GAAAwC,EAEA,OAAA7J,KAAAgiB,IAUAvX,YAAA,CACAjB,IAAA,WACA,OAAAxJ,KAAA4L,IAAA5L,KAAA4L,EAAA/Q,EAAAsY,QAAAnT,KAAAkH,WAUAkb,YAAA,CACA5Y,IAAA,WACA,OAAAxJ,KAAAiiB,IAAAjiB,KAAAiiB,EAAApnB,EAAAsY,QAAAnT,KAAA+H,WAUA0H,KAAA,CACAjG,IAAA,WACA,OAAAxJ,KAAAkiB,IAAAliB,KAAAyP,KAAAvB,EAAAmU,oBAAAriB,KAAAkO,KAEAwH,IAAA,SAAAjG,GAmBA,IAhBA,IAAAvP,EAAAuP,EAAAvP,UAeArD,GAdAqD,aAAA8Q,KACAvB,EAAAvP,UAAA,IAAA8Q,GAAAlE,YAAA2C,EACA5U,EAAAohB,MAAAxM,EAAAvP,UAAAA,IAIAuP,EAAAqC,MAAArC,EAAAvP,UAAA4R,MAAA9R,KAGAnF,EAAAohB,MAAAxM,EAAAuB,GAAA,GAEAhR,KAAAkiB,EAAAzS,EAGA,GACA5S,EAAAmD,KAAAyK,YAAA7O,SAAAiB,EACAmD,KAAA4L,EAAA/O,GAAAZ,UAIA,IADA,IAAAqmB,EAAA,GACAzlB,EAAA,EAAAA,EAAAmD,KAAAoiB,YAAAxmB,SAAAiB,EACAylB,EAAAtiB,KAAAiiB,EAAAplB,GAAAZ,UAAAxB,MAAA,CACA+O,IAAA3O,EAAA4a,YAAAzV,KAAAiiB,EAAAplB,GAAAoL,OACAyN,IAAA7a,EAAA8a,YAAA3V,KAAAiiB,EAAAplB,GAAAoL,QAEApL,GACAiC,OAAA2V,iBAAAhF,EAAAvP,UAAAoiB,OAUApU,EAAAmU,oBAAA,SAAA7X,GAIA,IAFA,IAEAX,EAFAD,EAAA/O,EAAAqD,QAAA,CAAA,KAAAsM,EAAA/P,MAEAoC,EAAA,EAAAA,EAAA2N,EAAAC,YAAA7O,SAAAiB,GACAgN,EAAAW,EAAAoB,EAAA/O,IAAA8N,IAAAf,EACA,YAAA/O,EAAA6P,SAAAb,EAAApP,OACAoP,EAAAK,UAAAN,EACA,YAAA/O,EAAA6P,SAAAb,EAAApP,OACA,OAAAmP,EACA,wEADAA,CAEA,yBA6BAsE,EAAAb,SAAA,SAAA5S,EAAAqM,GAMA,IALA,IAAAM,EAAA,IAAA8G,EAAAzT,EAAAqM,EAAA/F,SAGAuS,GAFAlM,EAAAsS,WAAA5S,EAAA4S,WACAtS,EAAAgG,SAAAtG,EAAAsG,SACAtO,OAAAC,KAAA+H,EAAAI,SACArK,EAAA,EACAA,EAAAyW,EAAA1X,SAAAiB,EACAuK,EAAAsG,UACA,IAAA5G,EAAAI,OAAAoM,EAAAzW,IAAAgL,QACAgJ,EACA5C,GADAZ,SACAiG,EAAAzW,GAAAiK,EAAAI,OAAAoM,EAAAzW,MAEA,GAAAiK,EAAAiB,OACA,IAAAuL,EAAAxU,OAAAC,KAAA+H,EAAAiB,QAAAlL,EAAA,EAAAA,EAAAyW,EAAA1X,SAAAiB,EACAuK,EAAAsG,IAAAkD,EAAAvD,SAAAiG,EAAAzW,GAAAiK,EAAAiB,OAAAuL,EAAAzW,MACA,GAAAiK,EAAAC,OACA,IAAAuM,EAAAxU,OAAAC,KAAA+H,EAAAC,QAAAlK,EAAA,EAAAA,EAAAyW,EAAA1X,SAAAiB,EAAA,CACA,IAAAkK,EAAAD,EAAAC,OAAAuM,EAAAzW,IACAuK,EAAAsG,KACA3G,EAAAM,KAAAlN,GACA8T,EACAlH,EAAAG,SAAA/M,GACA+T,EACAnH,EAAA0B,SAAAtO,GACAuP,EACA3C,EAAAwM,UAAApZ,GACA2W,EACAlE,GAPAS,SAOAiG,EAAAzW,GAAAkK,IAWA,OARAD,EAAA4S,YAAA5S,EAAA4S,WAAA9d,SACAwL,EAAAsS,WAAA5S,EAAA4S,YACA5S,EAAAsG,UAAAtG,EAAAsG,SAAAxR,SACAwL,EAAAgG,SAAAtG,EAAAsG,UACAtG,EAAAiF,QACA3E,EAAA2E,OAAA,GACAjF,EAAAkG,UACA5F,EAAA4F,QAAAlG,EAAAkG,SACA5F,GAQA8G,EAAAhO,UAAAqN,OAAA,SAAAC,GACA,IAAAoR,EAAAhS,EAAA1M,UAAAqN,OAAA5S,KAAAqF,KAAAwN,GACAC,IAAAD,KAAAA,EAAAC,aACA,OAAA5S,EAAA+P,SAAA,CACA,UAAAgU,GAAAA,EAAA7d,SAAA5G,GACA,SAAAyS,EAAAgG,YAAA5S,KAAAoiB,YAAA5U,GACA,SAAAZ,EAAAgG,YAAA5S,KAAAyK,YAAAqB,OAAA,SAAAgH,GAAA,OAAAA,EAAApE,iBAAAlB,IAAA,GACA,aAAAxN,KAAA0Z,YAAA1Z,KAAA0Z,WAAA9d,OAAAoE,KAAA0Z,WAAAvf,GACA,WAAA6F,KAAAoN,UAAApN,KAAAoN,SAAAxR,OAAAoE,KAAAoN,SAAAjT,GACA,QAAA6F,KAAA+L,OAAA5R,GACA,SAAAykB,GAAAA,EAAA7X,QAAA5M,GACA,UAAAsT,EAAAzN,KAAAgN,QAAA7S,MAOA+T,EAAAhO,UAAA8T,WAAA,WAEA,IADA,IAAA9M,EAAAlH,KAAAyK,YAAA5N,EAAA,EACAA,EAAAqK,EAAAtL,QACAsL,EAAArK,KAAAZ,UAEA,IADA,IAAA8L,EAAA/H,KAAAoiB,YAAAvlB,EAAA,EACAA,EAAAkL,EAAAnM,QACAmM,EAAAlL,KAAAZ,UACA,OAAA2Q,EAAA1M,UAAA8T,WAAArZ,KAAAqF,OAMAkO,EAAAhO,UAAAsJ,IAAA,SAAA/O,GACA,OAAAuF,KAAAkH,OAAAzM,IACAuF,KAAA+H,QAAA/H,KAAA+H,OAAAtN,IACAuF,KAAA+G,QAAA/G,KAAA+G,OAAAtM,IACA,MAUAyT,EAAAhO,UAAAwN,IAAA,SAAA0E,GAEA,GAAApS,KAAAwJ,IAAA4I,EAAA3X,MACA,MAAAuD,MAAA,mBAAAoU,EAAA3X,KAAA,QAAAuF,MAEA,GAAAoS,aAAAnE,GAAAmE,EAAAhE,SAAAjU,GAAA,CAMA,IAAA6F,KAAAgiB,GAAAhiB,KAAAmiB,YAAA/P,EAAA/K,IACA,MAAArJ,MAAA,gBAAAoU,EAAA/K,GAAA,OAAArH,MACA,GAAAA,KAAA6N,aAAAuE,EAAA/K,IACA,MAAArJ,MAAA,MAAAoU,EAAA/K,GAAA,mBAAArH,MACA,GAAAA,KAAA8N,eAAAsE,EAAA3X,MACA,MAAAuD,MAAA,SAAAoU,EAAA3X,KAAA,oBAAAuF,MAOA,OALAoS,EAAAnD,QACAmD,EAAAnD,OAAAjB,OAAAoE,IACApS,KAAAkH,OAAAkL,EAAA3X,MAAA2X,GACA7D,QAAAvO,KACAoS,EAAAuB,MAAA3T,MACAgT,EAAAhT,MAEA,OAAAoS,aAAAxB,GACA5Q,KAAA+H,SACA/H,KAAA+H,OAAA,KACA/H,KAAA+H,OAAAqK,EAAA3X,MAAA2X,GACAuB,MAAA3T,MACAgT,EAAAhT,OAEA4M,EAAA1M,UAAAwN,IAAA/S,KAAAqF,KAAAoS,IAUAlE,EAAAhO,UAAA8N,OAAA,SAAAoE,GACA,GAAAA,aAAAnE,GAAAmE,EAAAhE,SAAAjU,GAAA,CAIA,GAAA6F,KAAAkH,QAAAlH,KAAAkH,OAAAkL,EAAA3X,QAAA2X,EAMA,cAHApS,KAAAkH,OAAAkL,EAAA3X,MACA2X,EAAAnD,OAAA,KACAmD,EAAAwB,SAAA5T,MACAgT,EAAAhT,MALA,MAAAhC,MAAAoU,EAAA,uBAAApS,MAOA,GAAAoS,aAAAxB,EAAA,CAGA,GAAA5Q,KAAA+H,QAAA/H,KAAA+H,OAAAqK,EAAA3X,QAAA2X,EAMA,cAHApS,KAAA+H,OAAAqK,EAAA3X,MACA2X,EAAAnD,OAAA,KACAmD,EAAAwB,SAAA5T,MACAgT,EAAAhT,MALA,MAAAhC,MAAAoU,EAAA,uBAAApS,MAOA,OAAA4M,EAAA1M,UAAA8N,OAAArT,KAAAqF,KAAAoS,IAQAlE,EAAAhO,UAAA2N,aAAA,SAAAxG,GACA,OAAAuF,EAAAiB,aAAA7N,KAAAoN,SAAA/F,IAQA6G,EAAAhO,UAAA4N,eAAA,SAAArT,GACA,OAAAmS,EAAAkB,eAAA9N,KAAAoN,SAAA3S,IAQAyT,EAAAhO,UAAA2M,OAAA,SAAAgF,GACA,OAAA,IAAA7R,KAAAyP,KAAAoC,IAOA3D,EAAAhO,UAAAqiB,MAAA,WAMA,IAFA,IAAApY,EAAAnK,KAAAmK,SACA8B,EAAA,GACApP,EAAA,EAAAA,EAAAmD,KAAAyK,YAAA7O,SAAAiB,EACAoP,EAAA1O,KAAAyC,KAAA4L,EAAA/O,GAAAZ,UAAA+N,cAGAhK,KAAAlD,OAAA2T,EAAAzQ,KAAAyQ,CAAA,CACAS,OAAAA,EACAjF,MAAAA,EACApR,KAAAA,IAEAmF,KAAAnC,OAAA6S,EAAA1Q,KAAA0Q,CAAA,CACAU,OAAAA,EACAnF,MAAAA,EACApR,KAAAA,IAEAmF,KAAAmS,OAAAxB,EAAA3Q,KAAA2Q,CAAA,CACA1E,MAAAA,EACApR,KAAAA,IAEAmF,KAAAuK,WAAAD,EAAAC,WAAAvK,KAAAsK,CAAA,CACA2B,MAAAA,EACApR,KAAAA,IAEAmF,KAAA4K,SAAAN,EAAAM,SAAA5K,KAAAsK,CAAA,CACA2B,MAAAA,EACApR,KAAAA,IAIA,IAEA2nB,EAFAC,EAAAxR,EAAA9G,GAaA,OAZAsY,KACAD,EAAA1jB,OAAA+N,OAAA7M,OAEAuK,WAAAvK,KAAAuK,WACAvK,KAAAuK,WAAAkY,EAAAlY,WAAA9F,KAAA+d,GAGAA,EAAA5X,SAAA5K,KAAA4K,SACA5K,KAAA4K,SAAA6X,EAAA7X,SAAAnG,KAAA+d,IAIAxiB,MASAkO,EAAAhO,UAAApD,OAAA,SAAAyR,EAAAwD,GACA,OAAA/R,KAAAuiB,QAAAzlB,OAAAyR,EAAAwD,IASA7D,EAAAhO,UAAA8R,gBAAA,SAAAzD,EAAAwD,GACA,OAAA/R,KAAAlD,OAAAyR,EAAAwD,GAAAA,EAAAxL,IAAAwL,EAAA2Q,OAAA3Q,GAAA4Q,UAWAzU,EAAAhO,UAAArC,OAAA,SAAAoU,EAAArW,GACA,OAAAoE,KAAAuiB,QAAA1kB,OAAAoU,EAAArW,IAUAsS,EAAAhO,UAAAgS,gBAAA,SAAAD,GAGA,OAFAA,aAAAb,IACAa,EAAAb,EAAAvE,OAAAoF,IACAjS,KAAAnC,OAAAoU,EAAAA,EAAAsJ,WAQArN,EAAAhO,UAAAiS,OAAA,SAAA5D,GACA,OAAAvO,KAAAuiB,QAAApQ,OAAA5D,IAQAL,EAAAhO,UAAAqK,WAAA,SAAA6H,GACA,OAAApS,KAAAuiB,QAAAhY,WAAA6H,IA4BAlE,EAAAhO,UAAA0K,SAAA,SAAA2D,EAAAxN,GACA,OAAAf,KAAAuiB,QAAA3X,SAAA2D,EAAAxN,IAkBAmN,EAAAwB,EAAA,SAAAkT,GACA,OAAA,SAAAzK,GACAtd,EAAAiV,aAAAqI,EAAAyK,M,iHCpkBA,IAEA/nB,EAAAS,EAAA,IAEA6jB,EAAA,CACA,SACA,QACA,QACA,SACA,SACA,UACA,WACA,QACA,SACA,SACA,UACA,WACA,OACA,SACA,SAGA,SAAA0D,EAAApa,EAAA5M,GACA,IAAAgB,EAAA,EAAAimB,EAAA,GAEA,IADAjnB,GAAA,EACAgB,EAAA4L,EAAA7M,QAAAknB,EAAA3D,EAAAtiB,EAAAhB,IAAA4M,EAAA5L,KACA,OAAAimB,EAuBA7W,EAAAE,MAAA0W,EAAA,CACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,IAwBA5W,EAAAC,SAAA2W,EAAA,CACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,GACA,EACA,GACAhoB,EAAA2U,WACA,OAaAvD,EAAAZ,KAAAwX,EAAA,CACA,EACA,EACA,EACA,EACA,GACA,GAmBA5W,EAAAO,OAAAqW,EAAA,CACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,GACA,GAoBA5W,EAAAG,OAAAyW,EAAA,CACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,K,6BC5LA,IAIA3U,EACAxE,EALA7O,EAAAO,EAAAR,QAAAU,EAAA,IAEAiW,EAAAjW,EAAA,IAiDAynB,GA5CAloB,EAAAqD,QAAA5C,EAAA,GACAT,EAAA6F,MAAApF,EAAA,GACAT,EAAA2K,KAAAlK,EAAA,GAMAT,EAAA+F,GAAA/F,EAAAgG,QAAA,MAOAhG,EAAAsY,QAAA,SAAAf,GACA,GAAAA,EAAA,CAIA,IAHA,IAAArT,EAAAD,OAAAC,KAAAqT,GACAS,EAAAnX,MAAAqD,EAAAnD,QACAE,EAAA,EACAA,EAAAiD,EAAAnD,QACAiX,EAAA/W,GAAAsW,EAAArT,EAAAjD,MACA,OAAA+W,EAEA,MAAA,IAQAhY,EAAA+P,SAAA,SAAAiI,GAGA,IAFA,IAAAT,EAAA,GACAtW,EAAA,EACAA,EAAA+W,EAAAjX,QAAA,CACA,IAAAonB,EAAAnQ,EAAA/W,KACAqG,EAAA0Q,EAAA/W,KACAqG,IAAAhI,KACAiY,EAAA4Q,GAAA7gB,GAEA,OAAAiQ,GAGA,OACA6Q,EAAA,KA+BAC,GAxBAroB,EAAAmkB,WAAA,SAAAvkB,GACA,MAAA,uTAAAwD,KAAAxD,IAQAI,EAAA6P,SAAA,SAAAX,GACA,OAAA,YAAA9L,KAAA8L,IAAAlP,EAAAmkB,WAAAjV,GACA,KAAAA,EAAAzK,QAAAyjB,EAAA,QAAAzjB,QAAA2jB,EAAA,OAAA,KACA,IAAAlZ,GAQAlP,EAAAgf,QAAA,SAAAmG,GACA,OAAAA,EAAA,IAAAA,IAAAmD,cAAAnD,EAAAjI,UAAA,IAGA,aAuDAqL,GAhDAvoB,EAAA0c,UAAA,SAAAyI,GACA,OAAAA,EAAAjI,UAAA,EAAA,GACAiI,EAAAjI,UAAA,GACAzY,QAAA4jB,EAAA,SAAA3jB,EAAAC,GAAA,OAAAA,EAAA2jB,iBASAtoB,EAAAiQ,kBAAA,SAAAuY,EAAA/lB,GACA,OAAA+lB,EAAAhc,GAAA/J,EAAA+J,IAWAxM,EAAAiV,aAAA,SAAAL,EAAAmT,GAGA,GAAAnT,EAAAqC,MAMA,OALA8Q,GAAAnT,EAAAqC,MAAArX,OAAAmoB,IACA/nB,EAAAyoB,aAAAtV,OAAAyB,EAAAqC,OACArC,EAAAqC,MAAArX,KAAAmoB,EACA/nB,EAAAyoB,aAAA5V,IAAA+B,EAAAqC,QAEArC,EAAAqC,MAOA1K,EAAA,IAFA8G,EADAA,GACA5S,EAAA,KAEAsnB,GAAAnT,EAAAhV,MAKA,OAJAI,EAAAyoB,aAAA5V,IAAAtG,GACAA,EAAAqI,KAAAA,EACA3Q,OAAA8P,eAAAa,EAAA,QAAA,CAAAhQ,MAAA2H,EAAAmc,YAAA,IACAzkB,OAAA8P,eAAAa,EAAAvP,UAAA,QAAA,CAAAT,MAAA2H,EAAAmc,YAAA,IACAnc,GAGA,GAOAvM,EAAAkV,aAAA,SAAAqC,GAGA,GAAAA,EAAAN,MACA,OAAAM,EAAAN,MAMA,IAAAxE,EAAA,IAFA5D,EADAA,GACApO,EAAA,KAEA,OAAA8nB,IAAAhR,GAGA,OAFAvX,EAAAyoB,aAAA5V,IAAAJ,GACAxO,OAAA8P,eAAAwD,EAAA,QAAA,CAAA3S,MAAA6N,EAAAiW,YAAA,IACAjW,GAWAzS,EAAAua,YAAA,SAAAoO,EAAAhe,EAAA/F,GAiBA,GAAA,iBAAA+jB,EACA,MAAArW,UAAA,yBACA,GAAA3H,EAIA,OAtBA,SAAAie,EAAAD,EAAAhe,EAAA/F,GACA,IAAAsU,EAAAvO,EAAAK,QACA,MAAA,cAAAkO,IAGA,EAAAvO,EAAA5J,OACA4nB,EAAAzP,GAAA0P,EAAAD,EAAAzP,IAAA,GAAAvO,EAAA/F,KAEA6a,EAAAkJ,EAAAzP,MAEAtU,EAAA,GAAA8a,OAAAD,GAAAC,OAAA9a,IACA+jB,EAAAzP,GAAAtU,IARA+jB,EAmBAC,CAAAD,EADAhe,EAAAA,EAAAE,MAAA,KACAjG,GAHA,MAAA0N,UAAA,2BAYArO,OAAA8P,eAAA/T,EAAA,eAAA,CACA2O,IAAA,WACA,OAAA+H,EAAA,YAAAA,EAAA,UAAA,IAAAjW,EAAA,U,iEChNAF,EAAAR,QAAA8f,EAEA,IAAA7f,EAAAS,EAAA,IAUA,SAAAof,EAAA5W,EAAAC,GASA/D,KAAA8D,GAAAA,IAAA,EAMA9D,KAAA+D,GAAAA,IAAA,EAQA,IAAA2f,EAAAhJ,EAAAgJ,KAAA,IAAAhJ,EAAA,EAAA,GAoFA3c,GAlFA2lB,EAAAjY,SAAA,WAAA,OAAA,GACAiY,EAAAC,SAAAD,EAAArH,SAAA,WAAA,OAAArc,MACA0jB,EAAA9nB,OAAA,WAAA,OAAA,GAOA8e,EAAAkJ,SAAA,mBAOAlJ,EAAAtL,WAAA,SAAA3P,GACA,GAAA,IAAAA,EACA,OAAAikB,EACA,IAAAphB,EAAA7C,EAAA,EAGAqE,GADArE,EADA6C,GACA7C,EACAA,KAAA,EACAsE,GAAAtE,EAAAqE,GAAA,aAAA,EAUA,OATAxB,IACAyB,GAAAA,IAAA,EACAD,GAAAA,IAAA,EACA,aAAAA,IACAA,EAAA,EACA,aAAAC,IACAA,EAAA,KAGA,IAAA2W,EAAA5W,EAAAC,IAQA2W,EAAAmJ,KAAA,SAAApkB,GACA,GAAA,iBAAAA,EACA,OAAAib,EAAAtL,WAAA3P,GACA,GAAA5E,EAAA8S,SAAAlO,GAAA,CAEA,IAAA5E,EAAAI,KAGA,OAAAyf,EAAAtL,WAAA4I,SAAAvY,EAAA,KAFAA,EAAA5E,EAAAI,KAAA6oB,WAAArkB,GAIA,OAAAA,EAAA6L,KAAA7L,EAAA8L,KAAA,IAAAmP,EAAAjb,EAAA6L,MAAA,EAAA7L,EAAA8L,OAAA,GAAAmY,GAQAhJ,EAAAxa,UAAAuL,SAAA,SAAAD,GACA,IAEAzH,EAFA,OAAAyH,GAAAxL,KAAA+D,KAAA,IACAD,EAAA,GAAA9D,KAAA8D,KAAA,EACAC,GAAA/D,KAAA+D,KAAA,IAGAD,EAAA,YADAC,EADAD,EAEAC,EADAA,EAAA,IAAA,KAGA/D,KAAA8D,GAAA,WAAA9D,KAAA+D,IAQA2W,EAAAxa,UAAA6jB,OAAA,SAAAvY,GACA,OAAA3Q,EAAAI,KACA,IAAAJ,EAAAI,KAAA,EAAA+E,KAAA8D,GAAA,EAAA9D,KAAA+D,KAAAyH,GAEA,CAAAF,IAAA,EAAAtL,KAAA8D,GAAAyH,KAAA,EAAAvL,KAAA+D,GAAAyH,WAAAA,IAGAhO,OAAA0C,UAAAnC,YAOA2c,EAAAsJ,SAAA,SAAAC,GACA,MAjFAvJ,qBAiFAuJ,EACAP,EACA,IAAAhJ,GACA3c,EAAApD,KAAAspB,EAAA,GACAlmB,EAAApD,KAAAspB,EAAA,IAAA,EACAlmB,EAAApD,KAAAspB,EAAA,IAAA,GACAlmB,EAAApD,KAAAspB,EAAA,IAAA,MAAA,GAEAlmB,EAAApD,KAAAspB,EAAA,GACAlmB,EAAApD,KAAAspB,EAAA,IAAA,EACAlmB,EAAApD,KAAAspB,EAAA,IAAA,GACAlmB,EAAApD,KAAAspB,EAAA,IAAA,MAAA,IAQAvJ,EAAAxa,UAAAgkB,OAAA,WACA,OAAA1mB,OAAAC,aACA,IAAAuC,KAAA8D,GACA9D,KAAA8D,KAAA,EAAA,IACA9D,KAAA8D,KAAA,GAAA,IACA9D,KAAA8D,KAAA,GACA,IAAA9D,KAAA+D,GACA/D,KAAA+D,KAAA,EAAA,IACA/D,KAAA+D,KAAA,GAAA,IACA/D,KAAA+D,KAAA,KAQA2W,EAAAxa,UAAAyjB,SAAA,WACA,IAAAQ,EAAAnkB,KAAA+D,IAAA,GAGA,OAFA/D,KAAA+D,KAAA/D,KAAA+D,IAAA,EAAA/D,KAAA8D,KAAA,IAAAqgB,KAAA,EACAnkB,KAAA8D,IAAA9D,KAAA8D,IAAA,EAAAqgB,KAAA,EACAnkB,MAOA0a,EAAAxa,UAAAmc,SAAA,WACA,IAAA8H,IAAA,EAAAnkB,KAAA8D,IAGA,OAFA9D,KAAA8D,KAAA9D,KAAA8D,KAAA,EAAA9D,KAAA+D,IAAA,IAAAogB,KAAA,EACAnkB,KAAA+D,IAAA/D,KAAA+D,KAAA,EAAAogB,KAAA,EACAnkB,MAOA0a,EAAAxa,UAAAtE,OAAA,WACA,IAAAwoB,EAAApkB,KAAA8D,GACAugB,GAAArkB,KAAA8D,KAAA,GAAA9D,KAAA+D,IAAA,KAAA,EACAugB,EAAAtkB,KAAA+D,KAAA,GACA,OAAA,GAAAugB,EACA,GAAAD,EACAD,EAAA,MACAA,EAAA,IAAA,EAAA,EACAA,EAAA,QAAA,EAAA,EACAC,EAAA,MACAA,EAAA,IAAA,EAAA,EACAA,EAAA,QAAA,EAAA,EACAC,EAAA,IAAA,EAAA,K,6BCrMA,IAAAzpB,EAAAD,EA2OA,SAAAqhB,EAAAuH,EAAAe,EAAAxV,GACA,IAAA,IAAAhQ,EAAAD,OAAAC,KAAAwlB,GAAA1nB,EAAA,EAAAA,EAAAkC,EAAAnD,SAAAiB,EACA2mB,EAAAzkB,EAAAlC,MAAA1C,IAAA4U,IACAyU,EAAAzkB,EAAAlC,IAAA0nB,EAAAxlB,EAAAlC,KACA,OAAA2mB,EAoBA,SAAAgB,EAAA/pB,GAEA,SAAAgqB,EAAAlW,EAAAsD,GAEA,KAAA7R,gBAAAykB,GACA,OAAA,IAAAA,EAAAlW,EAAAsD,GAKA/S,OAAA8P,eAAA5O,KAAA,UAAA,CAAAwJ,IAAA,WAAA,OAAA+E,KAGAvQ,MAAA0mB,kBACA1mB,MAAA0mB,kBAAA1kB,KAAAykB,GAEA3lB,OAAA8P,eAAA5O,KAAA,QAAA,CAAAP,MAAAzB,QAAAkiB,OAAA,KAEArO,GACAoK,EAAAjc,KAAA6R,GA4BA,OAzBA4S,EAAAvkB,UAAApB,OAAA+N,OAAA7O,MAAAkC,UAAA,CACA4M,YAAA,CACArN,MAAAglB,EACAE,UAAA,EACApB,YAAA,EACAqB,cAAA,GAEAnqB,KAAA,CACA+O,MAAA,OAAA/O,GACAib,IAAAvb,GACAopB,YAAA,EAKAqB,cAAA,GAEAnmB,SAAA,CACAgB,QAAA,OAAAO,KAAAvF,KAAA,KAAAuF,KAAAuO,SACAoW,UAAA,EACApB,YAAA,EACAqB,cAAA,KAIAH,EA/SA5pB,EAAA8F,UAAArF,EAAA,GAGAT,EAAAwB,OAAAf,EAAA,GAGAT,EAAAkF,aAAAzE,EAAA,GAGAT,EAAAghB,MAAAvgB,EAAA,GAGAT,EAAAgG,QAAAvF,EAAA,GAGAT,EAAAyL,KAAAhL,EAAA,IAGAT,EAAAgqB,KAAAvpB,EAAA,GAGAT,EAAA6f,SAAApf,EAAA,IAOAT,EAAA+iB,UAAA,oBAAA9iB,QACAA,QACAA,OAAAwiB,SACAxiB,OAAAwiB,QAAAwH,UACAhqB,OAAAwiB,QAAAwH,SAAAC,MAOAlqB,EAAAC,OAAAD,EAAA+iB,QAAA9iB,QACA,oBAAAkqB,QAAAA,QACA,oBAAAlI,MAAAA,MACA9c,KAQAnF,EAAA2U,WAAA1Q,OAAAuQ,OAAAvQ,OAAAuQ,OAAA,IAAA,GAOAxU,EAAA0U,YAAAzQ,OAAAuQ,OAAAvQ,OAAAuQ,OAAA,IAAA,GAQAxU,EAAA+S,UAAAlO,OAAAkO,WAAA,SAAAnO,GACA,MAAA,iBAAAA,GAAAwlB,SAAAxlB,IAAAhD,KAAAkD,MAAAF,KAAAA,GAQA5E,EAAA8S,SAAA,SAAAlO,GACA,MAAA,iBAAAA,GAAAA,aAAAjC,QAQA3C,EAAAwT,SAAA,SAAA5O,GACA,OAAAA,GAAA,iBAAAA,GAWA5E,EAAAqqB,MAQArqB,EAAAsqB,MAAA,SAAArS,EAAA/I,GACA,IAAAtK,EAAAqT,EAAA/I,GACA,OAAA,MAAAtK,GAAAqT,EAAAoC,eAAAnL,KACA,iBAAAtK,GAAA,GAAA/D,MAAAmY,QAAApU,GAAAA,EAAAX,OAAAC,KAAAU,IAAA7D,SAeAf,EAAAigB,OAAA,WACA,IACA,IAAAA,EAAAjgB,EAAAgG,QAAA,UAAAia,OAEA,OAAAA,EAAA5a,UAAAklB,UAAAtK,EAAA,KACA,MAAAxV,GAEA,OAAA,MAPA,GAYAzK,EAAAwqB,EAAA,KAGAxqB,EAAAyqB,EAAA,KAOAzqB,EAAAyU,UAAA,SAAAiW,GAEA,MAAA,iBAAAA,EACA1qB,EAAAigB,OACAjgB,EAAAyqB,EAAAC,GACA,IAAA1qB,EAAAa,MAAA6pB,GACA1qB,EAAAigB,OACAjgB,EAAAwqB,EAAAE,GACA,oBAAA5jB,WACA4jB,EACA,IAAA5jB,WAAA4jB,IAOA1qB,EAAAa,MAAA,oBAAAiG,WAAAA,WAAAjG,MAeAb,EAAAI,KAAAJ,EAAAC,OAAA0qB,SAAA3qB,EAAAC,OAAA0qB,QAAAvqB,MACAJ,EAAAC,OAAAG,MACAJ,EAAAgG,QAAA,QAOAhG,EAAA4qB,OAAA,mBAOA5qB,EAAA6qB,QAAA,wBAOA7qB,EAAA8qB,QAAA,6CAOA9qB,EAAA+qB,WAAA,SAAAnmB,GACA,OAAAA,EACA5E,EAAA6f,SAAAmJ,KAAApkB,GAAAykB,SACArpB,EAAA6f,SAAAkJ,UASA/oB,EAAAgrB,aAAA,SAAA5B,EAAAzY,GACA0P,EAAArgB,EAAA6f,SAAAsJ,SAAAC,GACA,OAAAppB,EAAAI,KACAJ,EAAAI,KAAA6qB,SAAA5K,EAAApX,GAAAoX,EAAAnX,GAAAyH,GACA0P,EAAAzP,WAAAD,IAkBA3Q,EAAAohB,MAAAA,EAOAphB,EAAA+e,QAAA,SAAAoG,GACA,OAAAA,EAAA,IAAAA,IAAA1R,cAAA0R,EAAAjI,UAAA,IA2DAld,EAAA2pB,SAAAA,EAmBA3pB,EAAAkrB,cAAAvB,EAAA,iBAoBA3pB,EAAA4a,YAAA,SAAAH,GAEA,IADA,IAAA0Q,EAAA,GACAnpB,EAAA,EAAAA,EAAAyY,EAAA1Z,SAAAiB,EACAmpB,EAAA1Q,EAAAzY,IAAA,EAOA,OAAA,WACA,IAAA,IAAAkC,EAAAD,OAAAC,KAAAiB,MAAAnD,EAAAkC,EAAAnD,OAAA,GAAA,EAAAiB,IAAAA,EACA,GAAA,IAAAmpB,EAAAjnB,EAAAlC,KAAAmD,KAAAjB,EAAAlC,MAAA1C,IAAA,OAAA6F,KAAAjB,EAAAlC,IACA,OAAAkC,EAAAlC,KAiBAhC,EAAA8a,YAAA,SAAAL,GAQA,OAAA,SAAA7a,GACA,IAAA,IAAAoC,EAAA,EAAAA,EAAAyY,EAAA1Z,SAAAiB,EACAyY,EAAAzY,KAAApC,UACAuF,KAAAsV,EAAAzY,MAoBAhC,EAAA2S,cAAA,CACAyY,MAAAzoB,OACA0oB,MAAA1oB,OACAkO,MAAAlO,OACAsJ,MAAA,GAIAjM,EAAAqV,EAAA,WACA,IAAA4K,EAAAjgB,EAAAigB,OAEAA,GAMAjgB,EAAAwqB,EAAAvK,EAAA+I,OAAAliB,WAAAkiB,MAAA/I,EAAA+I,MAEA,SAAApkB,EAAA0mB,GACA,OAAA,IAAArL,EAAArb,EAAA0mB,IAEAtrB,EAAAyqB,EAAAxK,EAAAsL,aAEA,SAAAlgB,GACA,OAAA,IAAA4U,EAAA5U,KAbArL,EAAAwqB,EAAAxqB,EAAAyqB,EAAA,O,2DCraAlqB,EAAAR,QAwHA,SAAA4P,GAGA,IAAAZ,EAAA/O,EAAAqD,QAAA,CAAA,KAAAsM,EAAA/P,KAAA,UAAAI,CACA,oCADAA,CAEA,WAAA,mBACAkN,EAAAyC,EAAA4X,YACAiE,EAAA,GACAte,EAAAnM,QAAAgO,EACA,YAEA,IAAA,IAAA/M,EAAA,EAAAA,EAAA2N,EAAAC,YAAA7O,SAAAiB,EAAA,CACA,IA2BAypB,EA3BAzc,EAAAW,EAAAoB,EAAA/O,GAAAZ,UACA+P,EAAA,IAAAnR,EAAA6P,SAAAb,EAAApP,MAEAoP,EAAA6C,UAAA9C,EACA,sCAAAoC,EAAAnC,EAAApP,MAGAoP,EAAAc,KAAAf,EACA,yBAAAoC,EADApC,CAEA,WAAA2c,EAAA1c,EAAA,UAFAD,CAGA,wBAAAoC,EAHApC,CAIA,gCAxDA,SAAAA,EAAAC,EAAAmC,GAEA,OAAAnC,EAAAhC,SACA,IAAA,QACA,IAAA,SACA,IAAA,SACA,IAAA,UACA,IAAA,WAAA+B,EACA,6BAAAoC,EADApC,CAEA,WAAA2c,EAAA1c,EAAA,gBACA,MACA,IAAA,QACA,IAAA,SACA,IAAA,SACA,IAAA,UACA,IAAA,WAAAD,EACA,6BAAAoC,EADApC,CAEA,WAAA2c,EAAA1c,EAAA,qBACA,MACA,IAAA,OAAAD,EACA,4BAAAoC,EADApC,CAEA,WAAA2c,EAAA1c,EAAA,iBAoCA2c,CAAA5c,EAAAC,EAAA,QACA4c,EAAA7c,EAAAC,EAAAhN,EAAAmP,EAAA,SAAAya,CACA,MAGA5c,EAAAK,UAAAN,EACA,yBAAAoC,EADApC,CAEA,WAAA2c,EAAA1c,EAAA,SAFAD,CAGA,gCAAAoC,GACAya,EAAA7c,EAAAC,EAAAhN,EAAAmP,EAAA,MAAAya,CACA,OAIA5c,EAAAqB,SACAob,EAAAzrB,EAAA6P,SAAAb,EAAAqB,OAAAzQ,MACA,IAAA4rB,EAAAxc,EAAAqB,OAAAzQ,OAAAmP,EACA,cAAA0c,EADA1c,CAEA,WAAAC,EAAAqB,OAAAzQ,KAAA,qBACA4rB,EAAAxc,EAAAqB,OAAAzQ,MAAA,EACAmP,EACA,QAAA0c,IAEAG,EAAA7c,EAAAC,EAAAhN,EAAAmP,IAEAnC,EAAA6C,UAAA9C,EACA,KAEA,OAAAA,EACA,gBA3KA,IAAAF,EAAApO,EAAA,IACAT,EAAAS,EAAA,IAEA,SAAAirB,EAAA1c,EAAAgY,GACA,OAAAhY,EAAApP,KAAA,KAAAonB,GAAAhY,EAAAK,UAAA,UAAA2X,EAAA,KAAAhY,EAAAc,KAAA,WAAAkX,EAAA,MAAAhY,EAAAhC,QAAA,IAAA,IAAA,YAYA,SAAA4e,EAAA7c,EAAAC,EAAAC,EAAAkC,GAEA,GAAAnC,EAAAG,aACA,GAAAH,EAAAG,wBAAAN,EAAA,CAAAE,EACA,cAAAoC,EADApC,CAEA,WAFAA,CAGA,WAAA2c,EAAA1c,EAAA,eACA,IAAA,IAAA9K,EAAAD,OAAAC,KAAA8K,EAAAG,aAAAvB,QAAApL,EAAA,EAAAA,EAAA0B,EAAAnD,SAAAyB,EAAAuM,EACA,WAAAC,EAAAG,aAAAvB,OAAA1J,EAAA1B,KACAuM,EACA,QADAA,CAEA,UAEAA,EACA,IADAA,CAEA,8BAAAE,EAAAkC,EAFApC,CAGA,QAHAA,CAIA,aAAAC,EAAApP,KAAA,IAJAmP,CAKA,UAGA,OAAAC,EAAAzC,MACA,IAAA,QACA,IAAA,SACA,IAAA,SACA,IAAA,UACA,IAAA,WAAAwC,EACA,0BAAAoC,EADApC,CAEA,WAAA2c,EAAA1c,EAAA,YACA,MACA,IAAA,QACA,IAAA,SACA,IAAA,SACA,IAAA,UACA,IAAA,WAAAD,EACA,kFAAAoC,EAAAA,EAAAA,EAAAA,EADApC,CAEA,WAAA2c,EAAA1c,EAAA,iBACA,MACA,IAAA,QACA,IAAA,SAAAD,EACA,2BAAAoC,EADApC,CAEA,WAAA2c,EAAA1c,EAAA,WACA,MACA,IAAA,OAAAD,EACA,4BAAAoC,EADApC,CAEA,WAAA2c,EAAA1c,EAAA,YACA,MACA,IAAA,SAAAD,EACA,yBAAAoC,EADApC,CAEA,WAAA2c,EAAA1c,EAAA,WACA,MACA,IAAA,QAAAD,EACA,4DAAAoC,EAAAA,EAAAA,EADApC,CAEA,WAAA2c,EAAA1c,EAAA,WAIA,OAAAD,I,mCCrEA,IAEAoH,EAAA1V,EAAA,IA6BA2V,EAAA,wBAAA,CAEA1G,WAAA,SAAA6H,GAGA,GAAAA,GAAAA,EAAA,SAAA,CAEA,IAKAjL,EALA1M,EAAA2X,EAAA,SAAA2F,UAAA,EAAA3F,EAAA,SAAAgL,YAAA,MACAhW,EAAApH,KAAAiU,OAAAxZ,GAEA,GAAA2M,EAQA,QANAD,EAAA,MAAAiL,EAAA,SAAA,IAAAA,IACAA,EAAA,SAAA1U,MAAA,GAAA0U,EAAA,UAEAvG,QAAA,OACA1E,EAAA,IAAAA,GAEAnH,KAAA6M,OAAA,CACA1F,SAAAA,EACA1H,MAAA2H,EAAAtK,OAAAsK,EAAAmD,WAAA6H,IAAA4K,WAKA,OAAAhd,KAAAuK,WAAA6H,IAGAxH,SAAA,SAAA2D,EAAAxN,GAGA,IAkBAqR,EACAsU,EAlBA9gB,EAAA,GACAnL,EAAA,GAeA,OAZAsG,GAAAA,EAAA+F,MAAAyH,EAAApH,UAAAoH,EAAA9O,QAEAhF,EAAA8T,EAAApH,SAAA4Q,UAAA,EAAAxJ,EAAApH,SAAAiW,YAAA,MAEAxX,EAAA2I,EAAApH,SAAA4Q,UAAA,EAAA,EAAAxJ,EAAApH,SAAAiW,YAAA,OACAhW,EAAApH,KAAAiU,OAAAxZ,MAGA8T,EAAAnH,EAAAvJ,OAAA0Q,EAAA9O,WAIA8O,aAAAvO,KAAAyP,OAAAlB,aAAAyC,GACAoB,EAAA7D,EAAAuD,MAAAlH,SAAA2D,EAAAxN,GACA2lB,EAAA,MAAAnY,EAAAuD,MAAA3H,SAAA,GACAoE,EAAAuD,MAAA3H,SAAAzM,MAAA,GAAA6Q,EAAAuD,MAAA3H,SAMAiI,EAAA,SADA3X,GAFAmL,EADA,KAAAA,EAtBA,uBAyBAA,GAAA8gB,EAEAtU,GAGApS,KAAA4K,SAAA2D,EAAAxN,M,6BClGA3F,EAAAR,QAAAsW,EAEA,IAEAC,EAFAtW,EAAAS,EAAA,IAIAof,EAAA7f,EAAA6f,SACAre,EAAAxB,EAAAwB,OACAiK,EAAAzL,EAAAyL,KAWA,SAAAqgB,EAAAprB,EAAAgL,EAAApE,GAMAnC,KAAAzE,GAAAA,EAMAyE,KAAAuG,IAAAA,EAMAvG,KAAAgX,KAAA7c,GAMA6F,KAAAmC,IAAAA,EAIA,SAAAykB,KAUA,SAAAC,EAAA9U,GAMA/R,KAAAoX,KAAArF,EAAAqF,KAMApX,KAAA8mB,KAAA/U,EAAA+U,KAMA9mB,KAAAuG,IAAAwL,EAAAxL,IAMAvG,KAAAgX,KAAAjF,EAAAgV,OAQA,SAAA7V,IAMAlR,KAAAuG,IAAA,EAMAvG,KAAAoX,KAAA,IAAAuP,EAAAC,EAAA,EAAA,GAMA5mB,KAAA8mB,KAAA9mB,KAAAoX,KAMApX,KAAA+mB,OAAA,KASA,SAAAla,IACA,OAAAhS,EAAAigB,OACA,WACA,OAAA5J,EAAArE,OAAA,WACA,OAAA,IAAAsE,OAIA,WACA,OAAA,IAAAD,GAuCA,SAAA8V,EAAA7kB,EAAAC,EAAAC,GACAD,EAAAC,GAAA,IAAAF,EAoBA,SAAA8kB,EAAA1gB,EAAApE,GACAnC,KAAAuG,IAAAA,EACAvG,KAAAgX,KAAA7c,GACA6F,KAAAmC,IAAAA,EA8CA,SAAA+kB,EAAA/kB,EAAAC,EAAAC,GACA,KAAAF,EAAA4B,IACA3B,EAAAC,KAAA,IAAAF,EAAA2B,GAAA,IACA3B,EAAA2B,IAAA3B,EAAA2B,KAAA,EAAA3B,EAAA4B,IAAA,MAAA,EACA5B,EAAA4B,MAAA,EAEA,KAAA,IAAA5B,EAAA2B,IACA1B,EAAAC,KAAA,IAAAF,EAAA2B,GAAA,IACA3B,EAAA2B,GAAA3B,EAAA2B,KAAA,EAEA1B,EAAAC,KAAAF,EAAA2B,GA2CA,SAAAqjB,EAAAhlB,EAAAC,EAAAC,GACAD,EAAAC,GAAA,IAAAF,EACAC,EAAAC,EAAA,GAAAF,IAAA,EAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,GAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,GA7JA+O,EAAArE,OAAAA,IAOAqE,EAAAjL,MAAA,SAAAC,GACA,OAAA,IAAArL,EAAAa,MAAAwK,IAKArL,EAAAa,QAAAA,QACAwV,EAAAjL,MAAApL,EAAAgqB,KAAA3T,EAAAjL,MAAApL,EAAAa,MAAAwE,UAAAob,WAUApK,EAAAhR,UAAAknB,EAAA,SAAA7rB,EAAAgL,EAAApE,GAGA,OAFAnC,KAAA8mB,KAAA9mB,KAAA8mB,KAAA9P,KAAA,IAAA2P,EAAAprB,EAAAgL,EAAApE,GACAnC,KAAAuG,KAAAA,EACAvG,OA8BAinB,EAAA/mB,UAAApB,OAAA+N,OAAA8Z,EAAAzmB,YACA3E,GAxBA,SAAA4G,EAAAC,EAAAC,GACA,KAAA,IAAAF,GACAC,EAAAC,KAAA,IAAAF,EAAA,IACAA,KAAA,EAEAC,EAAAC,GAAAF,GA0BA+O,EAAAhR,UAAAqb,OAAA,SAAA9b,GAWA,OARAO,KAAAuG,MAAAvG,KAAA8mB,KAAA9mB,KAAA8mB,KAAA9P,KAAA,IAAAiQ,GACAxnB,KAAA,GACA,IAAA,EACAA,EAAA,MAAA,EACAA,EAAA,QAAA,EACAA,EAAA,UAAA,EACA,EACAA,IAAA8G,IACAvG,MASAkR,EAAAhR,UAAAsb,MAAA,SAAA/b,GACA,OAAAA,EAAA,EACAO,KAAAonB,EAAAF,EAAA,GAAAxM,EAAAtL,WAAA3P,IACAO,KAAAub,OAAA9b,IAQAyR,EAAAhR,UAAAub,OAAA,SAAAhc,GACA,OAAAO,KAAAub,QAAA9b,GAAA,EAAAA,GAAA,MAAA,IAkCAyR,EAAAhR,UAAAgc,MAZAhL,EAAAhR,UAAAic,OAAA,SAAA1c,GACAyb,EAAAR,EAAAmJ,KAAApkB,GACA,OAAAO,KAAAonB,EAAAF,EAAAhM,EAAAtf,SAAAsf,IAkBAhK,EAAAhR,UAAAkc,OAAA,SAAA3c,GACAyb,EAAAR,EAAAmJ,KAAApkB,GAAAkkB,WACA,OAAA3jB,KAAAonB,EAAAF,EAAAhM,EAAAtf,SAAAsf,IAQAhK,EAAAhR,UAAAwb,KAAA,SAAAjc,GACA,OAAAO,KAAAonB,EAAAJ,EAAA,EAAAvnB,EAAA,EAAA,IAyBAyR,EAAAhR,UAAA0b,SAVA1K,EAAAhR,UAAAyb,QAAA,SAAAlc,GACA,OAAAO,KAAAonB,EAAAD,EAAA,EAAA1nB,IAAA,IA6BAyR,EAAAhR,UAAAqc,SAZArL,EAAAhR,UAAAoc,QAAA,SAAA7c,GACAyb,EAAAR,EAAAmJ,KAAApkB,GACA,OAAAO,KAAAonB,EAAAD,EAAA,EAAAjM,EAAApX,IAAAsjB,EAAAD,EAAA,EAAAjM,EAAAnX,KAkBAmN,EAAAhR,UAAA2b,MAAA,SAAApc,GACA,OAAAO,KAAAonB,EAAAvsB,EAAAghB,MAAAxX,aAAA,EAAA5E,IASAyR,EAAAhR,UAAA4b,OAAA,SAAArc,GACA,OAAAO,KAAAonB,EAAAvsB,EAAAghB,MAAA9W,cAAA,EAAAtF,IAGA,IAAA4nB,EAAAxsB,EAAAa,MAAAwE,UAAAwV,IACA,SAAAvT,EAAAC,EAAAC,GACAD,EAAAsT,IAAAvT,EAAAE,IAGA,SAAAF,EAAAC,EAAAC,GACA,IAAA,IAAAxF,EAAA,EAAAA,EAAAsF,EAAAvG,SAAAiB,EACAuF,EAAAC,EAAAxF,GAAAsF,EAAAtF,IAQAqU,EAAAhR,UAAAwL,MAAA,SAAAjM,GACA,IAIA2C,EAJAmE,EAAA9G,EAAA7D,SAAA,EACA,OAAA2K,GAEA1L,EAAA8S,SAAAlO,KACA2C,EAAA8O,EAAAjL,MAAAM,EAAAlK,EAAAT,OAAA6D,IACApD,EAAAwB,OAAA4B,EAAA2C,EAAA,GACA3C,EAAA2C,GAEApC,KAAAub,OAAAhV,GAAA6gB,EAAAC,EAAA9gB,EAAA9G,IANAO,KAAAonB,EAAAJ,EAAA,EAAA,IAcA9V,EAAAhR,UAAA5D,OAAA,SAAAmD,GACA,IAAA8G,EAAAD,EAAA1K,OAAA6D,GACA,OAAA8G,EACAvG,KAAAub,OAAAhV,GAAA6gB,EAAA9gB,EAAAG,MAAAF,EAAA9G,GACAO,KAAAonB,EAAAJ,EAAA,EAAA,IAQA9V,EAAAhR,UAAAwiB,KAAA,WAIA,OAHA1iB,KAAA+mB,OAAA,IAAAF,EAAA7mB,MACAA,KAAAoX,KAAApX,KAAA8mB,KAAA,IAAAH,EAAAC,EAAA,EAAA,GACA5mB,KAAAuG,IAAA,EACAvG,MAOAkR,EAAAhR,UAAAonB,MAAA,WAUA,OATAtnB,KAAA+mB,QACA/mB,KAAAoX,KAAApX,KAAA+mB,OAAA3P,KACApX,KAAA8mB,KAAA9mB,KAAA+mB,OAAAD,KACA9mB,KAAAuG,IAAAvG,KAAA+mB,OAAAxgB,IACAvG,KAAA+mB,OAAA/mB,KAAA+mB,OAAA/P,OAEAhX,KAAAoX,KAAApX,KAAA8mB,KAAA,IAAAH,EAAAC,EAAA,EAAA,GACA5mB,KAAAuG,IAAA,GAEAvG,MAOAkR,EAAAhR,UAAAyiB,OAAA,WACA,IAAAvL,EAAApX,KAAAoX,KACA0P,EAAA9mB,KAAA8mB,KACAvgB,EAAAvG,KAAAuG,IAOA,OANAvG,KAAAsnB,QAAA/L,OAAAhV,GACAA,IACAvG,KAAA8mB,KAAA9P,KAAAI,EAAAJ,KACAhX,KAAA8mB,KAAAA,EACA9mB,KAAAuG,KAAAA,GAEAvG,MAOAkR,EAAAhR,UAAA8c,OAAA,WAIA,IAHA,IAAA5F,EAAApX,KAAAoX,KAAAJ,KACA5U,EAAApC,KAAA8M,YAAA7G,MAAAjG,KAAAuG,KACAlE,EAAA,EACA+U,GACAA,EAAA7b,GAAA6b,EAAAjV,IAAAC,EAAAC,GACAA,GAAA+U,EAAA7Q,IACA6Q,EAAAA,EAAAJ,KAGA,OAAA5U,GAGA8O,EAAAhB,EAAA,SAAAqX,GACApW,EAAAoW,EACArW,EAAArE,OAAAA,IACAsE,EAAAjB,M,6BC9cA9U,EAAAR,QAAAuW,EAGA,IAAAD,EAAA5V,EAAA,IAGAT,IAFAsW,EAAAjR,UAAApB,OAAA+N,OAAAqE,EAAAhR,YAAA4M,YAAAqE,EAEA7V,EAAA,KAQA,SAAA6V,IACAD,EAAAvW,KAAAqF,MAwCA,SAAAwnB,EAAArlB,EAAAC,EAAAC,GACAF,EAAAvG,OAAA,GACAf,EAAAyL,KAAAG,MAAAtE,EAAAC,EAAAC,GACAD,EAAAgjB,UACAhjB,EAAAgjB,UAAAjjB,EAAAE,GAEAD,EAAAqE,MAAAtE,EAAAE,GA3CA8O,EAAAjB,EAAA,WAOAiB,EAAAlL,MAAApL,EAAAyqB,EAEAnU,EAAAsW,iBAAA5sB,EAAAigB,QAAAjgB,EAAAigB,OAAA5a,qBAAAyB,YAAA,QAAA9G,EAAAigB,OAAA5a,UAAAwV,IAAAjb,KACA,SAAA0H,EAAAC,EAAAC,GACAD,EAAAsT,IAAAvT,EAAAE,IAIA,SAAAF,EAAAC,EAAAC,GACA,GAAAF,EAAAulB,KACAvlB,EAAAulB,KAAAtlB,EAAAC,EAAA,EAAAF,EAAAvG,aACA,IAAA,IAAAiB,EAAA,EAAAA,EAAAsF,EAAAvG,QACAwG,EAAAC,KAAAF,EAAAtF,OAQAsU,EAAAjR,UAAAwL,MAAA,SAAAjM,GAGA,IAAA8G,GADA9G,EADA5E,EAAA8S,SAAAlO,GACA5E,EAAAwqB,EAAA5lB,EAAA,UACAA,GAAA7D,SAAA,EAIA,OAHAoE,KAAAub,OAAAhV,GACAA,GACAvG,KAAAonB,EAAAjW,EAAAsW,iBAAAlhB,EAAA9G,GACAO,MAeAmR,EAAAjR,UAAA5D,OAAA,SAAAmD,GACA,IAAA8G,EAAA1L,EAAAigB,OAAA6M,WAAAloB,GAIA,OAHAO,KAAAub,OAAAhV,GACAA,GACAvG,KAAAonB,EAAAI,EAAAjhB,EAAA9G,GACAO,MAWAmR,EAAAjB,8B3CpFA", "file": "protobuf.min.js", "sourcesContent": ["(function prelude(modules, cache, entries) {\n\n    // This is the prelude used to bundle protobuf.js for the browser. Wraps up the CommonJS\n    // sources through a conflict-free require shim and is again wrapped within an iife that\n    // provides a minification-friendly `undefined` var plus a global \"use strict\" directive\n    // so that minification can remove the directives of each module.\n\n    function $require(name) {\n        var $module = cache[name];\n        if (!$module)\n            modules[name][0].call($module = cache[name] = { exports: {} }, $require, $module, $module.exports);\n        return $module.exports;\n    }\n\n    var protobuf = $require(entries[0]);\n\n    // Expose globally\n    protobuf.util.global.protobuf = protobuf;\n\n    // Be nice to AMD\n    if (typeof define === \"function\" && define.amd)\n        define([\"long\"], function(Long) {\n            if (Long && Long.isLong) {\n                protobuf.util.Long = Long;\n                protobuf.configure();\n            }\n            return protobuf;\n        });\n\n    // Be nice to CommonJS\n    if (typeof module === \"object\" && module && module.exports)\n        module.exports = protobuf;\n\n})/* end of prelude */", "\"use strict\";\r\nmodule.exports = asPromise;\r\n\r\n/**\r\n * Callback as used by {@link util.asPromise}.\r\n * @typedef asPromiseCallback\r\n * @type {function}\r\n * @param {Error|null} error Error, if any\r\n * @param {...*} params Additional arguments\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Returns a promise from a node-style callback function.\r\n * @memberof util\r\n * @param {asPromiseCallback} fn Function to call\r\n * @param {*} ctx Function context\r\n * @param {...*} params Function arguments\r\n * @returns {Promise<*>} Promisified function\r\n */\r\nfunction asPromise(fn, ctx/*, varargs */) {\r\n    var params  = new Array(arguments.length - 1),\r\n        offset  = 0,\r\n        index   = 2,\r\n        pending = true;\r\n    while (index < arguments.length)\r\n        params[offset++] = arguments[index++];\r\n    return new Promise(function executor(resolve, reject) {\r\n        params[offset] = function callback(err/*, varargs */) {\r\n            if (pending) {\r\n                pending = false;\r\n                if (err)\r\n                    reject(err);\r\n                else {\r\n                    var params = new Array(arguments.length - 1),\r\n                        offset = 0;\r\n                    while (offset < params.length)\r\n                        params[offset++] = arguments[offset];\r\n                    resolve.apply(null, params);\r\n                }\r\n            }\r\n        };\r\n        try {\r\n            fn.apply(ctx || null, params);\r\n        } catch (err) {\r\n            if (pending) {\r\n                pending = false;\r\n                reject(err);\r\n            }\r\n        }\r\n    });\r\n}\r\n", "\"use strict\";\r\n\r\n/**\r\n * A minimal base64 implementation for number arrays.\r\n * @memberof util\r\n * @namespace\r\n */\r\nvar base64 = exports;\r\n\r\n/**\r\n * Calculates the byte length of a base64 encoded string.\r\n * @param {string} string Base64 encoded string\r\n * @returns {number} Byte length\r\n */\r\nbase64.length = function length(string) {\r\n    var p = string.length;\r\n    if (!p)\r\n        return 0;\r\n    var n = 0;\r\n    while (--p % 4 > 1 && string.charAt(p) === \"=\")\r\n        ++n;\r\n    return Math.ceil(string.length * 3) / 4 - n;\r\n};\r\n\r\n// Base64 encoding table\r\nvar b64 = new Array(64);\r\n\r\n// Base64 decoding table\r\nvar s64 = new Array(123);\r\n\r\n// 65..90, 97..122, 48..57, 43, 47\r\nfor (var i = 0; i < 64;)\r\n    s64[b64[i] = i < 26 ? i + 65 : i < 52 ? i + 71 : i < 62 ? i - 4 : i - 59 | 43] = i++;\r\n\r\n/**\r\n * Encodes a buffer to a base64 encoded string.\r\n * @param {Uint8Array} buffer Source buffer\r\n * @param {number} start Source start\r\n * @param {number} end Source end\r\n * @returns {string} Base64 encoded string\r\n */\r\nbase64.encode = function encode(buffer, start, end) {\r\n    var parts = null,\r\n        chunk = [];\r\n    var i = 0, // output index\r\n        j = 0, // goto index\r\n        t;     // temporary\r\n    while (start < end) {\r\n        var b = buffer[start++];\r\n        switch (j) {\r\n            case 0:\r\n                chunk[i++] = b64[b >> 2];\r\n                t = (b & 3) << 4;\r\n                j = 1;\r\n                break;\r\n            case 1:\r\n                chunk[i++] = b64[t | b >> 4];\r\n                t = (b & 15) << 2;\r\n                j = 2;\r\n                break;\r\n            case 2:\r\n                chunk[i++] = b64[t | b >> 6];\r\n                chunk[i++] = b64[b & 63];\r\n                j = 0;\r\n                break;\r\n        }\r\n        if (i > 8191) {\r\n            (parts || (parts = [])).push(String.fromCharCode.apply(String, chunk));\r\n            i = 0;\r\n        }\r\n    }\r\n    if (j) {\r\n        chunk[i++] = b64[t];\r\n        chunk[i++] = 61;\r\n        if (j === 1)\r\n            chunk[i++] = 61;\r\n    }\r\n    if (parts) {\r\n        if (i)\r\n            parts.push(String.fromCharCode.apply(String, chunk.slice(0, i)));\r\n        return parts.join(\"\");\r\n    }\r\n    return String.fromCharCode.apply(String, chunk.slice(0, i));\r\n};\r\n\r\nvar invalidEncoding = \"invalid encoding\";\r\n\r\n/**\r\n * Decodes a base64 encoded string to a buffer.\r\n * @param {string} string Source string\r\n * @param {Uint8Array} buffer Destination buffer\r\n * @param {number} offset Destination offset\r\n * @returns {number} Number of bytes written\r\n * @throws {Error} If encoding is invalid\r\n */\r\nbase64.decode = function decode(string, buffer, offset) {\r\n    var start = offset;\r\n    var j = 0, // goto index\r\n        t;     // temporary\r\n    for (var i = 0; i < string.length;) {\r\n        var c = string.charCodeAt(i++);\r\n        if (c === 61 && j > 1)\r\n            break;\r\n        if ((c = s64[c]) === undefined)\r\n            throw Error(invalidEncoding);\r\n        switch (j) {\r\n            case 0:\r\n                t = c;\r\n                j = 1;\r\n                break;\r\n            case 1:\r\n                buffer[offset++] = t << 2 | (c & 48) >> 4;\r\n                t = c;\r\n                j = 2;\r\n                break;\r\n            case 2:\r\n                buffer[offset++] = (t & 15) << 4 | (c & 60) >> 2;\r\n                t = c;\r\n                j = 3;\r\n                break;\r\n            case 3:\r\n                buffer[offset++] = (t & 3) << 6 | c;\r\n                j = 0;\r\n                break;\r\n        }\r\n    }\r\n    if (j === 1)\r\n        throw Error(invalidEncoding);\r\n    return offset - start;\r\n};\r\n\r\n/**\r\n * Tests if the specified string appears to be base64 encoded.\r\n * @param {string} string String to test\r\n * @returns {boolean} `true` if probably base64 encoded, otherwise false\r\n */\r\nbase64.test = function test(string) {\r\n    return /^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/.test(string);\r\n};\r\n", "\"use strict\";\r\nmodule.exports = codegen;\r\n\r\n/**\r\n * Begins generating a function.\r\n * @memberof util\r\n * @param {string[]} functionParams Function parameter names\r\n * @param {string} [functionName] Function name if not anonymous\r\n * @returns {Codegen} Appender that appends code to the function's body\r\n */\r\nfunction codegen(functionParams, functionName) {\r\n\r\n    /* istanbul ignore if */\r\n    if (typeof functionParams === \"string\") {\r\n        functionName = functionParams;\r\n        functionParams = undefined;\r\n    }\r\n\r\n    var body = [];\r\n\r\n    /**\r\n     * Appends code to the function's body or finishes generation.\r\n     * @typedef Codegen\r\n     * @type {function}\r\n     * @param {string|Object.<string,*>} [formatStringOrScope] Format string or, to finish the function, an object of additional scope variables, if any\r\n     * @param {...*} [formatParams] Format parameters\r\n     * @returns {Codegen|Function} Itself or the generated function if finished\r\n     * @throws {Error} If format parameter counts do not match\r\n     */\r\n\r\n    function Codegen(formatStringOrScope) {\r\n        // note that explicit array handling below makes this ~50% faster\r\n\r\n        // finish the function\r\n        if (typeof formatStringOrScope !== \"string\") {\r\n            var source = toString();\r\n            if (codegen.verbose)\r\n                console.log(\"codegen: \" + source); // eslint-disable-line no-console\r\n            source = \"return \" + source;\r\n            if (formatStringOrScope) {\r\n                var scopeKeys   = Object.keys(formatStringOrScope),\r\n                    scopeParams = new Array(scopeKeys.length + 1),\r\n                    scopeValues = new Array(scopeKeys.length),\r\n                    scopeOffset = 0;\r\n                while (scopeOffset < scopeKeys.length) {\r\n                    scopeParams[scopeOffset] = scopeKeys[scopeOffset];\r\n                    scopeValues[scopeOffset] = formatStringOrScope[scopeKeys[scopeOffset++]];\r\n                }\r\n                scopeParams[scopeOffset] = source;\r\n                return Function.apply(null, scopeParams).apply(null, scopeValues); // eslint-disable-line no-new-func\r\n            }\r\n            return Function(source)(); // eslint-disable-line no-new-func\r\n        }\r\n\r\n        // otherwise append to body\r\n        var formatParams = new Array(arguments.length - 1),\r\n            formatOffset = 0;\r\n        while (formatOffset < formatParams.length)\r\n            formatParams[formatOffset] = arguments[++formatOffset];\r\n        formatOffset = 0;\r\n        formatStringOrScope = formatStringOrScope.replace(/%([%dfijs])/g, function replace($0, $1) {\r\n            var value = formatParams[formatOffset++];\r\n            switch ($1) {\r\n                case \"d\": case \"f\": return String(Number(value));\r\n                case \"i\": return String(Math.floor(value));\r\n                case \"j\": return JSON.stringify(value);\r\n                case \"s\": return String(value);\r\n            }\r\n            return \"%\";\r\n        });\r\n        if (formatOffset !== formatParams.length)\r\n            throw Error(\"parameter count mismatch\");\r\n        body.push(formatStringOrScope);\r\n        return Codegen;\r\n    }\r\n\r\n    function toString(functionNameOverride) {\r\n        return \"function \" + (functionNameOverride || functionName || \"\") + \"(\" + (functionParams && functionParams.join(\",\") || \"\") + \"){\\n  \" + body.join(\"\\n  \") + \"\\n}\";\r\n    }\r\n\r\n    Codegen.toString = toString;\r\n    return Codegen;\r\n}\r\n\r\n/**\r\n * Begins generating a function.\r\n * @memberof util\r\n * @function codegen\r\n * @param {string} [functionName] Function name if not anonymous\r\n * @returns {Codegen} Appender that appends code to the function's body\r\n * @variation 2\r\n */\r\n\r\n/**\r\n * When set to `true`, codegen will log generated code to console. Useful for debugging.\r\n * @name util.codegen.verbose\r\n * @type {boolean}\r\n */\r\ncodegen.verbose = false;\r\n", "\"use strict\";\r\nmodule.exports = EventEmitter;\r\n\r\n/**\r\n * Constructs a new event emitter instance.\r\n * @classdesc A minimal event emitter.\r\n * @memberof util\r\n * @constructor\r\n */\r\nfunction EventEmitter() {\r\n\r\n    /**\r\n     * Registered listeners.\r\n     * @type {Object.<string,*>}\r\n     * @private\r\n     */\r\n    this._listeners = {};\r\n}\r\n\r\n/**\r\n * Registers an event listener.\r\n * @param {string} evt Event name\r\n * @param {function} fn Listener\r\n * @param {*} [ctx] Listener context\r\n * @returns {util.EventEmitter} `this`\r\n */\r\nEventEmitter.prototype.on = function on(evt, fn, ctx) {\r\n    (this._listeners[evt] || (this._listeners[evt] = [])).push({\r\n        fn  : fn,\r\n        ctx : ctx || this\r\n    });\r\n    return this;\r\n};\r\n\r\n/**\r\n * Removes an event listener or any matching listeners if arguments are omitted.\r\n * @param {string} [evt] Event name. Removes all listeners if omitted.\r\n * @param {function} [fn] Listener to remove. Removes all listeners of `evt` if omitted.\r\n * @returns {util.EventEmitter} `this`\r\n */\r\nEventEmitter.prototype.off = function off(evt, fn) {\r\n    if (evt === undefined)\r\n        this._listeners = {};\r\n    else {\r\n        if (fn === undefined)\r\n            this._listeners[evt] = [];\r\n        else {\r\n            var listeners = this._listeners[evt];\r\n            for (var i = 0; i < listeners.length;)\r\n                if (listeners[i].fn === fn)\r\n                    listeners.splice(i, 1);\r\n                else\r\n                    ++i;\r\n        }\r\n    }\r\n    return this;\r\n};\r\n\r\n/**\r\n * Emits an event by calling its listeners with the specified arguments.\r\n * @param {string} evt Event name\r\n * @param {...*} args Arguments\r\n * @returns {util.EventEmitter} `this`\r\n */\r\nEventEmitter.prototype.emit = function emit(evt) {\r\n    var listeners = this._listeners[evt];\r\n    if (listeners) {\r\n        var args = [],\r\n            i = 1;\r\n        for (; i < arguments.length;)\r\n            args.push(arguments[i++]);\r\n        for (i = 0; i < listeners.length;)\r\n            listeners[i].fn.apply(listeners[i++].ctx, args);\r\n    }\r\n    return this;\r\n};\r\n", "\"use strict\";\r\nmodule.exports = fetch;\r\n\r\nvar asPromise = require(1),\r\n    inquire   = require(7);\r\n\r\nvar fs = inquire(\"fs\");\r\n\r\n/**\r\n * Node-style callback as used by {@link util.fetch}.\r\n * @typedef FetchCallback\r\n * @type {function}\r\n * @param {?Error} error Error, if any, otherwise `null`\r\n * @param {string} [contents] File contents, if there hasn't been an error\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Options as used by {@link util.fetch}.\r\n * @typedef FetchOptions\r\n * @type {Object}\r\n * @property {boolean} [binary=false] Whether expecting a binary response\r\n * @property {boolean} [xhr=false] If `true`, forces the use of XMLHttpRequest\r\n */\r\n\r\n/**\r\n * Fetches the contents of a file.\r\n * @memberof util\r\n * @param {string} filename File path or url\r\n * @param {FetchOptions} options Fetch options\r\n * @param {FetchCallback} callback Callback function\r\n * @returns {undefined}\r\n */\r\nfunction fetch(filename, options, callback) {\r\n    if (typeof options === \"function\") {\r\n        callback = options;\r\n        options = {};\r\n    } else if (!options)\r\n        options = {};\r\n\r\n    if (!callback)\r\n        return asPromise(fetch, this, filename, options); // eslint-disable-line no-invalid-this\r\n\r\n    // if a node-like filesystem is present, try it first but fall back to XHR if nothing is found.\r\n    if (!options.xhr && fs && fs.readFile)\r\n        return fs.readFile(filename, function fetchReadFileCallback(err, contents) {\r\n            return err && typeof XMLHttpRequest !== \"undefined\"\r\n                ? fetch.xhr(filename, options, callback)\r\n                : err\r\n                ? callback(err)\r\n                : callback(null, options.binary ? contents : contents.toString(\"utf8\"));\r\n        });\r\n\r\n    // use the XHR version otherwise.\r\n    return fetch.xhr(filename, options, callback);\r\n}\r\n\r\n/**\r\n * Fetches the contents of a file.\r\n * @name util.fetch\r\n * @function\r\n * @param {string} path File path or url\r\n * @param {FetchCallback} callback Callback function\r\n * @returns {undefined}\r\n * @variation 2\r\n */\r\n\r\n/**\r\n * Fetches the contents of a file.\r\n * @name util.fetch\r\n * @function\r\n * @param {string} path File path or url\r\n * @param {FetchOptions} [options] Fetch options\r\n * @returns {Promise<string|Uint8Array>} Promise\r\n * @variation 3\r\n */\r\n\r\n/**/\r\nfetch.xhr = function fetch_xhr(filename, options, callback) {\r\n    var xhr = new XMLHttpRequest();\r\n    xhr.onreadystatechange /* works everywhere */ = function fetchOnReadyStateChange() {\r\n\r\n        if (xhr.readyState !== 4)\r\n            return undefined;\r\n\r\n        // local cors security errors return status 0 / empty string, too. afaik this cannot be\r\n        // reliably distinguished from an actually empty file for security reasons. feel free\r\n        // to send a pull request if you are aware of a solution.\r\n        if (xhr.status !== 0 && xhr.status !== 200)\r\n            return callback(Error(\"status \" + xhr.status));\r\n\r\n        // if binary data is expected, make sure that some sort of array is returned, even if\r\n        // ArrayBuffers are not supported. the binary string fallback, however, is unsafe.\r\n        if (options.binary) {\r\n            var buffer = xhr.response;\r\n            if (!buffer) {\r\n                buffer = [];\r\n                for (var i = 0; i < xhr.responseText.length; ++i)\r\n                    buffer.push(xhr.responseText.charCodeAt(i) & 255);\r\n            }\r\n            return callback(null, typeof Uint8Array !== \"undefined\" ? new Uint8Array(buffer) : buffer);\r\n        }\r\n        return callback(null, xhr.responseText);\r\n    };\r\n\r\n    if (options.binary) {\r\n        // ref: https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest/Sending_and_Receiving_Binary_Data#Receiving_binary_data_in_older_browsers\r\n        if (\"overrideMimeType\" in xhr)\r\n            xhr.overrideMimeType(\"text/plain; charset=x-user-defined\");\r\n        xhr.responseType = \"arraybuffer\";\r\n    }\r\n\r\n    xhr.open(\"GET\", filename);\r\n    xhr.send();\r\n};\r\n", "\"use strict\";\r\n\r\nmodule.exports = factory(factory);\r\n\r\n/**\r\n * Reads / writes floats / doubles from / to buffers.\r\n * @name util.float\r\n * @namespace\r\n */\r\n\r\n/**\r\n * Writes a 32 bit float to a buffer using little endian byte order.\r\n * @name util.float.writeFloatLE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Writes a 32 bit float to a buffer using big endian byte order.\r\n * @name util.float.writeFloatBE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Reads a 32 bit float from a buffer using little endian byte order.\r\n * @name util.float.readFloatLE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n/**\r\n * Reads a 32 bit float from a buffer using big endian byte order.\r\n * @name util.float.readFloatBE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n/**\r\n * Writes a 64 bit double to a buffer using little endian byte order.\r\n * @name util.float.writeDoubleLE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Writes a 64 bit double to a buffer using big endian byte order.\r\n * @name util.float.writeDoubleBE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Reads a 64 bit double from a buffer using little endian byte order.\r\n * @name util.float.readDoubleLE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n/**\r\n * Reads a 64 bit double from a buffer using big endian byte order.\r\n * @name util.float.readDoubleBE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n// Factory function for the purpose of node-based testing in modified global environments\r\nfunction factory(exports) {\r\n\r\n    // float: typed array\r\n    if (typeof Float32Array !== \"undefined\") (function() {\r\n\r\n        var f32 = new Float32Array([ -0 ]),\r\n            f8b = new Uint8Array(f32.buffer),\r\n            le  = f8b[3] === 128;\r\n\r\n        function writeFloat_f32_cpy(val, buf, pos) {\r\n            f32[0] = val;\r\n            buf[pos    ] = f8b[0];\r\n            buf[pos + 1] = f8b[1];\r\n            buf[pos + 2] = f8b[2];\r\n            buf[pos + 3] = f8b[3];\r\n        }\r\n\r\n        function writeFloat_f32_rev(val, buf, pos) {\r\n            f32[0] = val;\r\n            buf[pos    ] = f8b[3];\r\n            buf[pos + 1] = f8b[2];\r\n            buf[pos + 2] = f8b[1];\r\n            buf[pos + 3] = f8b[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.writeFloatLE = le ? writeFloat_f32_cpy : writeFloat_f32_rev;\r\n        /* istanbul ignore next */\r\n        exports.writeFloatBE = le ? writeFloat_f32_rev : writeFloat_f32_cpy;\r\n\r\n        function readFloat_f32_cpy(buf, pos) {\r\n            f8b[0] = buf[pos    ];\r\n            f8b[1] = buf[pos + 1];\r\n            f8b[2] = buf[pos + 2];\r\n            f8b[3] = buf[pos + 3];\r\n            return f32[0];\r\n        }\r\n\r\n        function readFloat_f32_rev(buf, pos) {\r\n            f8b[3] = buf[pos    ];\r\n            f8b[2] = buf[pos + 1];\r\n            f8b[1] = buf[pos + 2];\r\n            f8b[0] = buf[pos + 3];\r\n            return f32[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.readFloatLE = le ? readFloat_f32_cpy : readFloat_f32_rev;\r\n        /* istanbul ignore next */\r\n        exports.readFloatBE = le ? readFloat_f32_rev : readFloat_f32_cpy;\r\n\r\n    // float: ieee754\r\n    })(); else (function() {\r\n\r\n        function writeFloat_ieee754(writeUint, val, buf, pos) {\r\n            var sign = val < 0 ? 1 : 0;\r\n            if (sign)\r\n                val = -val;\r\n            if (val === 0)\r\n                writeUint(1 / val > 0 ? /* positive */ 0 : /* negative 0 */ 2147483648, buf, pos);\r\n            else if (isNaN(val))\r\n                writeUint(2143289344, buf, pos);\r\n            else if (val > 3.4028234663852886e+38) // +-Infinity\r\n                writeUint((sign << 31 | 2139095040) >>> 0, buf, pos);\r\n            else if (val < 1.1754943508222875e-38) // denormal\r\n                writeUint((sign << 31 | Math.round(val / 1.401298464324817e-45)) >>> 0, buf, pos);\r\n            else {\r\n                var exponent = Math.floor(Math.log(val) / Math.LN2),\r\n                    mantissa = Math.round(val * Math.pow(2, -exponent) * 8388608) & 8388607;\r\n                writeUint((sign << 31 | exponent + 127 << 23 | mantissa) >>> 0, buf, pos);\r\n            }\r\n        }\r\n\r\n        exports.writeFloatLE = writeFloat_ieee754.bind(null, writeUintLE);\r\n        exports.writeFloatBE = writeFloat_ieee754.bind(null, writeUintBE);\r\n\r\n        function readFloat_ieee754(readUint, buf, pos) {\r\n            var uint = readUint(buf, pos),\r\n                sign = (uint >> 31) * 2 + 1,\r\n                exponent = uint >>> 23 & 255,\r\n                mantissa = uint & 8388607;\r\n            return exponent === 255\r\n                ? mantissa\r\n                ? NaN\r\n                : sign * Infinity\r\n                : exponent === 0 // denormal\r\n                ? sign * 1.401298464324817e-45 * mantissa\r\n                : sign * Math.pow(2, exponent - 150) * (mantissa + 8388608);\r\n        }\r\n\r\n        exports.readFloatLE = readFloat_ieee754.bind(null, readUintLE);\r\n        exports.readFloatBE = readFloat_ieee754.bind(null, readUintBE);\r\n\r\n    })();\r\n\r\n    // double: typed array\r\n    if (typeof Float64Array !== \"undefined\") (function() {\r\n\r\n        var f64 = new Float64Array([-0]),\r\n            f8b = new Uint8Array(f64.buffer),\r\n            le  = f8b[7] === 128;\r\n\r\n        function writeDouble_f64_cpy(val, buf, pos) {\r\n            f64[0] = val;\r\n            buf[pos    ] = f8b[0];\r\n            buf[pos + 1] = f8b[1];\r\n            buf[pos + 2] = f8b[2];\r\n            buf[pos + 3] = f8b[3];\r\n            buf[pos + 4] = f8b[4];\r\n            buf[pos + 5] = f8b[5];\r\n            buf[pos + 6] = f8b[6];\r\n            buf[pos + 7] = f8b[7];\r\n        }\r\n\r\n        function writeDouble_f64_rev(val, buf, pos) {\r\n            f64[0] = val;\r\n            buf[pos    ] = f8b[7];\r\n            buf[pos + 1] = f8b[6];\r\n            buf[pos + 2] = f8b[5];\r\n            buf[pos + 3] = f8b[4];\r\n            buf[pos + 4] = f8b[3];\r\n            buf[pos + 5] = f8b[2];\r\n            buf[pos + 6] = f8b[1];\r\n            buf[pos + 7] = f8b[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.writeDoubleLE = le ? writeDouble_f64_cpy : writeDouble_f64_rev;\r\n        /* istanbul ignore next */\r\n        exports.writeDoubleBE = le ? writeDouble_f64_rev : writeDouble_f64_cpy;\r\n\r\n        function readDouble_f64_cpy(buf, pos) {\r\n            f8b[0] = buf[pos    ];\r\n            f8b[1] = buf[pos + 1];\r\n            f8b[2] = buf[pos + 2];\r\n            f8b[3] = buf[pos + 3];\r\n            f8b[4] = buf[pos + 4];\r\n            f8b[5] = buf[pos + 5];\r\n            f8b[6] = buf[pos + 6];\r\n            f8b[7] = buf[pos + 7];\r\n            return f64[0];\r\n        }\r\n\r\n        function readDouble_f64_rev(buf, pos) {\r\n            f8b[7] = buf[pos    ];\r\n            f8b[6] = buf[pos + 1];\r\n            f8b[5] = buf[pos + 2];\r\n            f8b[4] = buf[pos + 3];\r\n            f8b[3] = buf[pos + 4];\r\n            f8b[2] = buf[pos + 5];\r\n            f8b[1] = buf[pos + 6];\r\n            f8b[0] = buf[pos + 7];\r\n            return f64[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.readDoubleLE = le ? readDouble_f64_cpy : readDouble_f64_rev;\r\n        /* istanbul ignore next */\r\n        exports.readDoubleBE = le ? readDouble_f64_rev : readDouble_f64_cpy;\r\n\r\n    // double: ieee754\r\n    })(); else (function() {\r\n\r\n        function writeDouble_ieee754(writeUint, off0, off1, val, buf, pos) {\r\n            var sign = val < 0 ? 1 : 0;\r\n            if (sign)\r\n                val = -val;\r\n            if (val === 0) {\r\n                writeUint(0, buf, pos + off0);\r\n                writeUint(1 / val > 0 ? /* positive */ 0 : /* negative 0 */ 2147483648, buf, pos + off1);\r\n            } else if (isNaN(val)) {\r\n                writeUint(0, buf, pos + off0);\r\n                writeUint(2146959360, buf, pos + off1);\r\n            } else if (val > 1.7976931348623157e+308) { // +-Infinity\r\n                writeUint(0, buf, pos + off0);\r\n                writeUint((sign << 31 | 2146435072) >>> 0, buf, pos + off1);\r\n            } else {\r\n                var mantissa;\r\n                if (val < 2.2250738585072014e-308) { // denormal\r\n                    mantissa = val / 5e-324;\r\n                    writeUint(mantissa >>> 0, buf, pos + off0);\r\n                    writeUint((sign << 31 | mantissa / 4294967296) >>> 0, buf, pos + off1);\r\n                } else {\r\n                    var exponent = Math.floor(Math.log(val) / Math.LN2);\r\n                    if (exponent === 1024)\r\n                        exponent = 1023;\r\n                    mantissa = val * Math.pow(2, -exponent);\r\n                    writeUint(mantissa * 4503599627370496 >>> 0, buf, pos + off0);\r\n                    writeUint((sign << 31 | exponent + 1023 << 20 | mantissa * 1048576 & 1048575) >>> 0, buf, pos + off1);\r\n                }\r\n            }\r\n        }\r\n\r\n        exports.writeDoubleLE = writeDouble_ieee754.bind(null, writeUintLE, 0, 4);\r\n        exports.writeDoubleBE = writeDouble_ieee754.bind(null, writeUintBE, 4, 0);\r\n\r\n        function readDouble_ieee754(readUint, off0, off1, buf, pos) {\r\n            var lo = readUint(buf, pos + off0),\r\n                hi = readUint(buf, pos + off1);\r\n            var sign = (hi >> 31) * 2 + 1,\r\n                exponent = hi >>> 20 & 2047,\r\n                mantissa = 4294967296 * (hi & 1048575) + lo;\r\n            return exponent === 2047\r\n                ? mantissa\r\n                ? NaN\r\n                : sign * Infinity\r\n                : exponent === 0 // denormal\r\n                ? sign * 5e-324 * mantissa\r\n                : sign * Math.pow(2, exponent - 1075) * (mantissa + 4503599627370496);\r\n        }\r\n\r\n        exports.readDoubleLE = readDouble_ieee754.bind(null, readUintLE, 0, 4);\r\n        exports.readDoubleBE = readDouble_ieee754.bind(null, readUintBE, 4, 0);\r\n\r\n    })();\r\n\r\n    return exports;\r\n}\r\n\r\n// uint helpers\r\n\r\nfunction writeUintLE(val, buf, pos) {\r\n    buf[pos    ] =  val        & 255;\r\n    buf[pos + 1] =  val >>> 8  & 255;\r\n    buf[pos + 2] =  val >>> 16 & 255;\r\n    buf[pos + 3] =  val >>> 24;\r\n}\r\n\r\nfunction writeUintBE(val, buf, pos) {\r\n    buf[pos    ] =  val >>> 24;\r\n    buf[pos + 1] =  val >>> 16 & 255;\r\n    buf[pos + 2] =  val >>> 8  & 255;\r\n    buf[pos + 3] =  val        & 255;\r\n}\r\n\r\nfunction readUintLE(buf, pos) {\r\n    return (buf[pos    ]\r\n          | buf[pos + 1] << 8\r\n          | buf[pos + 2] << 16\r\n          | buf[pos + 3] << 24) >>> 0;\r\n}\r\n\r\nfunction readUintBE(buf, pos) {\r\n    return (buf[pos    ] << 24\r\n          | buf[pos + 1] << 16\r\n          | buf[pos + 2] << 8\r\n          | buf[pos + 3]) >>> 0;\r\n}\r\n", "\"use strict\";\r\nmodule.exports = inquire;\r\n\r\n/**\r\n * Requires a module only if available.\r\n * @memberof util\r\n * @param {string} moduleName Module to require\r\n * @returns {?Object} Required module if available and not empty, otherwise `null`\r\n */\r\nfunction inquire(moduleName) {\r\n    try {\r\n        var mod = eval(\"quire\".replace(/^/,\"re\"))(moduleName); // eslint-disable-line no-eval\r\n        if (mod && (mod.length || Object.keys(mod).length))\r\n            return mod;\r\n    } catch (e) {} // eslint-disable-line no-empty\r\n    return null;\r\n}\r\n", "\"use strict\";\r\n\r\n/**\r\n * A minimal path module to resolve Unix, Windows and URL paths alike.\r\n * @memberof util\r\n * @namespace\r\n */\r\nvar path = exports;\r\n\r\nvar isAbsolute =\r\n/**\r\n * Tests if the specified path is absolute.\r\n * @param {string} path Path to test\r\n * @returns {boolean} `true` if path is absolute\r\n */\r\npath.isAbsolute = function isAbsolute(path) {\r\n    return /^(?:\\/|\\w+:)/.test(path);\r\n};\r\n\r\nvar normalize =\r\n/**\r\n * Normalizes the specified path.\r\n * @param {string} path Path to normalize\r\n * @returns {string} Normalized path\r\n */\r\npath.normalize = function normalize(path) {\r\n    path = path.replace(/\\\\/g, \"/\")\r\n               .replace(/\\/{2,}/g, \"/\");\r\n    var parts    = path.split(\"/\"),\r\n        absolute = isAbsolute(path),\r\n        prefix   = \"\";\r\n    if (absolute)\r\n        prefix = parts.shift() + \"/\";\r\n    for (var i = 0; i < parts.length;) {\r\n        if (parts[i] === \"..\") {\r\n            if (i > 0 && parts[i - 1] !== \"..\")\r\n                parts.splice(--i, 2);\r\n            else if (absolute)\r\n                parts.splice(i, 1);\r\n            else\r\n                ++i;\r\n        } else if (parts[i] === \".\")\r\n            parts.splice(i, 1);\r\n        else\r\n            ++i;\r\n    }\r\n    return prefix + parts.join(\"/\");\r\n};\r\n\r\n/**\r\n * Resolves the specified include path against the specified origin path.\r\n * @param {string} originPath Path to the origin file\r\n * @param {string} includePath Include path relative to origin path\r\n * @param {boolean} [alreadyNormalized=false] `true` if both paths are already known to be normalized\r\n * @returns {string} Path to the include file\r\n */\r\npath.resolve = function resolve(originPath, includePath, alreadyNormalized) {\r\n    if (!alreadyNormalized)\r\n        includePath = normalize(includePath);\r\n    if (isAbsolute(includePath))\r\n        return includePath;\r\n    if (!alreadyNormalized)\r\n        originPath = normalize(originPath);\r\n    return (originPath = originPath.replace(/(?:\\/|^)[^/]+$/, \"\")).length ? normalize(originPath + \"/\" + includePath) : includePath;\r\n};\r\n", "\"use strict\";\r\nmodule.exports = pool;\r\n\r\n/**\r\n * An allocator as used by {@link util.pool}.\r\n * @typedef PoolAllocator\r\n * @type {function}\r\n * @param {number} size Buffer size\r\n * @returns {Uint8Array} Buffer\r\n */\r\n\r\n/**\r\n * A slicer as used by {@link util.pool}.\r\n * @typedef PoolSlicer\r\n * @type {function}\r\n * @param {number} start Start offset\r\n * @param {number} end End offset\r\n * @returns {Uint8Array} Buffer slice\r\n * @this {Uint8Array}\r\n */\r\n\r\n/**\r\n * A general purpose buffer pool.\r\n * @memberof util\r\n * @function\r\n * @param {PoolAllocator} alloc Allocator\r\n * @param {PoolSlicer} slice Slicer\r\n * @param {number} [size=8192] Slab size\r\n * @returns {PoolAllocator} Pooled allocator\r\n */\r\nfunction pool(alloc, slice, size) {\r\n    var SIZE   = size || 8192;\r\n    var MAX    = SIZE >>> 1;\r\n    var slab   = null;\r\n    var offset = SIZE;\r\n    return function pool_alloc(size) {\r\n        if (size < 1 || size > MAX)\r\n            return alloc(size);\r\n        if (offset + size > SIZE) {\r\n            slab = alloc(SIZE);\r\n            offset = 0;\r\n        }\r\n        var buf = slice.call(slab, offset, offset += size);\r\n        if (offset & 7) // align to 32 bit\r\n            offset = (offset | 7) + 1;\r\n        return buf;\r\n    };\r\n}\r\n", "\"use strict\";\r\n\r\n/**\r\n * A minimal UTF8 implementation for number arrays.\r\n * @memberof util\r\n * @namespace\r\n */\r\nvar utf8 = exports;\r\n\r\n/**\r\n * Calculates the UTF8 byte length of a string.\r\n * @param {string} string String\r\n * @returns {number} Byte length\r\n */\r\nutf8.length = function utf8_length(string) {\r\n    var len = 0,\r\n        c = 0;\r\n    for (var i = 0; i < string.length; ++i) {\r\n        c = string.charCodeAt(i);\r\n        if (c < 128)\r\n            len += 1;\r\n        else if (c < 2048)\r\n            len += 2;\r\n        else if ((c & 0xFC00) === 0xD800 && (string.charCodeAt(i + 1) & 0xFC00) === 0xDC00) {\r\n            ++i;\r\n            len += 4;\r\n        } else\r\n            len += 3;\r\n    }\r\n    return len;\r\n};\r\n\r\n/**\r\n * Reads UTF8 bytes as a string.\r\n * @param {Uint8Array} buffer Source buffer\r\n * @param {number} start Source start\r\n * @param {number} end Source end\r\n * @returns {string} String read\r\n */\r\nutf8.read = function utf8_read(buffer, start, end) {\r\n    var len = end - start;\r\n    if (len < 1)\r\n        return \"\";\r\n    var parts = null,\r\n        chunk = [],\r\n        i = 0, // char offset\r\n        t;     // temporary\r\n    while (start < end) {\r\n        t = buffer[start++];\r\n        if (t < 128)\r\n            chunk[i++] = t;\r\n        else if (t > 191 && t < 224)\r\n            chunk[i++] = (t & 31) << 6 | buffer[start++] & 63;\r\n        else if (t > 239 && t < 365) {\r\n            t = ((t & 7) << 18 | (buffer[start++] & 63) << 12 | (buffer[start++] & 63) << 6 | buffer[start++] & 63) - 0x10000;\r\n            chunk[i++] = 0xD800 + (t >> 10);\r\n            chunk[i++] = 0xDC00 + (t & 1023);\r\n        } else\r\n            chunk[i++] = (t & 15) << 12 | (buffer[start++] & 63) << 6 | buffer[start++] & 63;\r\n        if (i > 8191) {\r\n            (parts || (parts = [])).push(String.fromCharCode.apply(String, chunk));\r\n            i = 0;\r\n        }\r\n    }\r\n    if (parts) {\r\n        if (i)\r\n            parts.push(String.fromCharCode.apply(String, chunk.slice(0, i)));\r\n        return parts.join(\"\");\r\n    }\r\n    return String.fromCharCode.apply(String, chunk.slice(0, i));\r\n};\r\n\r\n/**\r\n * Writes a string as UTF8 bytes.\r\n * @param {string} string Source string\r\n * @param {Uint8Array} buffer Destination buffer\r\n * @param {number} offset Destination offset\r\n * @returns {number} Bytes written\r\n */\r\nutf8.write = function utf8_write(string, buffer, offset) {\r\n    var start = offset,\r\n        c1, // character 1\r\n        c2; // character 2\r\n    for (var i = 0; i < string.length; ++i) {\r\n        c1 = string.charCodeAt(i);\r\n        if (c1 < 128) {\r\n            buffer[offset++] = c1;\r\n        } else if (c1 < 2048) {\r\n            buffer[offset++] = c1 >> 6       | 192;\r\n            buffer[offset++] = c1       & 63 | 128;\r\n        } else if ((c1 & 0xFC00) === 0xD800 && ((c2 = string.charCodeAt(i + 1)) & 0xFC00) === 0xDC00) {\r\n            c1 = 0x10000 + ((c1 & 0x03FF) << 10) + (c2 & 0x03FF);\r\n            ++i;\r\n            buffer[offset++] = c1 >> 18      | 240;\r\n            buffer[offset++] = c1 >> 12 & 63 | 128;\r\n            buffer[offset++] = c1 >> 6  & 63 | 128;\r\n            buffer[offset++] = c1       & 63 | 128;\r\n        } else {\r\n            buffer[offset++] = c1 >> 12      | 224;\r\n            buffer[offset++] = c1 >> 6  & 63 | 128;\r\n            buffer[offset++] = c1       & 63 | 128;\r\n        }\r\n    }\r\n    return offset - start;\r\n};\r\n", "\"use strict\";\nmodule.exports = common;\n\nvar commonRe = /\\/|\\./;\n\n/**\n * Provides common type definitions.\n * Can also be used to provide additional google types or your own custom types.\n * @param {string} name Short name as in `google/protobuf/[name].proto` or full file name\n * @param {Object.<string,*>} json JSON definition within `google.protobuf` if a short name, otherwise the file's root definition\n * @returns {undefined}\n * @property {INamespace} google/protobuf/any.proto Any\n * @property {INamespace} google/protobuf/duration.proto Duration\n * @property {INamespace} google/protobuf/empty.proto Empty\n * @property {INamespace} google/protobuf/field_mask.proto FieldMask\n * @property {INamespace} google/protobuf/struct.proto Struct, Value, NullValue and ListValue\n * @property {INamespace} google/protobuf/timestamp.proto Timestamp\n * @property {INamespace} google/protobuf/wrappers.proto Wrappers\n * @example\n * // manually provides descriptor.proto (assumes google/protobuf/ namespace and .proto extension)\n * protobuf.common(\"descriptor\", descriptorJson);\n *\n * // manually provides a custom definition (uses my.foo namespace)\n * protobuf.common(\"my/foo/bar.proto\", myFooBarJson);\n */\nfunction common(name, json) {\n    if (!commonRe.test(name)) {\n        name = \"google/protobuf/\" + name + \".proto\";\n        json = { nested: { google: { nested: { protobuf: { nested: json } } } } };\n    }\n    common[name] = json;\n}\n\n// Not provided because of limited use (feel free to discuss or to provide yourself):\n//\n// google/protobuf/descriptor.proto\n// google/protobuf/source_context.proto\n// google/protobuf/type.proto\n//\n// Stripped and pre-parsed versions of these non-bundled files are instead available as part of\n// the repository or package within the google/protobuf directory.\n\ncommon(\"any\", {\n\n    /**\n     * Properties of a google.protobuf.Any message.\n     * @interface IAny\n     * @type {Object}\n     * @property {string} [typeUrl]\n     * @property {Uint8Array} [bytes]\n     * @memberof common\n     */\n    Any: {\n        fields: {\n            type_url: {\n                type: \"string\",\n                id: 1\n            },\n            value: {\n                type: \"bytes\",\n                id: 2\n            }\n        }\n    }\n});\n\nvar timeType;\n\ncommon(\"duration\", {\n\n    /**\n     * Properties of a google.protobuf.Duration message.\n     * @interface IDuration\n     * @type {Object}\n     * @property {number|Long} [seconds]\n     * @property {number} [nanos]\n     * @memberof common\n     */\n    Duration: timeType = {\n        fields: {\n            seconds: {\n                type: \"int64\",\n                id: 1\n            },\n            nanos: {\n                type: \"int32\",\n                id: 2\n            }\n        }\n    }\n});\n\ncommon(\"timestamp\", {\n\n    /**\n     * Properties of a google.protobuf.Timestamp message.\n     * @interface ITimestamp\n     * @type {Object}\n     * @property {number|Long} [seconds]\n     * @property {number} [nanos]\n     * @memberof common\n     */\n    Timestamp: timeType\n});\n\ncommon(\"empty\", {\n\n    /**\n     * Properties of a google.protobuf.Empty message.\n     * @interface IEmpty\n     * @memberof common\n     */\n    Empty: {\n        fields: {}\n    }\n});\n\ncommon(\"struct\", {\n\n    /**\n     * Properties of a google.protobuf.Struct message.\n     * @interface IStruct\n     * @type {Object}\n     * @property {Object.<string,IValue>} [fields]\n     * @memberof common\n     */\n    Struct: {\n        fields: {\n            fields: {\n                keyType: \"string\",\n                type: \"Value\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.Value message.\n     * @interface IValue\n     * @type {Object}\n     * @property {string} [kind]\n     * @property {0} [nullValue]\n     * @property {number} [numberValue]\n     * @property {string} [stringValue]\n     * @property {boolean} [boolValue]\n     * @property {IStruct} [structValue]\n     * @property {IListValue} [listValue]\n     * @memberof common\n     */\n    Value: {\n        oneofs: {\n            kind: {\n                oneof: [\n                    \"nullValue\",\n                    \"numberValue\",\n                    \"stringValue\",\n                    \"boolValue\",\n                    \"structValue\",\n                    \"listValue\"\n                ]\n            }\n        },\n        fields: {\n            nullValue: {\n                type: \"NullValue\",\n                id: 1\n            },\n            numberValue: {\n                type: \"double\",\n                id: 2\n            },\n            stringValue: {\n                type: \"string\",\n                id: 3\n            },\n            boolValue: {\n                type: \"bool\",\n                id: 4\n            },\n            structValue: {\n                type: \"Struct\",\n                id: 5\n            },\n            listValue: {\n                type: \"ListValue\",\n                id: 6\n            }\n        }\n    },\n\n    NullValue: {\n        values: {\n            NULL_VALUE: 0\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.ListValue message.\n     * @interface IListValue\n     * @type {Object}\n     * @property {Array.<IValue>} [values]\n     * @memberof common\n     */\n    ListValue: {\n        fields: {\n            values: {\n                rule: \"repeated\",\n                type: \"Value\",\n                id: 1\n            }\n        }\n    }\n});\n\ncommon(\"wrappers\", {\n\n    /**\n     * Properties of a google.protobuf.DoubleValue message.\n     * @interface IDoubleValue\n     * @type {Object}\n     * @property {number} [value]\n     * @memberof common\n     */\n    DoubleValue: {\n        fields: {\n            value: {\n                type: \"double\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.FloatValue message.\n     * @interface IFloatValue\n     * @type {Object}\n     * @property {number} [value]\n     * @memberof common\n     */\n    FloatValue: {\n        fields: {\n            value: {\n                type: \"float\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.Int64Value message.\n     * @interface IInt64Value\n     * @type {Object}\n     * @property {number|Long} [value]\n     * @memberof common\n     */\n    Int64Value: {\n        fields: {\n            value: {\n                type: \"int64\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.UInt64Value message.\n     * @interface IUInt64Value\n     * @type {Object}\n     * @property {number|Long} [value]\n     * @memberof common\n     */\n    UInt64Value: {\n        fields: {\n            value: {\n                type: \"uint64\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.Int32Value message.\n     * @interface IInt32Value\n     * @type {Object}\n     * @property {number} [value]\n     * @memberof common\n     */\n    Int32Value: {\n        fields: {\n            value: {\n                type: \"int32\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.UInt32Value message.\n     * @interface IUInt32Value\n     * @type {Object}\n     * @property {number} [value]\n     * @memberof common\n     */\n    UInt32Value: {\n        fields: {\n            value: {\n                type: \"uint32\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.BoolValue message.\n     * @interface IBoolValue\n     * @type {Object}\n     * @property {boolean} [value]\n     * @memberof common\n     */\n    BoolValue: {\n        fields: {\n            value: {\n                type: \"bool\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.StringValue message.\n     * @interface IStringValue\n     * @type {Object}\n     * @property {string} [value]\n     * @memberof common\n     */\n    StringValue: {\n        fields: {\n            value: {\n                type: \"string\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.BytesValue message.\n     * @interface IBytesValue\n     * @type {Object}\n     * @property {Uint8Array} [value]\n     * @memberof common\n     */\n    BytesValue: {\n        fields: {\n            value: {\n                type: \"bytes\",\n                id: 1\n            }\n        }\n    }\n});\n\ncommon(\"field_mask\", {\n\n    /**\n     * Properties of a google.protobuf.FieldMask message.\n     * @interface IDoubleValue\n     * @type {Object}\n     * @property {number} [value]\n     * @memberof common\n     */\n    FieldMask: {\n        fields: {\n            paths: {\n                rule: \"repeated\",\n                type: \"string\",\n                id: 1\n            }\n        }\n    }\n});\n\n/**\n * Gets the root definition of the specified common proto file.\n *\n * Bundled definitions are:\n * - google/protobuf/any.proto\n * - google/protobuf/duration.proto\n * - google/protobuf/empty.proto\n * - google/protobuf/field_mask.proto\n * - google/protobuf/struct.proto\n * - google/protobuf/timestamp.proto\n * - google/protobuf/wrappers.proto\n *\n * @param {string} file Proto file name\n * @returns {INamespace|null} Root definition or `null` if not defined\n */\ncommon.get = function get(file) {\n    return common[file] || null;\n};\n", "\"use strict\";\n/**\n * Runtime message from/to plain object converters.\n * @namespace\n */\nvar converter = exports;\n\nvar Enum = require(15),\n    util = require(37);\n\n/**\n * Generates a partial value fromObject conveter.\n * @param {Codegen} gen Codegen instance\n * @param {Field} field Reflected field\n * @param {number} fieldIndex Field index\n * @param {string} prop Property reference\n * @returns {Codegen} Codegen instance\n * @ignore\n */\nfunction genValuePartial_fromObject(gen, field, fieldIndex, prop) {\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\n    if (field.resolvedType) {\n        if (field.resolvedType instanceof Enum) { gen\n            (\"switch(d%s){\", prop);\n            for (var values = field.resolvedType.values, keys = Object.keys(values), i = 0; i < keys.length; ++i) {\n                // enum unknown values passthrough\n                if (values[keys[i]] === field.typeDefault) { gen\n                    (\"default:\")\n                        (\"if(typeof(d%s)===\\\"number\\\"){m%s=d%s;break}\", prop, prop, prop);\n                    if (!field.repeated) gen // fallback to default value only for\n                                             // arrays, to avoid leaving holes.\n                        (\"break\");           // for non-repeated fields, just ignore\n                }\n                gen\n                (\"case%j:\", keys[i])\n                (\"case %i:\", values[keys[i]])\n                    (\"m%s=%j\", prop, values[keys[i]])\n                    (\"break\");\n            } gen\n            (\"}\");\n        } else gen\n            (\"if(typeof d%s!==\\\"object\\\")\", prop)\n                (\"throw TypeError(%j)\", field.fullName + \": object expected\")\n            (\"m%s=types[%i].fromObject(d%s)\", prop, fieldIndex, prop);\n    } else {\n        var isUnsigned = false;\n        switch (field.type) {\n            case \"double\":\n            case \"float\": gen\n                (\"m%s=Number(d%s)\", prop, prop); // also catches \"NaN\", \"Infinity\"\n                break;\n            case \"uint32\":\n            case \"fixed32\": gen\n                (\"m%s=d%s>>>0\", prop, prop);\n                break;\n            case \"int32\":\n            case \"sint32\":\n            case \"sfixed32\": gen\n                (\"m%s=d%s|0\", prop, prop);\n                break;\n            case \"uint64\":\n                isUnsigned = true;\n                // eslint-disable-line no-fallthrough\n            case \"int64\":\n            case \"sint64\":\n            case \"fixed64\":\n            case \"sfixed64\": gen\n                (\"if(util.Long)\")\n                    (\"(m%s=util.Long.fromValue(d%s)).unsigned=%j\", prop, prop, isUnsigned)\n                (\"else if(typeof d%s===\\\"string\\\")\", prop)\n                    (\"m%s=parseInt(d%s,10)\", prop, prop)\n                (\"else if(typeof d%s===\\\"number\\\")\", prop)\n                    (\"m%s=d%s\", prop, prop)\n                (\"else if(typeof d%s===\\\"object\\\")\", prop)\n                    (\"m%s=new util.LongBits(d%s.low>>>0,d%s.high>>>0).toNumber(%s)\", prop, prop, prop, isUnsigned ? \"true\" : \"\");\n                break;\n            case \"bytes\": gen\n                (\"if(typeof d%s===\\\"string\\\")\", prop)\n                    (\"util.base64.decode(d%s,m%s=util.newBuffer(util.base64.length(d%s)),0)\", prop, prop, prop)\n                (\"else if(d%s.length >= 0)\", prop)\n                    (\"m%s=d%s\", prop, prop);\n                break;\n            case \"string\": gen\n                (\"m%s=String(d%s)\", prop, prop);\n                break;\n            case \"bool\": gen\n                (\"m%s=Boolean(d%s)\", prop, prop);\n                break;\n            /* default: gen\n                (\"m%s=d%s\", prop, prop);\n                break; */\n        }\n    }\n    return gen;\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\n}\n\n/**\n * Generates a plain object to runtime message converter specific to the specified message type.\n * @param {Type} mtype Message type\n * @returns {Codegen} Codegen instance\n */\nconverter.fromObject = function fromObject(mtype) {\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\n    var fields = mtype.fieldsArray;\n    var gen = util.codegen([\"d\"], mtype.name + \"$fromObject\")\n    (\"if(d instanceof this.ctor)\")\n        (\"return d\");\n    if (!fields.length) return gen\n    (\"return new this.ctor\");\n    gen\n    (\"var m=new this.ctor\");\n    for (var i = 0; i < fields.length; ++i) {\n        var field  = fields[i].resolve(),\n            prop   = util.safeProp(field.name);\n\n        // Map fields\n        if (field.map) { gen\n    (\"if(d%s){\", prop)\n        (\"if(typeof d%s!==\\\"object\\\")\", prop)\n            (\"throw TypeError(%j)\", field.fullName + \": object expected\")\n        (\"m%s={}\", prop)\n        (\"for(var ks=Object.keys(d%s),i=0;i<ks.length;++i){\", prop);\n            genValuePartial_fromObject(gen, field, /* not sorted */ i, prop + \"[ks[i]]\")\n        (\"}\")\n    (\"}\");\n\n        // Repeated fields\n        } else if (field.repeated) { gen\n    (\"if(d%s){\", prop)\n        (\"if(!Array.isArray(d%s))\", prop)\n            (\"throw TypeError(%j)\", field.fullName + \": array expected\")\n        (\"m%s=[]\", prop)\n        (\"for(var i=0;i<d%s.length;++i){\", prop);\n            genValuePartial_fromObject(gen, field, /* not sorted */ i, prop + \"[i]\")\n        (\"}\")\n    (\"}\");\n\n        // Non-repeated fields\n        } else {\n            if (!(field.resolvedType instanceof Enum)) gen // no need to test for null/undefined if an enum (uses switch)\n    (\"if(d%s!=null){\", prop); // !== undefined && !== null\n        genValuePartial_fromObject(gen, field, /* not sorted */ i, prop);\n            if (!(field.resolvedType instanceof Enum)) gen\n    (\"}\");\n        }\n    } return gen\n    (\"return m\");\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\n};\n\n/**\n * Generates a partial value toObject converter.\n * @param {Codegen} gen Codegen instance\n * @param {Field} field Reflected field\n * @param {number} fieldIndex Field index\n * @param {string} prop Property reference\n * @returns {Codegen} Codegen instance\n * @ignore\n */\nfunction genValuePartial_toObject(gen, field, fieldIndex, prop) {\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\n    if (field.resolvedType) {\n        if (field.resolvedType instanceof Enum) gen\n            (\"d%s=o.enums===String?(types[%i].values[m%s]===undefined?m%s:types[%i].values[m%s]):m%s\", prop, fieldIndex, prop, prop, fieldIndex, prop, prop);\n        else gen\n            (\"d%s=types[%i].toObject(m%s,o)\", prop, fieldIndex, prop);\n    } else {\n        var isUnsigned = false;\n        switch (field.type) {\n            case \"double\":\n            case \"float\": gen\n            (\"d%s=o.json&&!isFinite(m%s)?String(m%s):m%s\", prop, prop, prop, prop);\n                break;\n            case \"uint64\":\n                isUnsigned = true;\n                // eslint-disable-line no-fallthrough\n            case \"int64\":\n            case \"sint64\":\n            case \"fixed64\":\n            case \"sfixed64\": gen\n            (\"if(typeof m%s===\\\"number\\\")\", prop)\n                (\"d%s=o.longs===String?String(m%s):m%s\", prop, prop, prop)\n            (\"else\") // Long-like\n                (\"d%s=o.longs===String?util.Long.prototype.toString.call(m%s):o.longs===Number?new util.LongBits(m%s.low>>>0,m%s.high>>>0).toNumber(%s):m%s\", prop, prop, prop, prop, isUnsigned ? \"true\": \"\", prop);\n                break;\n            case \"bytes\": gen\n            (\"d%s=o.bytes===String?util.base64.encode(m%s,0,m%s.length):o.bytes===Array?Array.prototype.slice.call(m%s):m%s\", prop, prop, prop, prop, prop);\n                break;\n            default: gen\n            (\"d%s=m%s\", prop, prop);\n                break;\n        }\n    }\n    return gen;\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\n}\n\n/**\n * Generates a runtime message to plain object converter specific to the specified message type.\n * @param {Type} mtype Message type\n * @returns {Codegen} Codegen instance\n */\nconverter.toObject = function toObject(mtype) {\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\n    var fields = mtype.fieldsArray.slice().sort(util.compareFieldsById);\n    if (!fields.length)\n        return util.codegen()(\"return {}\");\n    var gen = util.codegen([\"m\", \"o\"], mtype.name + \"$toObject\")\n    (\"if(!o)\")\n        (\"o={}\")\n    (\"var d={}\");\n\n    var repeatedFields = [],\n        mapFields = [],\n        normalFields = [],\n        i = 0;\n    for (; i < fields.length; ++i)\n        if (!fields[i].partOf)\n            ( fields[i].resolve().repeated ? repeatedFields\n            : fields[i].map ? mapFields\n            : normalFields).push(fields[i]);\n\n    if (repeatedFields.length) { gen\n    (\"if(o.arrays||o.defaults){\");\n        for (i = 0; i < repeatedFields.length; ++i) gen\n        (\"d%s=[]\", util.safeProp(repeatedFields[i].name));\n        gen\n    (\"}\");\n    }\n\n    if (mapFields.length) { gen\n    (\"if(o.objects||o.defaults){\");\n        for (i = 0; i < mapFields.length; ++i) gen\n        (\"d%s={}\", util.safeProp(mapFields[i].name));\n        gen\n    (\"}\");\n    }\n\n    if (normalFields.length) { gen\n    (\"if(o.defaults){\");\n        for (i = 0; i < normalFields.length; ++i) {\n            var field = normalFields[i],\n                prop  = util.safeProp(field.name);\n            if (field.resolvedType instanceof Enum) gen\n        (\"d%s=o.enums===String?%j:%j\", prop, field.resolvedType.valuesById[field.typeDefault], field.typeDefault);\n            else if (field.long) gen\n        (\"if(util.Long){\")\n            (\"var n=new util.Long(%i,%i,%j)\", field.typeDefault.low, field.typeDefault.high, field.typeDefault.unsigned)\n            (\"d%s=o.longs===String?n.toString():o.longs===Number?n.toNumber():n\", prop)\n        (\"}else\")\n            (\"d%s=o.longs===String?%j:%i\", prop, field.typeDefault.toString(), field.typeDefault.toNumber());\n            else if (field.bytes) {\n                var arrayDefault = \"[\" + Array.prototype.slice.call(field.typeDefault).join(\",\") + \"]\";\n                gen\n        (\"if(o.bytes===String)d%s=%j\", prop, String.fromCharCode.apply(String, field.typeDefault))\n        (\"else{\")\n            (\"d%s=%s\", prop, arrayDefault)\n            (\"if(o.bytes!==Array)d%s=util.newBuffer(d%s)\", prop, prop)\n        (\"}\");\n            } else gen\n        (\"d%s=%j\", prop, field.typeDefault); // also messages (=null)\n        } gen\n    (\"}\");\n    }\n    var hasKs2 = false;\n    for (i = 0; i < fields.length; ++i) {\n        var field = fields[i],\n            index = mtype._fieldsArray.indexOf(field),\n            prop  = util.safeProp(field.name);\n        if (field.map) {\n            if (!hasKs2) { hasKs2 = true; gen\n    (\"var ks2\");\n            } gen\n    (\"if(m%s&&(ks2=Object.keys(m%s)).length){\", prop, prop)\n        (\"d%s={}\", prop)\n        (\"for(var j=0;j<ks2.length;++j){\");\n            genValuePartial_toObject(gen, field, /* sorted */ index, prop + \"[ks2[j]]\")\n        (\"}\");\n        } else if (field.repeated) { gen\n    (\"if(m%s&&m%s.length){\", prop, prop)\n        (\"d%s=[]\", prop)\n        (\"for(var j=0;j<m%s.length;++j){\", prop);\n            genValuePartial_toObject(gen, field, /* sorted */ index, prop + \"[j]\")\n        (\"}\");\n        } else { gen\n    (\"if(m%s!=null&&m.hasOwnProperty(%j)){\", prop, field.name); // !== undefined && !== null\n        genValuePartial_toObject(gen, field, /* sorted */ index, prop);\n        if (field.partOf) gen\n        (\"if(o.oneofs)\")\n            (\"d%s=%j\", util.safeProp(field.partOf.name), field.name);\n        }\n        gen\n    (\"}\");\n    }\n    return gen\n    (\"return d\");\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\n};\n", "\"use strict\";\nmodule.exports = decoder;\n\nvar Enum    = require(15),\n    types   = require(36),\n    util    = require(37);\n\nfunction missing(field) {\n    return \"missing required '\" + field.name + \"'\";\n}\n\n/**\n * Generates a decoder specific to the specified message type.\n * @param {Type} mtype Message type\n * @returns {Codegen} Codegen instance\n */\nfunction decoder(mtype) {\n    /* eslint-disable no-unexpected-multiline */\n    var gen = util.codegen([\"r\", \"l\"], mtype.name + \"$decode\")\n    (\"if(!(r instanceof Reader))\")\n        (\"r=Reader.create(r)\")\n    (\"var c=l===undefined?r.len:r.pos+l,m=new this.ctor\" + (mtype.fieldsArray.filter(function(field) { return field.map; }).length ? \",k,value\" : \"\"))\n    (\"while(r.pos<c){\")\n        (\"var t=r.uint32()\");\n    if (mtype.group) gen\n        (\"if((t&7)===4)\")\n            (\"break\");\n    gen\n        (\"switch(t>>>3){\");\n\n    var i = 0;\n    for (; i < /* initializes */ mtype.fieldsArray.length; ++i) {\n        var field = mtype._fieldsArray[i].resolve(),\n            type  = field.resolvedType instanceof Enum ? \"int32\" : field.type,\n            ref   = \"m\" + util.safeProp(field.name); gen\n            (\"case %i: {\", field.id);\n\n        // Map fields\n        if (field.map) { gen\n                (\"if(%s===util.emptyObject)\", ref)\n                    (\"%s={}\", ref)\n                (\"var c2 = r.uint32()+r.pos\");\n\n            if (types.defaults[field.keyType] !== undefined) gen\n                (\"k=%j\", types.defaults[field.keyType]);\n            else gen\n                (\"k=null\");\n\n            if (types.defaults[type] !== undefined) gen\n                (\"value=%j\", types.defaults[type]);\n            else gen\n                (\"value=null\");\n\n            gen\n                (\"while(r.pos<c2){\")\n                    (\"var tag2=r.uint32()\")\n                    (\"switch(tag2>>>3){\")\n                        (\"case 1: k=r.%s(); break\", field.keyType)\n                        (\"case 2:\");\n\n            if (types.basic[type] === undefined) gen\n                            (\"value=types[%i].decode(r,r.uint32())\", i); // can't be groups\n            else gen\n                            (\"value=r.%s()\", type);\n\n            gen\n                            (\"break\")\n                        (\"default:\")\n                            (\"r.skipType(tag2&7)\")\n                            (\"break\")\n                    (\"}\")\n                (\"}\");\n\n            if (types.long[field.keyType] !== undefined) gen\n                (\"%s[typeof k===\\\"object\\\"?util.longToHash(k):k]=value\", ref);\n            else gen\n                (\"%s[k]=value\", ref);\n\n        // Repeated fields\n        } else if (field.repeated) { gen\n\n                (\"if(!(%s&&%s.length))\", ref, ref)\n                    (\"%s=[]\", ref);\n\n            // Packable (always check for forward and backward compatiblity)\n            if (types.packed[type] !== undefined) gen\n                (\"if((t&7)===2){\")\n                    (\"var c2=r.uint32()+r.pos\")\n                    (\"while(r.pos<c2)\")\n                        (\"%s.push(r.%s())\", ref, type)\n                (\"}else\");\n\n            // Non-packed\n            if (types.basic[type] === undefined) gen(field.resolvedType.group\n                    ? \"%s.push(types[%i].decode(r))\"\n                    : \"%s.push(types[%i].decode(r,r.uint32()))\", ref, i);\n            else gen\n                    (\"%s.push(r.%s())\", ref, type);\n\n        // Non-repeated\n        } else if (types.basic[type] === undefined) gen(field.resolvedType.group\n                ? \"%s=types[%i].decode(r)\"\n                : \"%s=types[%i].decode(r,r.uint32())\", ref, i);\n        else gen\n                (\"%s=r.%s()\", ref, type);\n        gen\n                (\"break\")\n            (\"}\");\n        // Unknown fields\n    } gen\n            (\"default:\")\n                (\"r.skipType(t&7)\")\n                (\"break\")\n\n        (\"}\")\n    (\"}\");\n\n    // Field presence\n    for (i = 0; i < mtype._fieldsArray.length; ++i) {\n        var rfield = mtype._fieldsArray[i];\n        if (rfield.required) gen\n    (\"if(!m.hasOwnProperty(%j))\", rfield.name)\n        (\"throw util.ProtocolError(%j,{instance:m})\", missing(rfield));\n    }\n\n    return gen\n    (\"return m\");\n    /* eslint-enable no-unexpected-multiline */\n}\n", "\"use strict\";\nmodule.exports = encoder;\n\nvar Enum     = require(15),\n    types    = require(36),\n    util     = require(37);\n\n/**\n * Generates a partial message type encoder.\n * @param {Codegen} gen Codegen instance\n * @param {Field} field Reflected field\n * @param {number} fieldIndex Field index\n * @param {string} ref Variable reference\n * @returns {Codegen} Codegen instance\n * @ignore\n */\nfunction genTypePartial(gen, field, fieldIndex, ref) {\n    return field.resolvedType.group\n        ? gen(\"types[%i].encode(%s,w.uint32(%i)).uint32(%i)\", fieldIndex, ref, (field.id << 3 | 3) >>> 0, (field.id << 3 | 4) >>> 0)\n        : gen(\"types[%i].encode(%s,w.uint32(%i).fork()).ldelim()\", fieldIndex, ref, (field.id << 3 | 2) >>> 0);\n}\n\n/**\n * Generates an encoder specific to the specified message type.\n * @param {Type} mtype Message type\n * @returns {Codegen} Codegen instance\n */\nfunction encoder(mtype) {\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\n    var gen = util.codegen([\"m\", \"w\"], mtype.name + \"$encode\")\n    (\"if(!w)\")\n        (\"w=Writer.create()\");\n\n    var i, ref;\n\n    // \"when a message is serialized its known fields should be written sequentially by field number\"\n    var fields = /* initializes */ mtype.fieldsArray.slice().sort(util.compareFieldsById);\n\n    for (var i = 0; i < fields.length; ++i) {\n        var field    = fields[i].resolve(),\n            index    = mtype._fieldsArray.indexOf(field),\n            type     = field.resolvedType instanceof Enum ? \"int32\" : field.type,\n            wireType = types.basic[type];\n            ref      = \"m\" + util.safeProp(field.name);\n\n        // Map fields\n        if (field.map) {\n            gen\n    (\"if(%s!=null&&Object.hasOwnProperty.call(m,%j)){\", ref, field.name) // !== undefined && !== null\n        (\"for(var ks=Object.keys(%s),i=0;i<ks.length;++i){\", ref)\n            (\"w.uint32(%i).fork().uint32(%i).%s(ks[i])\", (field.id << 3 | 2) >>> 0, 8 | types.mapKey[field.keyType], field.keyType);\n            if (wireType === undefined) gen\n            (\"types[%i].encode(%s[ks[i]],w.uint32(18).fork()).ldelim().ldelim()\", index, ref); // can't be groups\n            else gen\n            (\".uint32(%i).%s(%s[ks[i]]).ldelim()\", 16 | wireType, type, ref);\n            gen\n        (\"}\")\n    (\"}\");\n\n            // Repeated fields\n        } else if (field.repeated) { gen\n    (\"if(%s!=null&&%s.length){\", ref, ref); // !== undefined && !== null\n\n            // Packed repeated\n            if (field.packed && types.packed[type] !== undefined) { gen\n\n        (\"w.uint32(%i).fork()\", (field.id << 3 | 2) >>> 0)\n        (\"for(var i=0;i<%s.length;++i)\", ref)\n            (\"w.%s(%s[i])\", type, ref)\n        (\"w.ldelim()\");\n\n            // Non-packed\n            } else { gen\n\n        (\"for(var i=0;i<%s.length;++i)\", ref);\n                if (wireType === undefined)\n            genTypePartial(gen, field, index, ref + \"[i]\");\n                else gen\n            (\"w.uint32(%i).%s(%s[i])\", (field.id << 3 | wireType) >>> 0, type, ref);\n\n            } gen\n    (\"}\");\n\n        // Non-repeated\n        } else {\n            if (field.optional) gen\n    (\"if(%s!=null&&Object.hasOwnProperty.call(m,%j))\", ref, field.name); // !== undefined && !== null\n\n            if (wireType === undefined)\n        genTypePartial(gen, field, index, ref);\n            else gen\n        (\"w.uint32(%i).%s(%s)\", (field.id << 3 | wireType) >>> 0, type, ref);\n\n        }\n    }\n\n    return gen\n    (\"return w\");\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\n}\n", "\"use strict\";\nmodule.exports = Enum;\n\n// extends ReflectionObject\nvar ReflectionObject = require(24);\n((Enum.prototype = Object.create(ReflectionObject.prototype)).constructor = Enum).className = \"Enum\";\n\nvar Namespace = require(23),\n    util = require(37);\n\n/**\n * Constructs a new enum instance.\n * @classdesc Reflected enum.\n * @extends ReflectionObject\n * @constructor\n * @param {string} name Unique name within its namespace\n * @param {Object.<string,number>} [values] Enum values as an object, by name\n * @param {Object.<string,*>} [options] Declared options\n * @param {string} [comment] The comment for this enum\n * @param {Object.<string,string>} [comments] The value comments for this enum\n * @param {Object.<string,Object<string,*>>|undefined} [valuesOptions] The value options for this enum\n */\nfunction Enum(name, values, options, comment, comments, valuesOptions) {\n    ReflectionObject.call(this, name, options);\n\n    if (values && typeof values !== \"object\")\n        throw TypeError(\"values must be an object\");\n\n    /**\n     * Enum values by id.\n     * @type {Object.<number,string>}\n     */\n    this.valuesById = {};\n\n    /**\n     * Enum values by name.\n     * @type {Object.<string,number>}\n     */\n    this.values = Object.create(this.valuesById); // toJSON, marker\n\n    /**\n     * Enum comment text.\n     * @type {string|null}\n     */\n    this.comment = comment;\n\n    /**\n     * Value comment texts, if any.\n     * @type {Object.<string,string>}\n     */\n    this.comments = comments || {};\n\n    /**\n     * Values options, if any\n     * @type {Object<string, Object<string, *>>|undefined}\n     */\n    this.valuesOptions = valuesOptions;\n\n    /**\n     * Reserved ranges, if any.\n     * @type {Array.<number[]|string>}\n     */\n    this.reserved = undefined; // toJSON\n\n    // Note that values inherit valuesById on their prototype which makes them a TypeScript-\n    // compatible enum. This is used by pbts to write actual enum definitions that work for\n    // static and reflection code alike instead of emitting generic object definitions.\n\n    if (values)\n        for (var keys = Object.keys(values), i = 0; i < keys.length; ++i)\n            if (typeof values[keys[i]] === \"number\") // use forward entries only\n                this.valuesById[ this.values[keys[i]] = values[keys[i]] ] = keys[i];\n}\n\n/**\n * Enum descriptor.\n * @interface IEnum\n * @property {Object.<string,number>} values Enum values\n * @property {Object.<string,*>} [options] Enum options\n */\n\n/**\n * Constructs an enum from an enum descriptor.\n * @param {string} name Enum name\n * @param {IEnum} json Enum descriptor\n * @returns {Enum} Created enum\n * @throws {TypeError} If arguments are invalid\n */\nEnum.fromJSON = function fromJSON(name, json) {\n    var enm = new Enum(name, json.values, json.options, json.comment, json.comments);\n    enm.reserved = json.reserved;\n    return enm;\n};\n\n/**\n * Converts this enum to an enum descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IEnum} Enum descriptor\n */\nEnum.prototype.toJSON = function toJSON(toJSONOptions) {\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"options\"       , this.options,\n        \"valuesOptions\" , this.valuesOptions,\n        \"values\"        , this.values,\n        \"reserved\"      , this.reserved && this.reserved.length ? this.reserved : undefined,\n        \"comment\"       , keepComments ? this.comment : undefined,\n        \"comments\"      , keepComments ? this.comments : undefined\n    ]);\n};\n\n/**\n * Adds a value to this enum.\n * @param {string} name Value name\n * @param {number} id Value id\n * @param {string} [comment] Comment, if any\n * @param {Object.<string, *>|undefined} [options] Options, if any\n * @returns {Enum} `this`\n * @throws {TypeError} If arguments are invalid\n * @throws {Error} If there is already a value with this name or id\n */\nEnum.prototype.add = function add(name, id, comment, options) {\n    // utilized by the parser but not by .fromJSON\n\n    if (!util.isString(name))\n        throw TypeError(\"name must be a string\");\n\n    if (!util.isInteger(id))\n        throw TypeError(\"id must be an integer\");\n\n    if (this.values[name] !== undefined)\n        throw Error(\"duplicate name '\" + name + \"' in \" + this);\n\n    if (this.isReservedId(id))\n        throw Error(\"id \" + id + \" is reserved in \" + this);\n\n    if (this.isReservedName(name))\n        throw Error(\"name '\" + name + \"' is reserved in \" + this);\n\n    if (this.valuesById[id] !== undefined) {\n        if (!(this.options && this.options.allow_alias))\n            throw Error(\"duplicate id \" + id + \" in \" + this);\n        this.values[name] = id;\n    } else\n        this.valuesById[this.values[name] = id] = name;\n\n    if (options) {\n        if (this.valuesOptions === undefined)\n            this.valuesOptions = {};\n        this.valuesOptions[name] = options || null;\n    }\n\n    this.comments[name] = comment || null;\n    return this;\n};\n\n/**\n * Removes a value from this enum\n * @param {string} name Value name\n * @returns {Enum} `this`\n * @throws {TypeError} If arguments are invalid\n * @throws {Error} If `name` is not a name of this enum\n */\nEnum.prototype.remove = function remove(name) {\n\n    if (!util.isString(name))\n        throw TypeError(\"name must be a string\");\n\n    var val = this.values[name];\n    if (val == null)\n        throw Error(\"name '\" + name + \"' does not exist in \" + this);\n\n    delete this.valuesById[val];\n    delete this.values[name];\n    delete this.comments[name];\n    if (this.valuesOptions)\n        delete this.valuesOptions[name];\n\n    return this;\n};\n\n/**\n * Tests if the specified id is reserved.\n * @param {number} id Id to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nEnum.prototype.isReservedId = function isReservedId(id) {\n    return Namespace.isReservedId(this.reserved, id);\n};\n\n/**\n * Tests if the specified name is reserved.\n * @param {string} name Name to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nEnum.prototype.isReservedName = function isReservedName(name) {\n    return Namespace.isReservedName(this.reserved, name);\n};\n", "\"use strict\";\nmodule.exports = Field;\n\n// extends ReflectionObject\nvar ReflectionObject = require(24);\n((Field.prototype = Object.create(ReflectionObject.prototype)).constructor = Field).className = \"Field\";\n\nvar Enum  = require(15),\n    types = require(36),\n    util  = require(37);\n\nvar Type; // cyclic\n\nvar ruleRe = /^required|optional|repeated$/;\n\n/**\n * Constructs a new message field instance. Note that {@link MapField|map fields} have their own class.\n * @name Field\n * @classdesc Reflected message field.\n * @extends FieldBase\n * @constructor\n * @param {string} name Unique name within its namespace\n * @param {number} id Unique id within its namespace\n * @param {string} type Value type\n * @param {string|Object.<string,*>} [rule=\"optional\"] Field rule\n * @param {string|Object.<string,*>} [extend] Extended type if different from parent\n * @param {Object.<string,*>} [options] Declared options\n */\n\n/**\n * Constructs a field from a field descriptor.\n * @param {string} name Field name\n * @param {IField} json Field descriptor\n * @returns {Field} Created field\n * @throws {TypeError} If arguments are invalid\n */\nField.fromJSON = function fromJSON(name, json) {\n    return new Field(name, json.id, json.type, json.rule, json.extend, json.options, json.comment);\n};\n\n/**\n * Not an actual constructor. Use {@link Field} instead.\n * @classdesc Base class of all reflected message fields. This is not an actual class but here for the sake of having consistent type definitions.\n * @exports FieldBase\n * @extends ReflectionObject\n * @constructor\n * @param {string} name Unique name within its namespace\n * @param {number} id Unique id within its namespace\n * @param {string} type Value type\n * @param {string|Object.<string,*>} [rule=\"optional\"] Field rule\n * @param {string|Object.<string,*>} [extend] Extended type if different from parent\n * @param {Object.<string,*>} [options] Declared options\n * @param {string} [comment] Comment associated with this field\n */\nfunction Field(name, id, type, rule, extend, options, comment) {\n\n    if (util.isObject(rule)) {\n        comment = extend;\n        options = rule;\n        rule = extend = undefined;\n    } else if (util.isObject(extend)) {\n        comment = options;\n        options = extend;\n        extend = undefined;\n    }\n\n    ReflectionObject.call(this, name, options);\n\n    if (!util.isInteger(id) || id < 0)\n        throw TypeError(\"id must be a non-negative integer\");\n\n    if (!util.isString(type))\n        throw TypeError(\"type must be a string\");\n\n    if (rule !== undefined && !ruleRe.test(rule = rule.toString().toLowerCase()))\n        throw TypeError(\"rule must be a string rule\");\n\n    if (extend !== undefined && !util.isString(extend))\n        throw TypeError(\"extend must be a string\");\n\n    /**\n     * Field rule, if any.\n     * @type {string|undefined}\n     */\n    if (rule === \"proto3_optional\") {\n        rule = \"optional\";\n    }\n    this.rule = rule && rule !== \"optional\" ? rule : undefined; // toJSON\n\n    /**\n     * Field type.\n     * @type {string}\n     */\n    this.type = type; // toJSON\n\n    /**\n     * Unique field id.\n     * @type {number}\n     */\n    this.id = id; // toJSON, marker\n\n    /**\n     * Extended type if different from parent.\n     * @type {string|undefined}\n     */\n    this.extend = extend || undefined; // toJSON\n\n    /**\n     * Whether this field is required.\n     * @type {boolean}\n     */\n    this.required = rule === \"required\";\n\n    /**\n     * Whether this field is optional.\n     * @type {boolean}\n     */\n    this.optional = !this.required;\n\n    /**\n     * Whether this field is repeated.\n     * @type {boolean}\n     */\n    this.repeated = rule === \"repeated\";\n\n    /**\n     * Whether this field is a map or not.\n     * @type {boolean}\n     */\n    this.map = false;\n\n    /**\n     * Message this field belongs to.\n     * @type {Type|null}\n     */\n    this.message = null;\n\n    /**\n     * OneOf this field belongs to, if any,\n     * @type {OneOf|null}\n     */\n    this.partOf = null;\n\n    /**\n     * The field type's default value.\n     * @type {*}\n     */\n    this.typeDefault = null;\n\n    /**\n     * The field's default value on prototypes.\n     * @type {*}\n     */\n    this.defaultValue = null;\n\n    /**\n     * Whether this field's value should be treated as a long.\n     * @type {boolean}\n     */\n    this.long = util.Long ? types.long[type] !== undefined : /* istanbul ignore next */ false;\n\n    /**\n     * Whether this field's value is a buffer.\n     * @type {boolean}\n     */\n    this.bytes = type === \"bytes\";\n\n    /**\n     * Resolved type if not a basic type.\n     * @type {Type|Enum|null}\n     */\n    this.resolvedType = null;\n\n    /**\n     * Sister-field within the extended type if a declaring extension field.\n     * @type {Field|null}\n     */\n    this.extensionField = null;\n\n    /**\n     * Sister-field within the declaring namespace if an extended field.\n     * @type {Field|null}\n     */\n    this.declaringField = null;\n\n    /**\n     * Internally remembers whether this field is packed.\n     * @type {boolean|null}\n     * @private\n     */\n    this._packed = null;\n\n    /**\n     * Comment for this field.\n     * @type {string|null}\n     */\n    this.comment = comment;\n}\n\n/**\n * Determines whether this field is packed. Only relevant when repeated and working with proto2.\n * @name Field#packed\n * @type {boolean}\n * @readonly\n */\nObject.defineProperty(Field.prototype, \"packed\", {\n    get: function() {\n        // defaults to packed=true if not explicity set to false\n        if (this._packed === null)\n            this._packed = this.getOption(\"packed\") !== false;\n        return this._packed;\n    }\n});\n\n/**\n * @override\n */\nField.prototype.setOption = function setOption(name, value, ifNotSet) {\n    if (name === \"packed\") // clear cached before setting\n        this._packed = null;\n    return ReflectionObject.prototype.setOption.call(this, name, value, ifNotSet);\n};\n\n/**\n * Field descriptor.\n * @interface IField\n * @property {string} [rule=\"optional\"] Field rule\n * @property {string} type Field type\n * @property {number} id Field id\n * @property {Object.<string,*>} [options] Field options\n */\n\n/**\n * Extension field descriptor.\n * @interface IExtensionField\n * @extends IField\n * @property {string} extend Extended type\n */\n\n/**\n * Converts this field to a field descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IField} Field descriptor\n */\nField.prototype.toJSON = function toJSON(toJSONOptions) {\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"rule\"    , this.rule !== \"optional\" && this.rule || undefined,\n        \"type\"    , this.type,\n        \"id\"      , this.id,\n        \"extend\"  , this.extend,\n        \"options\" , this.options,\n        \"comment\" , keepComments ? this.comment : undefined\n    ]);\n};\n\n/**\n * Resolves this field's type references.\n * @returns {Field} `this`\n * @throws {Error} If any reference cannot be resolved\n */\nField.prototype.resolve = function resolve() {\n\n    if (this.resolved)\n        return this;\n\n    if ((this.typeDefault = types.defaults[this.type]) === undefined) { // if not a basic type, resolve it\n        this.resolvedType = (this.declaringField ? this.declaringField.parent : this.parent).lookupTypeOrEnum(this.type);\n        if (this.resolvedType instanceof Type)\n            this.typeDefault = null;\n        else // instanceof Enum\n            this.typeDefault = this.resolvedType.values[Object.keys(this.resolvedType.values)[0]]; // first defined\n    } else if (this.options && this.options.proto3_optional) {\n        // proto3 scalar value marked optional; should default to null\n        this.typeDefault = null;\n    }\n\n    // use explicitly set default value if present\n    if (this.options && this.options[\"default\"] != null) {\n        this.typeDefault = this.options[\"default\"];\n        if (this.resolvedType instanceof Enum && typeof this.typeDefault === \"string\")\n            this.typeDefault = this.resolvedType.values[this.typeDefault];\n    }\n\n    // remove unnecessary options\n    if (this.options) {\n        if (this.options.packed === true || this.options.packed !== undefined && this.resolvedType && !(this.resolvedType instanceof Enum))\n            delete this.options.packed;\n        if (!Object.keys(this.options).length)\n            this.options = undefined;\n    }\n\n    // convert to internal data type if necesssary\n    if (this.long) {\n        this.typeDefault = util.Long.fromNumber(this.typeDefault, this.type.charAt(0) === \"u\");\n\n        /* istanbul ignore else */\n        if (Object.freeze)\n            Object.freeze(this.typeDefault); // long instances are meant to be immutable anyway (i.e. use small int cache that even requires it)\n\n    } else if (this.bytes && typeof this.typeDefault === \"string\") {\n        var buf;\n        if (util.base64.test(this.typeDefault))\n            util.base64.decode(this.typeDefault, buf = util.newBuffer(util.base64.length(this.typeDefault)), 0);\n        else\n            util.utf8.write(this.typeDefault, buf = util.newBuffer(util.utf8.length(this.typeDefault)), 0);\n        this.typeDefault = buf;\n    }\n\n    // take special care of maps and repeated fields\n    if (this.map)\n        this.defaultValue = util.emptyObject;\n    else if (this.repeated)\n        this.defaultValue = util.emptyArray;\n    else\n        this.defaultValue = this.typeDefault;\n\n    // ensure proper value on prototype\n    if (this.parent instanceof Type)\n        this.parent.ctor.prototype[this.name] = this.defaultValue;\n\n    return ReflectionObject.prototype.resolve.call(this);\n};\n\n/**\n * Decorator function as returned by {@link Field.d} and {@link MapField.d} (TypeScript).\n * @typedef FieldDecorator\n * @type {function}\n * @param {Object} prototype Target prototype\n * @param {string} fieldName Field name\n * @returns {undefined}\n */\n\n/**\n * Field decorator (TypeScript).\n * @name Field.d\n * @function\n * @param {number} fieldId Field id\n * @param {\"double\"|\"float\"|\"int32\"|\"uint32\"|\"sint32\"|\"fixed32\"|\"sfixed32\"|\"int64\"|\"uint64\"|\"sint64\"|\"fixed64\"|\"sfixed64\"|\"string\"|\"bool\"|\"bytes\"|Object} fieldType Field type\n * @param {\"optional\"|\"required\"|\"repeated\"} [fieldRule=\"optional\"] Field rule\n * @param {T} [defaultValue] Default value\n * @returns {FieldDecorator} Decorator function\n * @template T extends number | number[] | Long | Long[] | string | string[] | boolean | boolean[] | Uint8Array | Uint8Array[] | Buffer | Buffer[]\n */\nField.d = function decorateField(fieldId, fieldType, fieldRule, defaultValue) {\n\n    // submessage: decorate the submessage and use its name as the type\n    if (typeof fieldType === \"function\")\n        fieldType = util.decorateType(fieldType).name;\n\n    // enum reference: create a reflected copy of the enum and keep reuseing it\n    else if (fieldType && typeof fieldType === \"object\")\n        fieldType = util.decorateEnum(fieldType).name;\n\n    return function fieldDecorator(prototype, fieldName) {\n        util.decorateType(prototype.constructor)\n            .add(new Field(fieldName, fieldId, fieldType, fieldRule, { \"default\": defaultValue }));\n    };\n};\n\n/**\n * Field decorator (TypeScript).\n * @name Field.d\n * @function\n * @param {number} fieldId Field id\n * @param {Constructor<T>|string} fieldType Field type\n * @param {\"optional\"|\"required\"|\"repeated\"} [fieldRule=\"optional\"] Field rule\n * @returns {FieldDecorator} Decorator function\n * @template T extends Message<T>\n * @variation 2\n */\n// like Field.d but without a default value\n\n// Sets up cyclic dependencies (called in index-light)\nField._configure = function configure(Type_) {\n    Type = Type_;\n};\n", "\"use strict\";\nvar protobuf = module.exports = require(18);\n\nprotobuf.build = \"light\";\n\n/**\n * A node-style callback as used by {@link load} and {@link Root#load}.\n * @typedef LoadCallback\n * @type {function}\n * @param {Error|null} error Error, if any, otherwise `null`\n * @param {Root} [root] Root, if there hasn't been an error\n * @returns {undefined}\n */\n\n/**\n * Loads one or multiple .proto or preprocessed .json files into a common root namespace and calls the callback.\n * @param {string|string[]} filename One or multiple files to load\n * @param {Root} root Root namespace, defaults to create a new one if omitted.\n * @param {LoadCallback} callback Callback function\n * @returns {undefined}\n * @see {@link Root#load}\n */\nfunction load(filename, root, callback) {\n    if (typeof root === \"function\") {\n        callback = root;\n        root = new protobuf.Root();\n    } else if (!root)\n        root = new protobuf.Root();\n    return root.load(filename, callback);\n}\n\n/**\n * Loads one or multiple .proto or preprocessed .json files into a common root namespace and calls the callback.\n * @name load\n * @function\n * @param {string|string[]} filename One or multiple files to load\n * @param {LoadCallback} callback Callback function\n * @returns {undefined}\n * @see {@link Root#load}\n * @variation 2\n */\n// function load(filename:string, callback:LoadCallback):undefined\n\n/**\n * Loads one or multiple .proto or preprocessed .json files into a common root namespace and returns a promise.\n * @name load\n * @function\n * @param {string|string[]} filename One or multiple files to load\n * @param {Root} [root] Root namespace, defaults to create a new one if omitted.\n * @returns {Promise<Root>} Promise\n * @see {@link Root#load}\n * @variation 3\n */\n// function load(filename:string, [root:Root]):Promise<Root>\n\nprotobuf.load = load;\n\n/**\n * Synchronously loads one or multiple .proto or preprocessed .json files into a common root namespace (node only).\n * @param {string|string[]} filename One or multiple files to load\n * @param {Root} [root] Root namespace, defaults to create a new one if omitted.\n * @returns {Root} Root namespace\n * @throws {Error} If synchronous fetching is not supported (i.e. in browsers) or if a file's syntax is invalid\n * @see {@link Root#loadSync}\n */\nfunction loadSync(filename, root) {\n    if (!root)\n        root = new protobuf.Root();\n    return root.loadSync(filename);\n}\n\nprotobuf.loadSync = loadSync;\n\n// Serialization\nprotobuf.encoder          = require(14);\nprotobuf.decoder          = require(13);\nprotobuf.verifier         = require(40);\nprotobuf.converter        = require(12);\n\n// Reflection\nprotobuf.ReflectionObject = require(24);\nprotobuf.Namespace        = require(23);\nprotobuf.Root             = require(29);\nprotobuf.Enum             = require(15);\nprotobuf.Type             = require(35);\nprotobuf.Field            = require(16);\nprotobuf.OneOf            = require(25);\nprotobuf.MapField         = require(20);\nprotobuf.Service          = require(33);\nprotobuf.Method           = require(22);\n\n// Runtime\nprotobuf.Message          = require(21);\nprotobuf.wrappers         = require(41);\n\n// Utility\nprotobuf.types            = require(36);\nprotobuf.util             = require(37);\n\n// Set up possibly cyclic reflection dependencies\nprotobuf.ReflectionObject._configure(protobuf.Root);\nprotobuf.Namespace._configure(protobuf.Type, protobuf.Service, protobuf.Enum);\nprotobuf.Root._configure(protobuf.Type);\nprotobuf.Field._configure(protobuf.Type);\n", "\"use strict\";\nvar protobuf = exports;\n\n/**\n * Build type, one of `\"full\"`, `\"light\"` or `\"minimal\"`.\n * @name build\n * @type {string}\n * @const\n */\nprotobuf.build = \"minimal\";\n\n// Serialization\nprotobuf.Writer       = require(42);\nprotobuf.BufferWriter = require(43);\nprotobuf.Reader       = require(27);\nprotobuf.BufferReader = require(28);\n\n// Utility\nprotobuf.util         = require(39);\nprotobuf.rpc          = require(31);\nprotobuf.roots        = require(30);\nprotobuf.configure    = configure;\n\n/* istanbul ignore next */\n/**\n * Reconfigures the library according to the environment.\n * @returns {undefined}\n */\nfunction configure() {\n    protobuf.util._configure();\n    protobuf.Writer._configure(protobuf.BufferWriter);\n    protobuf.Reader._configure(protobuf.BufferReader);\n}\n\n// Set up buffer utility according to the environment\nconfigure();\n", "\"use strict\";\nvar protobuf = module.exports = require(17);\n\nprotobuf.build = \"full\";\n\n// Parser\nprotobuf.tokenize         = require(34);\nprotobuf.parse            = require(26);\nprotobuf.common           = require(11);\n\n// Configure parser\nprotobuf.Root._configure(protobuf.Type, protobuf.parse, protobuf.common);\n", "\"use strict\";\nmodule.exports = MapField;\n\n// extends Field\nvar Field = require(16);\n((MapField.prototype = Object.create(Field.prototype)).constructor = MapField).className = \"MapField\";\n\nvar types   = require(36),\n    util    = require(37);\n\n/**\n * Constructs a new map field instance.\n * @classdesc Reflected map field.\n * @extends FieldBase\n * @constructor\n * @param {string} name Unique name within its namespace\n * @param {number} id Unique id within its namespace\n * @param {string} keyType Key type\n * @param {string} type Value type\n * @param {Object.<string,*>} [options] Declared options\n * @param {string} [comment] Comment associated with this field\n */\nfunction MapField(name, id, keyType, type, options, comment) {\n    Field.call(this, name, id, type, undefined, undefined, options, comment);\n\n    /* istanbul ignore if */\n    if (!util.isString(keyType))\n        throw TypeError(\"keyType must be a string\");\n\n    /**\n     * Key type.\n     * @type {string}\n     */\n    this.keyType = keyType; // toJSON, marker\n\n    /**\n     * Resolved key type if not a basic type.\n     * @type {ReflectionObject|null}\n     */\n    this.resolvedKeyType = null;\n\n    // Overrides Field#map\n    this.map = true;\n}\n\n/**\n * Map field descriptor.\n * @interface IMapField\n * @extends {IField}\n * @property {string} keyType Key type\n */\n\n/**\n * Extension map field descriptor.\n * @interface IExtensionMapField\n * @extends IMapField\n * @property {string} extend Extended type\n */\n\n/**\n * Constructs a map field from a map field descriptor.\n * @param {string} name Field name\n * @param {IMapField} json Map field descriptor\n * @returns {MapField} Created map field\n * @throws {TypeError} If arguments are invalid\n */\nMapField.fromJSON = function fromJSON(name, json) {\n    return new MapField(name, json.id, json.keyType, json.type, json.options, json.comment);\n};\n\n/**\n * Converts this map field to a map field descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IMapField} Map field descriptor\n */\nMapField.prototype.toJSON = function toJSON(toJSONOptions) {\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"keyType\" , this.keyType,\n        \"type\"    , this.type,\n        \"id\"      , this.id,\n        \"extend\"  , this.extend,\n        \"options\" , this.options,\n        \"comment\" , keepComments ? this.comment : undefined\n    ]);\n};\n\n/**\n * @override\n */\nMapField.prototype.resolve = function resolve() {\n    if (this.resolved)\n        return this;\n\n    // Besides a value type, map fields have a key type that may be \"any scalar type except for floating point types and bytes\"\n    if (types.mapKey[this.keyType] === undefined)\n        throw Error(\"invalid key type: \" + this.keyType);\n\n    return Field.prototype.resolve.call(this);\n};\n\n/**\n * Map field decorator (TypeScript).\n * @name MapField.d\n * @function\n * @param {number} fieldId Field id\n * @param {\"int32\"|\"uint32\"|\"sint32\"|\"fixed32\"|\"sfixed32\"|\"int64\"|\"uint64\"|\"sint64\"|\"fixed64\"|\"sfixed64\"|\"bool\"|\"string\"} fieldKeyType Field key type\n * @param {\"double\"|\"float\"|\"int32\"|\"uint32\"|\"sint32\"|\"fixed32\"|\"sfixed32\"|\"int64\"|\"uint64\"|\"sint64\"|\"fixed64\"|\"sfixed64\"|\"bool\"|\"string\"|\"bytes\"|Object|Constructor<{}>} fieldValueType Field value type\n * @returns {FieldDecorator} Decorator function\n * @template T extends { [key: string]: number | Long | string | boolean | Uint8Array | Buffer | number[] | Message<{}> }\n */\nMapField.d = function decorateMapField(fieldId, fieldKeyType, fieldValueType) {\n\n    // submessage value: decorate the submessage and use its name as the type\n    if (typeof fieldValueType === \"function\")\n        fieldValueType = util.decorateType(fieldValueType).name;\n\n    // enum reference value: create a reflected copy of the enum and keep reuseing it\n    else if (fieldValueType && typeof fieldValueType === \"object\")\n        fieldValueType = util.decorateEnum(fieldValueType).name;\n\n    return function mapFieldDecorator(prototype, fieldName) {\n        util.decorateType(prototype.constructor)\n            .add(new MapField(fieldName, fieldId, fieldKeyType, fieldValueType));\n    };\n};\n", "\"use strict\";\nmodule.exports = Message;\n\nvar util = require(39);\n\n/**\n * Constructs a new message instance.\n * @classdesc Abstract runtime message.\n * @constructor\n * @param {Properties<T>} [properties] Properties to set\n * @template T extends object = object\n */\nfunction Message(properties) {\n    // not used internally\n    if (properties)\n        for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)\n            this[keys[i]] = properties[keys[i]];\n}\n\n/**\n * Reference to the reflected type.\n * @name Message.$type\n * @type {Type}\n * @readonly\n */\n\n/**\n * Reference to the reflected type.\n * @name Message#$type\n * @type {Type}\n * @readonly\n */\n\n/*eslint-disable valid-jsdoc*/\n\n/**\n * Creates a new message of this type using the specified properties.\n * @param {Object.<string,*>} [properties] Properties to set\n * @returns {Message<T>} Message instance\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.create = function create(properties) {\n    return this.$type.create(properties);\n};\n\n/**\n * Encodes a message of this type.\n * @param {T|Object.<string,*>} message Message to encode\n * @param {Writer} [writer] Writer to use\n * @returns {Writer} Writer\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.encode = function encode(message, writer) {\n    return this.$type.encode(message, writer);\n};\n\n/**\n * Encodes a message of this type preceeded by its length as a varint.\n * @param {T|Object.<string,*>} message Message to encode\n * @param {Writer} [writer] Writer to use\n * @returns {Writer} Writer\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.encodeDelimited = function encodeDelimited(message, writer) {\n    return this.$type.encodeDelimited(message, writer);\n};\n\n/**\n * Decodes a message of this type.\n * @name Message.decode\n * @function\n * @param {Reader|Uint8Array} reader Reader or buffer to decode\n * @returns {T} Decoded message\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.decode = function decode(reader) {\n    return this.$type.decode(reader);\n};\n\n/**\n * Decodes a message of this type preceeded by its length as a varint.\n * @name Message.decodeDelimited\n * @function\n * @param {Reader|Uint8Array} reader Reader or buffer to decode\n * @returns {T} Decoded message\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.decodeDelimited = function decodeDelimited(reader) {\n    return this.$type.decodeDelimited(reader);\n};\n\n/**\n * Verifies a message of this type.\n * @name Message.verify\n * @function\n * @param {Object.<string,*>} message Plain object to verify\n * @returns {string|null} `null` if valid, otherwise the reason why it is not\n */\nMessage.verify = function verify(message) {\n    return this.$type.verify(message);\n};\n\n/**\n * Creates a new message of this type from a plain object. Also converts values to their respective internal types.\n * @param {Object.<string,*>} object Plain object\n * @returns {T} Message instance\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.fromObject = function fromObject(object) {\n    return this.$type.fromObject(object);\n};\n\n/**\n * Creates a plain object from a message of this type. Also converts values to other types if specified.\n * @param {T} message Message instance\n * @param {IConversionOptions} [options] Conversion options\n * @returns {Object.<string,*>} Plain object\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.toObject = function toObject(message, options) {\n    return this.$type.toObject(message, options);\n};\n\n/**\n * Converts this message to JSON.\n * @returns {Object.<string,*>} JSON object\n */\nMessage.prototype.toJSON = function toJSON() {\n    return this.$type.toObject(this, util.toJSONOptions);\n};\n\n/*eslint-enable valid-jsdoc*/", "\"use strict\";\nmodule.exports = Method;\n\n// extends ReflectionObject\nvar ReflectionObject = require(24);\n((Method.prototype = Object.create(ReflectionObject.prototype)).constructor = Method).className = \"Method\";\n\nvar util = require(37);\n\n/**\n * Constructs a new service method instance.\n * @classdesc Reflected service method.\n * @extends ReflectionObject\n * @constructor\n * @param {string} name Method name\n * @param {string|undefined} type Method type, usually `\"rpc\"`\n * @param {string} requestType Request message type\n * @param {string} responseType Response message type\n * @param {boolean|Object.<string,*>} [requestStream] Whether the request is streamed\n * @param {boolean|Object.<string,*>} [responseStream] Whether the response is streamed\n * @param {Object.<string,*>} [options] Declared options\n * @param {string} [comment] The comment for this method\n * @param {Object.<string,*>} [parsedOptions] Declared options, properly parsed into an object\n */\nfunction Method(name, type, requestType, responseType, requestStream, responseStream, options, comment, parsedOptions) {\n\n    /* istanbul ignore next */\n    if (util.isObject(requestStream)) {\n        options = requestStream;\n        requestStream = responseStream = undefined;\n    } else if (util.isObject(responseStream)) {\n        options = responseStream;\n        responseStream = undefined;\n    }\n\n    /* istanbul ignore if */\n    if (!(type === undefined || util.isString(type)))\n        throw TypeError(\"type must be a string\");\n\n    /* istanbul ignore if */\n    if (!util.isString(requestType))\n        throw TypeError(\"requestType must be a string\");\n\n    /* istanbul ignore if */\n    if (!util.isString(responseType))\n        throw TypeError(\"responseType must be a string\");\n\n    ReflectionObject.call(this, name, options);\n\n    /**\n     * Method type.\n     * @type {string}\n     */\n    this.type = type || \"rpc\"; // toJSON\n\n    /**\n     * Request type.\n     * @type {string}\n     */\n    this.requestType = requestType; // toJSON, marker\n\n    /**\n     * Whether requests are streamed or not.\n     * @type {boolean|undefined}\n     */\n    this.requestStream = requestStream ? true : undefined; // toJSON\n\n    /**\n     * Response type.\n     * @type {string}\n     */\n    this.responseType = responseType; // toJSON\n\n    /**\n     * Whether responses are streamed or not.\n     * @type {boolean|undefined}\n     */\n    this.responseStream = responseStream ? true : undefined; // toJSON\n\n    /**\n     * Resolved request type.\n     * @type {Type|null}\n     */\n    this.resolvedRequestType = null;\n\n    /**\n     * Resolved response type.\n     * @type {Type|null}\n     */\n    this.resolvedResponseType = null;\n\n    /**\n     * Comment for this method\n     * @type {string|null}\n     */\n    this.comment = comment;\n\n    /**\n     * Options properly parsed into an object\n     */\n    this.parsedOptions = parsedOptions;\n}\n\n/**\n * Method descriptor.\n * @interface IMethod\n * @property {string} [type=\"rpc\"] Method type\n * @property {string} requestType Request type\n * @property {string} responseType Response type\n * @property {boolean} [requestStream=false] Whether requests are streamed\n * @property {boolean} [responseStream=false] Whether responses are streamed\n * @property {Object.<string,*>} [options] Method options\n * @property {string} comment Method comments\n * @property {Object.<string,*>} [parsedOptions] Method options properly parsed into an object\n */\n\n/**\n * Constructs a method from a method descriptor.\n * @param {string} name Method name\n * @param {IMethod} json Method descriptor\n * @returns {Method} Created method\n * @throws {TypeError} If arguments are invalid\n */\nMethod.fromJSON = function fromJSON(name, json) {\n    return new Method(name, json.type, json.requestType, json.responseType, json.requestStream, json.responseStream, json.options, json.comment, json.parsedOptions);\n};\n\n/**\n * Converts this method to a method descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IMethod} Method descriptor\n */\nMethod.prototype.toJSON = function toJSON(toJSONOptions) {\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"type\"           , this.type !== \"rpc\" && /* istanbul ignore next */ this.type || undefined,\n        \"requestType\"    , this.requestType,\n        \"requestStream\"  , this.requestStream,\n        \"responseType\"   , this.responseType,\n        \"responseStream\" , this.responseStream,\n        \"options\"        , this.options,\n        \"comment\"        , keepComments ? this.comment : undefined,\n        \"parsedOptions\"  , this.parsedOptions,\n    ]);\n};\n\n/**\n * @override\n */\nMethod.prototype.resolve = function resolve() {\n\n    /* istanbul ignore if */\n    if (this.resolved)\n        return this;\n\n    this.resolvedRequestType = this.parent.lookupType(this.requestType);\n    this.resolvedResponseType = this.parent.lookupType(this.responseType);\n\n    return ReflectionObject.prototype.resolve.call(this);\n};\n", "\"use strict\";\nmodule.exports = Namespace;\n\n// extends ReflectionObject\nvar ReflectionObject = require(24);\n((Namespace.prototype = Object.create(ReflectionObject.prototype)).constructor = Namespace).className = \"Namespace\";\n\nvar Field    = require(16),\n    util     = require(37),\n    OneOf    = require(25);\n\nvar Type,    // cyclic\n    Service,\n    Enum;\n\n/**\n * Constructs a new namespace instance.\n * @name Namespace\n * @classdesc Reflected namespace.\n * @extends NamespaceBase\n * @constructor\n * @param {string} name Namespace name\n * @param {Object.<string,*>} [options] Declared options\n */\n\n/**\n * Constructs a namespace from JSON.\n * @memberof Namespace\n * @function\n * @param {string} name Namespace name\n * @param {Object.<string,*>} json JSON object\n * @returns {Namespace} Created namespace\n * @throws {TypeError} If arguments are invalid\n */\nNamespace.fromJSON = function fromJSON(name, json) {\n    return new Namespace(name, json.options).addJSON(json.nested);\n};\n\n/**\n * Converts an array of reflection objects to JSON.\n * @memberof Namespace\n * @param {ReflectionObject[]} array Object array\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {Object.<string,*>|undefined} JSON object or `undefined` when array is empty\n */\nfunction arrayToJSON(array, toJSONOptions) {\n    if (!(array && array.length))\n        return undefined;\n    var obj = {};\n    for (var i = 0; i < array.length; ++i)\n        obj[array[i].name] = array[i].toJSON(toJSONOptions);\n    return obj;\n}\n\nNamespace.arrayToJSON = arrayToJSON;\n\n/**\n * Tests if the specified id is reserved.\n * @param {Array.<number[]|string>|undefined} reserved Array of reserved ranges and names\n * @param {number} id Id to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nNamespace.isReservedId = function isReservedId(reserved, id) {\n    if (reserved)\n        for (var i = 0; i < reserved.length; ++i)\n            if (typeof reserved[i] !== \"string\" && reserved[i][0] <= id && reserved[i][1] > id)\n                return true;\n    return false;\n};\n\n/**\n * Tests if the specified name is reserved.\n * @param {Array.<number[]|string>|undefined} reserved Array of reserved ranges and names\n * @param {string} name Name to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nNamespace.isReservedName = function isReservedName(reserved, name) {\n    if (reserved)\n        for (var i = 0; i < reserved.length; ++i)\n            if (reserved[i] === name)\n                return true;\n    return false;\n};\n\n/**\n * Not an actual constructor. Use {@link Namespace} instead.\n * @classdesc Base class of all reflection objects containing nested objects. This is not an actual class but here for the sake of having consistent type definitions.\n * @exports NamespaceBase\n * @extends ReflectionObject\n * @abstract\n * @constructor\n * @param {string} name Namespace name\n * @param {Object.<string,*>} [options] Declared options\n * @see {@link Namespace}\n */\nfunction Namespace(name, options) {\n    ReflectionObject.call(this, name, options);\n\n    /**\n     * Nested objects by name.\n     * @type {Object.<string,ReflectionObject>|undefined}\n     */\n    this.nested = undefined; // toJSON\n\n    /**\n     * Cached nested objects as an array.\n     * @type {ReflectionObject[]|null}\n     * @private\n     */\n    this._nestedArray = null;\n}\n\nfunction clearCache(namespace) {\n    namespace._nestedArray = null;\n    return namespace;\n}\n\n/**\n * Nested objects of this namespace as an array for iteration.\n * @name NamespaceBase#nestedArray\n * @type {ReflectionObject[]}\n * @readonly\n */\nObject.defineProperty(Namespace.prototype, \"nestedArray\", {\n    get: function() {\n        return this._nestedArray || (this._nestedArray = util.toArray(this.nested));\n    }\n});\n\n/**\n * Namespace descriptor.\n * @interface INamespace\n * @property {Object.<string,*>} [options] Namespace options\n * @property {Object.<string,AnyNestedObject>} [nested] Nested object descriptors\n */\n\n/**\n * Any extension field descriptor.\n * @typedef AnyExtensionField\n * @type {IExtensionField|IExtensionMapField}\n */\n\n/**\n * Any nested object descriptor.\n * @typedef AnyNestedObject\n * @type {IEnum|IType|IService|AnyExtensionField|INamespace}\n */\n// ^ BEWARE: VSCode hangs forever when using more than 5 types (that's why AnyExtensionField exists in the first place)\n\n/**\n * Converts this namespace to a namespace descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {INamespace} Namespace descriptor\n */\nNamespace.prototype.toJSON = function toJSON(toJSONOptions) {\n    return util.toObject([\n        \"options\" , this.options,\n        \"nested\"  , arrayToJSON(this.nestedArray, toJSONOptions)\n    ]);\n};\n\n/**\n * Adds nested objects to this namespace from nested object descriptors.\n * @param {Object.<string,AnyNestedObject>} nestedJson Any nested object descriptors\n * @returns {Namespace} `this`\n */\nNamespace.prototype.addJSON = function addJSON(nestedJson) {\n    var ns = this;\n    /* istanbul ignore else */\n    if (nestedJson) {\n        for (var names = Object.keys(nestedJson), i = 0, nested; i < names.length; ++i) {\n            nested = nestedJson[names[i]];\n            ns.add( // most to least likely\n                ( nested.fields !== undefined\n                ? Type.fromJSON\n                : nested.values !== undefined\n                ? Enum.fromJSON\n                : nested.methods !== undefined\n                ? Service.fromJSON\n                : nested.id !== undefined\n                ? Field.fromJSON\n                : Namespace.fromJSON )(names[i], nested)\n            );\n        }\n    }\n    return this;\n};\n\n/**\n * Gets the nested object of the specified name.\n * @param {string} name Nested object name\n * @returns {ReflectionObject|null} The reflection object or `null` if it doesn't exist\n */\nNamespace.prototype.get = function get(name) {\n    return this.nested && this.nested[name]\n        || null;\n};\n\n/**\n * Gets the values of the nested {@link Enum|enum} of the specified name.\n * This methods differs from {@link Namespace#get|get} in that it returns an enum's values directly and throws instead of returning `null`.\n * @param {string} name Nested enum name\n * @returns {Object.<string,number>} Enum values\n * @throws {Error} If there is no such enum\n */\nNamespace.prototype.getEnum = function getEnum(name) {\n    if (this.nested && this.nested[name] instanceof Enum)\n        return this.nested[name].values;\n    throw Error(\"no such enum: \" + name);\n};\n\n/**\n * Adds a nested object to this namespace.\n * @param {ReflectionObject} object Nested object to add\n * @returns {Namespace} `this`\n * @throws {TypeError} If arguments are invalid\n * @throws {Error} If there is already a nested object with this name\n */\nNamespace.prototype.add = function add(object) {\n\n    if (!(object instanceof Field && object.extend !== undefined || object instanceof Type  || object instanceof OneOf || object instanceof Enum || object instanceof Service || object instanceof Namespace))\n        throw TypeError(\"object must be a valid nested object\");\n\n    if (!this.nested)\n        this.nested = {};\n    else {\n        var prev = this.get(object.name);\n        if (prev) {\n            if (prev instanceof Namespace && object instanceof Namespace && !(prev instanceof Type || prev instanceof Service)) {\n                // replace plain namespace but keep existing nested elements and options\n                var nested = prev.nestedArray;\n                for (var i = 0; i < nested.length; ++i)\n                    object.add(nested[i]);\n                this.remove(prev);\n                if (!this.nested)\n                    this.nested = {};\n                object.setOptions(prev.options, true);\n\n            } else\n                throw Error(\"duplicate name '\" + object.name + \"' in \" + this);\n        }\n    }\n    this.nested[object.name] = object;\n    object.onAdd(this);\n    return clearCache(this);\n};\n\n/**\n * Removes a nested object from this namespace.\n * @param {ReflectionObject} object Nested object to remove\n * @returns {Namespace} `this`\n * @throws {TypeError} If arguments are invalid\n * @throws {Error} If `object` is not a member of this namespace\n */\nNamespace.prototype.remove = function remove(object) {\n\n    if (!(object instanceof ReflectionObject))\n        throw TypeError(\"object must be a ReflectionObject\");\n    if (object.parent !== this)\n        throw Error(object + \" is not a member of \" + this);\n\n    delete this.nested[object.name];\n    if (!Object.keys(this.nested).length)\n        this.nested = undefined;\n\n    object.onRemove(this);\n    return clearCache(this);\n};\n\n/**\n * Defines additial namespaces within this one if not yet existing.\n * @param {string|string[]} path Path to create\n * @param {*} [json] Nested types to create from JSON\n * @returns {Namespace} Pointer to the last namespace created or `this` if path is empty\n */\nNamespace.prototype.define = function define(path, json) {\n\n    if (util.isString(path))\n        path = path.split(\".\");\n    else if (!Array.isArray(path))\n        throw TypeError(\"illegal path\");\n    if (path && path.length && path[0] === \"\")\n        throw Error(\"path must be relative\");\n\n    var ptr = this;\n    while (path.length > 0) {\n        var part = path.shift();\n        if (ptr.nested && ptr.nested[part]) {\n            ptr = ptr.nested[part];\n            if (!(ptr instanceof Namespace))\n                throw Error(\"path conflicts with non-namespace objects\");\n        } else\n            ptr.add(ptr = new Namespace(part));\n    }\n    if (json)\n        ptr.addJSON(json);\n    return ptr;\n};\n\n/**\n * Resolves this namespace's and all its nested objects' type references. Useful to validate a reflection tree, but comes at a cost.\n * @returns {Namespace} `this`\n */\nNamespace.prototype.resolveAll = function resolveAll() {\n    var nested = this.nestedArray, i = 0;\n    while (i < nested.length)\n        if (nested[i] instanceof Namespace)\n            nested[i++].resolveAll();\n        else\n            nested[i++].resolve();\n    return this.resolve();\n};\n\n/**\n * Recursively looks up the reflection object matching the specified path in the scope of this namespace.\n * @param {string|string[]} path Path to look up\n * @param {*|Array.<*>} filterTypes Filter types, any combination of the constructors of `protobuf.Type`, `protobuf.Enum`, `protobuf.Service` etc.\n * @param {boolean} [parentAlreadyChecked=false] If known, whether the parent has already been checked\n * @returns {ReflectionObject|null} Looked up object or `null` if none could be found\n */\nNamespace.prototype.lookup = function lookup(path, filterTypes, parentAlreadyChecked) {\n\n    /* istanbul ignore next */\n    if (typeof filterTypes === \"boolean\") {\n        parentAlreadyChecked = filterTypes;\n        filterTypes = undefined;\n    } else if (filterTypes && !Array.isArray(filterTypes))\n        filterTypes = [ filterTypes ];\n\n    if (util.isString(path) && path.length) {\n        if (path === \".\")\n            return this.root;\n        path = path.split(\".\");\n    } else if (!path.length)\n        return this;\n\n    // Start at root if path is absolute\n    if (path[0] === \"\")\n        return this.root.lookup(path.slice(1), filterTypes);\n\n    // Test if the first part matches any nested object, and if so, traverse if path contains more\n    var found = this.get(path[0]);\n    if (found) {\n        if (path.length === 1) {\n            if (!filterTypes || filterTypes.indexOf(found.constructor) > -1)\n                return found;\n        } else if (found instanceof Namespace && (found = found.lookup(path.slice(1), filterTypes, true)))\n            return found;\n\n    // Otherwise try each nested namespace\n    } else\n        for (var i = 0; i < this.nestedArray.length; ++i)\n            if (this._nestedArray[i] instanceof Namespace && (found = this._nestedArray[i].lookup(path, filterTypes, true)))\n                return found;\n\n    // If there hasn't been a match, try again at the parent\n    if (this.parent === null || parentAlreadyChecked)\n        return null;\n    return this.parent.lookup(path, filterTypes);\n};\n\n/**\n * Looks up the reflection object at the specified path, relative to this namespace.\n * @name NamespaceBase#lookup\n * @function\n * @param {string|string[]} path Path to look up\n * @param {boolean} [parentAlreadyChecked=false] Whether the parent has already been checked\n * @returns {ReflectionObject|null} Looked up object or `null` if none could be found\n * @variation 2\n */\n// lookup(path: string, [parentAlreadyChecked: boolean])\n\n/**\n * Looks up the {@link Type|type} at the specified path, relative to this namespace.\n * Besides its signature, this methods differs from {@link Namespace#lookup|lookup} in that it throws instead of returning `null`.\n * @param {string|string[]} path Path to look up\n * @returns {Type} Looked up type\n * @throws {Error} If `path` does not point to a type\n */\nNamespace.prototype.lookupType = function lookupType(path) {\n    var found = this.lookup(path, [ Type ]);\n    if (!found)\n        throw Error(\"no such type: \" + path);\n    return found;\n};\n\n/**\n * Looks up the values of the {@link Enum|enum} at the specified path, relative to this namespace.\n * Besides its signature, this methods differs from {@link Namespace#lookup|lookup} in that it throws instead of returning `null`.\n * @param {string|string[]} path Path to look up\n * @returns {Enum} Looked up enum\n * @throws {Error} If `path` does not point to an enum\n */\nNamespace.prototype.lookupEnum = function lookupEnum(path) {\n    var found = this.lookup(path, [ Enum ]);\n    if (!found)\n        throw Error(\"no such Enum '\" + path + \"' in \" + this);\n    return found;\n};\n\n/**\n * Looks up the {@link Type|type} or {@link Enum|enum} at the specified path, relative to this namespace.\n * Besides its signature, this methods differs from {@link Namespace#lookup|lookup} in that it throws instead of returning `null`.\n * @param {string|string[]} path Path to look up\n * @returns {Type} Looked up type or enum\n * @throws {Error} If `path` does not point to a type or enum\n */\nNamespace.prototype.lookupTypeOrEnum = function lookupTypeOrEnum(path) {\n    var found = this.lookup(path, [ Type, Enum ]);\n    if (!found)\n        throw Error(\"no such Type or Enum '\" + path + \"' in \" + this);\n    return found;\n};\n\n/**\n * Looks up the {@link Service|service} at the specified path, relative to this namespace.\n * Besides its signature, this methods differs from {@link Namespace#lookup|lookup} in that it throws instead of returning `null`.\n * @param {string|string[]} path Path to look up\n * @returns {Service} Looked up service\n * @throws {Error} If `path` does not point to a service\n */\nNamespace.prototype.lookupService = function lookupService(path) {\n    var found = this.lookup(path, [ Service ]);\n    if (!found)\n        throw Error(\"no such Service '\" + path + \"' in \" + this);\n    return found;\n};\n\n// Sets up cyclic dependencies (called in index-light)\nNamespace._configure = function(Type_, Service_, Enum_) {\n    Type    = Type_;\n    Service = Service_;\n    Enum    = Enum_;\n};\n", "\"use strict\";\nmodule.exports = ReflectionObject;\n\nReflectionObject.className = \"ReflectionObject\";\n\nvar util = require(37);\n\nvar Root; // cyclic\n\n/**\n * Constructs a new reflection object instance.\n * @classdesc Base class of all reflection objects.\n * @constructor\n * @param {string} name Object name\n * @param {Object.<string,*>} [options] Declared options\n * @abstract\n */\nfunction ReflectionObject(name, options) {\n\n    if (!util.isString(name))\n        throw TypeError(\"name must be a string\");\n\n    if (options && !util.isObject(options))\n        throw TypeError(\"options must be an object\");\n\n    /**\n     * Options.\n     * @type {Object.<string,*>|undefined}\n     */\n    this.options = options; // toJSON\n\n    /**\n     * Parsed Options.\n     * @type {Array.<Object.<string,*>>|undefined}\n     */\n    this.parsedOptions = null;\n\n    /**\n     * Unique name within its namespace.\n     * @type {string}\n     */\n    this.name = name;\n\n    /**\n     * Parent namespace.\n     * @type {Namespace|null}\n     */\n    this.parent = null;\n\n    /**\n     * Whether already resolved or not.\n     * @type {boolean}\n     */\n    this.resolved = false;\n\n    /**\n     * Comment text, if any.\n     * @type {string|null}\n     */\n    this.comment = null;\n\n    /**\n     * Defining file name.\n     * @type {string|null}\n     */\n    this.filename = null;\n}\n\nObject.defineProperties(ReflectionObject.prototype, {\n\n    /**\n     * Reference to the root namespace.\n     * @name ReflectionObject#root\n     * @type {Root}\n     * @readonly\n     */\n    root: {\n        get: function() {\n            var ptr = this;\n            while (ptr.parent !== null)\n                ptr = ptr.parent;\n            return ptr;\n        }\n    },\n\n    /**\n     * Full name including leading dot.\n     * @name ReflectionObject#fullName\n     * @type {string}\n     * @readonly\n     */\n    fullName: {\n        get: function() {\n            var path = [ this.name ],\n                ptr = this.parent;\n            while (ptr) {\n                path.unshift(ptr.name);\n                ptr = ptr.parent;\n            }\n            return path.join(\".\");\n        }\n    }\n});\n\n/**\n * Converts this reflection object to its descriptor representation.\n * @returns {Object.<string,*>} Descriptor\n * @abstract\n */\nReflectionObject.prototype.toJSON = /* istanbul ignore next */ function toJSON() {\n    throw Error(); // not implemented, shouldn't happen\n};\n\n/**\n * Called when this object is added to a parent.\n * @param {ReflectionObject} parent Parent added to\n * @returns {undefined}\n */\nReflectionObject.prototype.onAdd = function onAdd(parent) {\n    if (this.parent && this.parent !== parent)\n        this.parent.remove(this);\n    this.parent = parent;\n    this.resolved = false;\n    var root = parent.root;\n    if (root instanceof Root)\n        root._handleAdd(this);\n};\n\n/**\n * Called when this object is removed from a parent.\n * @param {ReflectionObject} parent Parent removed from\n * @returns {undefined}\n */\nReflectionObject.prototype.onRemove = function onRemove(parent) {\n    var root = parent.root;\n    if (root instanceof Root)\n        root._handleRemove(this);\n    this.parent = null;\n    this.resolved = false;\n};\n\n/**\n * Resolves this objects type references.\n * @returns {ReflectionObject} `this`\n */\nReflectionObject.prototype.resolve = function resolve() {\n    if (this.resolved)\n        return this;\n    if (this.root instanceof Root)\n        this.resolved = true; // only if part of a root\n    return this;\n};\n\n/**\n * Gets an option value.\n * @param {string} name Option name\n * @returns {*} Option value or `undefined` if not set\n */\nReflectionObject.prototype.getOption = function getOption(name) {\n    if (this.options)\n        return this.options[name];\n    return undefined;\n};\n\n/**\n * Sets an option.\n * @param {string} name Option name\n * @param {*} value Option value\n * @param {boolean} [ifNotSet] Sets the option only if it isn't currently set\n * @returns {ReflectionObject} `this`\n */\nReflectionObject.prototype.setOption = function setOption(name, value, ifNotSet) {\n    if (!ifNotSet || !this.options || this.options[name] === undefined)\n        (this.options || (this.options = {}))[name] = value;\n    return this;\n};\n\n/**\n * Sets a parsed option.\n * @param {string} name parsed Option name\n * @param {*} value Option value\n * @param {string} propName dot '.' delimited full path of property within the option to set. if undefined\\empty, will add a new option with that value\n * @returns {ReflectionObject} `this`\n */\nReflectionObject.prototype.setParsedOption = function setParsedOption(name, value, propName) {\n    if (!this.parsedOptions) {\n        this.parsedOptions = [];\n    }\n    var parsedOptions = this.parsedOptions;\n    if (propName) {\n        // If setting a sub property of an option then try to merge it\n        // with an existing option\n        var opt = parsedOptions.find(function (opt) {\n            return Object.prototype.hasOwnProperty.call(opt, name);\n        });\n        if (opt) {\n            // If we found an existing option - just merge the property value\n            var newValue = opt[name];\n            util.setProperty(newValue, propName, value);\n        } else {\n            // otherwise, create a new option, set it's property and add it to the list\n            opt = {};\n            opt[name] = util.setProperty({}, propName, value);\n            parsedOptions.push(opt);\n        }\n    } else {\n        // Always create a new option when setting the value of the option itself\n        var newOpt = {};\n        newOpt[name] = value;\n        parsedOptions.push(newOpt);\n    }\n    return this;\n};\n\n/**\n * Sets multiple options.\n * @param {Object.<string,*>} options Options to set\n * @param {boolean} [ifNotSet] Sets an option only if it isn't currently set\n * @returns {ReflectionObject} `this`\n */\nReflectionObject.prototype.setOptions = function setOptions(options, ifNotSet) {\n    if (options)\n        for (var keys = Object.keys(options), i = 0; i < keys.length; ++i)\n            this.setOption(keys[i], options[keys[i]], ifNotSet);\n    return this;\n};\n\n/**\n * Converts this instance to its string representation.\n * @returns {string} Class name[, space, full name]\n */\nReflectionObject.prototype.toString = function toString() {\n    var className = this.constructor.className,\n        fullName  = this.fullName;\n    if (fullName.length)\n        return className + \" \" + fullName;\n    return className;\n};\n\n// Sets up cyclic dependencies (called in index-light)\nReflectionObject._configure = function(Root_) {\n    Root = Root_;\n};\n", "\"use strict\";\nmodule.exports = OneOf;\n\n// extends ReflectionObject\nvar ReflectionObject = require(24);\n((OneOf.prototype = Object.create(ReflectionObject.prototype)).constructor = OneOf).className = \"OneOf\";\n\nvar Field = require(16),\n    util  = require(37);\n\n/**\n * Constructs a new oneof instance.\n * @classdesc Reflected oneof.\n * @extends ReflectionObject\n * @constructor\n * @param {string} name Oneof name\n * @param {string[]|Object.<string,*>} [fieldNames] Field names\n * @param {Object.<string,*>} [options] Declared options\n * @param {string} [comment] Comment associated with this field\n */\nfunction OneOf(name, fieldNames, options, comment) {\n    if (!Array.isArray(fieldNames)) {\n        options = fieldNames;\n        fieldNames = undefined;\n    }\n    ReflectionObject.call(this, name, options);\n\n    /* istanbul ignore if */\n    if (!(fieldNames === undefined || Array.isArray(fieldNames)))\n        throw TypeError(\"fieldNames must be an Array\");\n\n    /**\n     * Field names that belong to this oneof.\n     * @type {string[]}\n     */\n    this.oneof = fieldNames || []; // toJSON, marker\n\n    /**\n     * Fields that belong to this oneof as an array for iteration.\n     * @type {Field[]}\n     * @readonly\n     */\n    this.fieldsArray = []; // declared readonly for conformance, possibly not yet added to parent\n\n    /**\n     * Comment for this field.\n     * @type {string|null}\n     */\n    this.comment = comment;\n}\n\n/**\n * Oneof descriptor.\n * @interface IOneOf\n * @property {Array.<string>} oneof Oneof field names\n * @property {Object.<string,*>} [options] Oneof options\n */\n\n/**\n * Constructs a oneof from a oneof descriptor.\n * @param {string} name Oneof name\n * @param {IOneOf} json Oneof descriptor\n * @returns {OneOf} Created oneof\n * @throws {TypeError} If arguments are invalid\n */\nOneOf.fromJSON = function fromJSON(name, json) {\n    return new OneOf(name, json.oneof, json.options, json.comment);\n};\n\n/**\n * Converts this oneof to a oneof descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IOneOf} Oneof descriptor\n */\nOneOf.prototype.toJSON = function toJSON(toJSONOptions) {\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"options\" , this.options,\n        \"oneof\"   , this.oneof,\n        \"comment\" , keepComments ? this.comment : undefined\n    ]);\n};\n\n/**\n * Adds the fields of the specified oneof to the parent if not already done so.\n * @param {OneOf} oneof The oneof\n * @returns {undefined}\n * @inner\n * @ignore\n */\nfunction addFieldsToParent(oneof) {\n    if (oneof.parent)\n        for (var i = 0; i < oneof.fieldsArray.length; ++i)\n            if (!oneof.fieldsArray[i].parent)\n                oneof.parent.add(oneof.fieldsArray[i]);\n}\n\n/**\n * Adds a field to this oneof and removes it from its current parent, if any.\n * @param {Field} field Field to add\n * @returns {OneOf} `this`\n */\nOneOf.prototype.add = function add(field) {\n\n    /* istanbul ignore if */\n    if (!(field instanceof Field))\n        throw TypeError(\"field must be a Field\");\n\n    if (field.parent && field.parent !== this.parent)\n        field.parent.remove(field);\n    this.oneof.push(field.name);\n    this.fieldsArray.push(field);\n    field.partOf = this; // field.parent remains null\n    addFieldsToParent(this);\n    return this;\n};\n\n/**\n * Removes a field from this oneof and puts it back to the oneof's parent.\n * @param {Field} field Field to remove\n * @returns {OneOf} `this`\n */\nOneOf.prototype.remove = function remove(field) {\n\n    /* istanbul ignore if */\n    if (!(field instanceof Field))\n        throw TypeError(\"field must be a Field\");\n\n    var index = this.fieldsArray.indexOf(field);\n\n    /* istanbul ignore if */\n    if (index < 0)\n        throw Error(field + \" is not a member of \" + this);\n\n    this.fieldsArray.splice(index, 1);\n    index = this.oneof.indexOf(field.name);\n\n    /* istanbul ignore else */\n    if (index > -1) // theoretical\n        this.oneof.splice(index, 1);\n\n    field.partOf = null;\n    return this;\n};\n\n/**\n * @override\n */\nOneOf.prototype.onAdd = function onAdd(parent) {\n    ReflectionObject.prototype.onAdd.call(this, parent);\n    var self = this;\n    // Collect present fields\n    for (var i = 0; i < this.oneof.length; ++i) {\n        var field = parent.get(this.oneof[i]);\n        if (field && !field.partOf) {\n            field.partOf = self;\n            self.fieldsArray.push(field);\n        }\n    }\n    // Add not yet present fields\n    addFieldsToParent(this);\n};\n\n/**\n * @override\n */\nOneOf.prototype.onRemove = function onRemove(parent) {\n    for (var i = 0, field; i < this.fieldsArray.length; ++i)\n        if ((field = this.fieldsArray[i]).parent)\n            field.parent.remove(field);\n    ReflectionObject.prototype.onRemove.call(this, parent);\n};\n\n/**\n * Decorator function as returned by {@link OneOf.d} (TypeScript).\n * @typedef OneOfDecorator\n * @type {function}\n * @param {Object} prototype Target prototype\n * @param {string} oneofName OneOf name\n * @returns {undefined}\n */\n\n/**\n * OneOf decorator (TypeScript).\n * @function\n * @param {...string} fieldNames Field names\n * @returns {OneOfDecorator} Decorator function\n * @template T extends string\n */\nOneOf.d = function decorateOneOf() {\n    var fieldNames = new Array(arguments.length),\n        index = 0;\n    while (index < arguments.length)\n        fieldNames[index] = arguments[index++];\n    return function oneOfDecorator(prototype, oneofName) {\n        util.decorateType(prototype.constructor)\n            .add(new OneOf(oneofName, fieldNames));\n        Object.defineProperty(prototype, oneofName, {\n            get: util.oneOfGetter(fieldNames),\n            set: util.oneOfSetter(fieldNames)\n        });\n    };\n};\n", "\"use strict\";\nmodule.exports = parse;\n\nparse.filename = null;\nparse.defaults = { keepCase: false };\n\nvar tokenize  = require(34),\n    Root      = require(29),\n    Type      = require(35),\n    Field     = require(16),\n    MapField  = require(20),\n    OneOf     = require(25),\n    Enum      = require(15),\n    Service   = require(33),\n    Method    = require(22),\n    types     = require(36),\n    util      = require(37);\n\nvar base10Re    = /^[1-9][0-9]*$/,\n    base10NegRe = /^-?[1-9][0-9]*$/,\n    base16Re    = /^0[x][0-9a-fA-F]+$/,\n    base16NegRe = /^-?0[x][0-9a-fA-F]+$/,\n    base8Re     = /^0[0-7]+$/,\n    base8NegRe  = /^-?0[0-7]+$/,\n    numberRe    = /^(?![eE])[0-9]*(?:\\.[0-9]*)?(?:[eE][+-]?[0-9]+)?$/,\n    nameRe      = /^[a-zA-Z_][a-zA-Z_0-9]*$/,\n    typeRefRe   = /^(?:\\.?[a-zA-Z_][a-zA-Z_0-9]*)(?:\\.[a-zA-Z_][a-zA-Z_0-9]*)*$/,\n    fqTypeRefRe = /^(?:\\.[a-zA-Z_][a-zA-Z_0-9]*)+$/;\n\n/**\n * Result object returned from {@link parse}.\n * @interface IParserResult\n * @property {string|undefined} package Package name, if declared\n * @property {string[]|undefined} imports Imports, if any\n * @property {string[]|undefined} weakImports Weak imports, if any\n * @property {string|undefined} syntax Syntax, if specified (either `\"proto2\"` or `\"proto3\"`)\n * @property {Root} root Populated root instance\n */\n\n/**\n * Options modifying the behavior of {@link parse}.\n * @interface IParseOptions\n * @property {boolean} [keepCase=false] Keeps field casing instead of converting to camel case\n * @property {boolean} [alternateCommentMode=false] Recognize double-slash comments in addition to doc-block comments.\n * @property {boolean} [preferTrailingComment=false] Use trailing comment when both leading comment and trailing comment exist.\n */\n\n/**\n * Options modifying the behavior of JSON serialization.\n * @interface IToJSONOptions\n * @property {boolean} [keepComments=false] Serializes comments.\n */\n\n/**\n * Parses the given .proto source and returns an object with the parsed contents.\n * @param {string} source Source contents\n * @param {Root} root Root to populate\n * @param {IParseOptions} [options] Parse options. Defaults to {@link parse.defaults} when omitted.\n * @returns {IParserResult} Parser result\n * @property {string} filename=null Currently processing file name for error reporting, if known\n * @property {IParseOptions} defaults Default {@link IParseOptions}\n */\nfunction parse(source, root, options) {\n    /* eslint-disable callback-return */\n    if (!(root instanceof Root)) {\n        options = root;\n        root = new Root();\n    }\n    if (!options)\n        options = parse.defaults;\n\n    var preferTrailingComment = options.preferTrailingComment || false;\n    var tn = tokenize(source, options.alternateCommentMode || false),\n        next = tn.next,\n        push = tn.push,\n        peek = tn.peek,\n        skip = tn.skip,\n        cmnt = tn.cmnt;\n\n    var head = true,\n        pkg,\n        imports,\n        weakImports,\n        syntax,\n        isProto3 = false;\n\n    var ptr = root;\n\n    var applyCase = options.keepCase ? function(name) { return name; } : util.camelCase;\n\n    /* istanbul ignore next */\n    function illegal(token, name, insideTryCatch) {\n        var filename = parse.filename;\n        if (!insideTryCatch)\n            parse.filename = null;\n        return Error(\"illegal \" + (name || \"token\") + \" '\" + token + \"' (\" + (filename ? filename + \", \" : \"\") + \"line \" + tn.line + \")\");\n    }\n\n    function readString() {\n        var values = [],\n            token;\n        do {\n            /* istanbul ignore if */\n            if ((token = next()) !== \"\\\"\" && token !== \"'\")\n                throw illegal(token);\n\n            values.push(next());\n            skip(token);\n            token = peek();\n        } while (token === \"\\\"\" || token === \"'\");\n        return values.join(\"\");\n    }\n\n    function readValue(acceptTypeRef) {\n        var token = next();\n        switch (token) {\n            case \"'\":\n            case \"\\\"\":\n                push(token);\n                return readString();\n            case \"true\": case \"TRUE\":\n                return true;\n            case \"false\": case \"FALSE\":\n                return false;\n        }\n        try {\n            return parseNumber(token, /* insideTryCatch */ true);\n        } catch (e) {\n\n            /* istanbul ignore else */\n            if (acceptTypeRef && typeRefRe.test(token))\n                return token;\n\n            /* istanbul ignore next */\n            throw illegal(token, \"value\");\n        }\n    }\n\n    function readRanges(target, acceptStrings) {\n        var token, start;\n        do {\n            if (acceptStrings && ((token = peek()) === \"\\\"\" || token === \"'\"))\n                target.push(readString());\n            else\n                target.push([ start = parseId(next()), skip(\"to\", true) ? parseId(next()) : start ]);\n        } while (skip(\",\", true));\n        skip(\";\");\n    }\n\n    function parseNumber(token, insideTryCatch) {\n        var sign = 1;\n        if (token.charAt(0) === \"-\") {\n            sign = -1;\n            token = token.substring(1);\n        }\n        switch (token) {\n            case \"inf\": case \"INF\": case \"Inf\":\n                return sign * Infinity;\n            case \"nan\": case \"NAN\": case \"Nan\": case \"NaN\":\n                return NaN;\n            case \"0\":\n                return 0;\n        }\n        if (base10Re.test(token))\n            return sign * parseInt(token, 10);\n        if (base16Re.test(token))\n            return sign * parseInt(token, 16);\n        if (base8Re.test(token))\n            return sign * parseInt(token, 8);\n\n        /* istanbul ignore else */\n        if (numberRe.test(token))\n            return sign * parseFloat(token);\n\n        /* istanbul ignore next */\n        throw illegal(token, \"number\", insideTryCatch);\n    }\n\n    function parseId(token, acceptNegative) {\n        switch (token) {\n            case \"max\": case \"MAX\": case \"Max\":\n                return 536870911;\n            case \"0\":\n                return 0;\n        }\n\n        /* istanbul ignore if */\n        if (!acceptNegative && token.charAt(0) === \"-\")\n            throw illegal(token, \"id\");\n\n        if (base10NegRe.test(token))\n            return parseInt(token, 10);\n        if (base16NegRe.test(token))\n            return parseInt(token, 16);\n\n        /* istanbul ignore else */\n        if (base8NegRe.test(token))\n            return parseInt(token, 8);\n\n        /* istanbul ignore next */\n        throw illegal(token, \"id\");\n    }\n\n    function parsePackage() {\n\n        /* istanbul ignore if */\n        if (pkg !== undefined)\n            throw illegal(\"package\");\n\n        pkg = next();\n\n        /* istanbul ignore if */\n        if (!typeRefRe.test(pkg))\n            throw illegal(pkg, \"name\");\n\n        ptr = ptr.define(pkg);\n        skip(\";\");\n    }\n\n    function parseImport() {\n        var token = peek();\n        var whichImports;\n        switch (token) {\n            case \"weak\":\n                whichImports = weakImports || (weakImports = []);\n                next();\n                break;\n            case \"public\":\n                next();\n                // eslint-disable-line no-fallthrough\n            default:\n                whichImports = imports || (imports = []);\n                break;\n        }\n        token = readString();\n        skip(\";\");\n        whichImports.push(token);\n    }\n\n    function parseSyntax() {\n        skip(\"=\");\n        syntax = readString();\n        isProto3 = syntax === \"proto3\";\n\n        /* istanbul ignore if */\n        if (!isProto3 && syntax !== \"proto2\")\n            throw illegal(syntax, \"syntax\");\n\n        skip(\";\");\n    }\n\n    function parseCommon(parent, token) {\n        switch (token) {\n\n            case \"option\":\n                parseOption(parent, token);\n                skip(\";\");\n                return true;\n\n            case \"message\":\n                parseType(parent, token);\n                return true;\n\n            case \"enum\":\n                parseEnum(parent, token);\n                return true;\n\n            case \"service\":\n                parseService(parent, token);\n                return true;\n\n            case \"extend\":\n                parseExtension(parent, token);\n                return true;\n        }\n        return false;\n    }\n\n    function ifBlock(obj, fnIf, fnElse) {\n        var trailingLine = tn.line;\n        if (obj) {\n            if(typeof obj.comment !== \"string\") {\n              obj.comment = cmnt(); // try block-type comment\n            }\n            obj.filename = parse.filename;\n        }\n        if (skip(\"{\", true)) {\n            var token;\n            while ((token = next()) !== \"}\")\n                fnIf(token);\n            skip(\";\", true);\n        } else {\n            if (fnElse)\n                fnElse();\n            skip(\";\");\n            if (obj && (typeof obj.comment !== \"string\" || preferTrailingComment))\n                obj.comment = cmnt(trailingLine) || obj.comment; // try line-type comment\n        }\n    }\n\n    function parseType(parent, token) {\n\n        /* istanbul ignore if */\n        if (!nameRe.test(token = next()))\n            throw illegal(token, \"type name\");\n\n        var type = new Type(token);\n        ifBlock(type, function parseType_block(token) {\n            if (parseCommon(type, token))\n                return;\n\n            switch (token) {\n\n                case \"map\":\n                    parseMapField(type, token);\n                    break;\n\n                case \"required\":\n                case \"repeated\":\n                    parseField(type, token);\n                    break;\n\n                case \"optional\":\n                    /* istanbul ignore if */\n                    if (isProto3) {\n                        parseField(type, \"proto3_optional\");\n                    } else {\n                        parseField(type, \"optional\");\n                    }\n                    break;\n\n                case \"oneof\":\n                    parseOneOf(type, token);\n                    break;\n\n                case \"extensions\":\n                    readRanges(type.extensions || (type.extensions = []));\n                    break;\n\n                case \"reserved\":\n                    readRanges(type.reserved || (type.reserved = []), true);\n                    break;\n\n                default:\n                    /* istanbul ignore if */\n                    if (!isProto3 || !typeRefRe.test(token))\n                        throw illegal(token);\n\n                    push(token);\n                    parseField(type, \"optional\");\n                    break;\n            }\n        });\n        parent.add(type);\n    }\n\n    function parseField(parent, rule, extend) {\n        var type = next();\n        if (type === \"group\") {\n            parseGroup(parent, rule);\n            return;\n        }\n\n        /* istanbul ignore if */\n        if (!typeRefRe.test(type))\n            throw illegal(type, \"type\");\n\n        var name = next();\n\n        /* istanbul ignore if */\n        if (!nameRe.test(name))\n            throw illegal(name, \"name\");\n\n        name = applyCase(name);\n        skip(\"=\");\n\n        var field = new Field(name, parseId(next()), type, rule, extend);\n        ifBlock(field, function parseField_block(token) {\n\n            /* istanbul ignore else */\n            if (token === \"option\") {\n                parseOption(field, token);\n                skip(\";\");\n            } else\n                throw illegal(token);\n\n        }, function parseField_line() {\n            parseInlineOptions(field);\n        });\n\n        if (rule === \"proto3_optional\") {\n            // for proto3 optional fields, we create a single-member Oneof to mimic \"optional\" behavior\n            var oneof = new OneOf(\"_\" + name);\n            field.setOption(\"proto3_optional\", true);\n            oneof.add(field);\n            parent.add(oneof);\n        } else {\n            parent.add(field);\n        }\n\n        // JSON defaults to packed=true if not set so we have to set packed=false explicity when\n        // parsing proto2 descriptors without the option, where applicable. This must be done for\n        // all known packable types and anything that could be an enum (= is not a basic type).\n        if (!isProto3 && field.repeated && (types.packed[type] !== undefined || types.basic[type] === undefined))\n            field.setOption(\"packed\", false, /* ifNotSet */ true);\n    }\n\n    function parseGroup(parent, rule) {\n        var name = next();\n\n        /* istanbul ignore if */\n        if (!nameRe.test(name))\n            throw illegal(name, \"name\");\n\n        var fieldName = util.lcFirst(name);\n        if (name === fieldName)\n            name = util.ucFirst(name);\n        skip(\"=\");\n        var id = parseId(next());\n        var type = new Type(name);\n        type.group = true;\n        var field = new Field(fieldName, id, name, rule);\n        field.filename = parse.filename;\n        ifBlock(type, function parseGroup_block(token) {\n            switch (token) {\n\n                case \"option\":\n                    parseOption(type, token);\n                    skip(\";\");\n                    break;\n\n                case \"required\":\n                case \"repeated\":\n                    parseField(type, token);\n                    break;\n\n                case \"optional\":\n                    /* istanbul ignore if */\n                    if (isProto3) {\n                        parseField(type, \"proto3_optional\");\n                    } else {\n                        parseField(type, \"optional\");\n                    }\n                    break;\n\n                case \"message\":\n                    parseType(type, token);\n                    break;\n\n                case \"enum\":\n                    parseEnum(type, token);\n                    break;\n\n                /* istanbul ignore next */\n                default:\n                    throw illegal(token); // there are no groups with proto3 semantics\n            }\n        });\n        parent.add(type)\n              .add(field);\n    }\n\n    function parseMapField(parent) {\n        skip(\"<\");\n        var keyType = next();\n\n        /* istanbul ignore if */\n        if (types.mapKey[keyType] === undefined)\n            throw illegal(keyType, \"type\");\n\n        skip(\",\");\n        var valueType = next();\n\n        /* istanbul ignore if */\n        if (!typeRefRe.test(valueType))\n            throw illegal(valueType, \"type\");\n\n        skip(\">\");\n        var name = next();\n\n        /* istanbul ignore if */\n        if (!nameRe.test(name))\n            throw illegal(name, \"name\");\n\n        skip(\"=\");\n        var field = new MapField(applyCase(name), parseId(next()), keyType, valueType);\n        ifBlock(field, function parseMapField_block(token) {\n\n            /* istanbul ignore else */\n            if (token === \"option\") {\n                parseOption(field, token);\n                skip(\";\");\n            } else\n                throw illegal(token);\n\n        }, function parseMapField_line() {\n            parseInlineOptions(field);\n        });\n        parent.add(field);\n    }\n\n    function parseOneOf(parent, token) {\n\n        /* istanbul ignore if */\n        if (!nameRe.test(token = next()))\n            throw illegal(token, \"name\");\n\n        var oneof = new OneOf(applyCase(token));\n        ifBlock(oneof, function parseOneOf_block(token) {\n            if (token === \"option\") {\n                parseOption(oneof, token);\n                skip(\";\");\n            } else {\n                push(token);\n                parseField(oneof, \"optional\");\n            }\n        });\n        parent.add(oneof);\n    }\n\n    function parseEnum(parent, token) {\n\n        /* istanbul ignore if */\n        if (!nameRe.test(token = next()))\n            throw illegal(token, \"name\");\n\n        var enm = new Enum(token);\n        ifBlock(enm, function parseEnum_block(token) {\n          switch(token) {\n            case \"option\":\n              parseOption(enm, token);\n              skip(\";\");\n              break;\n\n            case \"reserved\":\n              readRanges(enm.reserved || (enm.reserved = []), true);\n              break;\n\n            default:\n              parseEnumValue(enm, token);\n          }\n        });\n        parent.add(enm);\n    }\n\n    function parseEnumValue(parent, token) {\n\n        /* istanbul ignore if */\n        if (!nameRe.test(token))\n            throw illegal(token, \"name\");\n\n        skip(\"=\");\n        var value = parseId(next(), true),\n            dummy = {\n                options: undefined\n            };\n        dummy.setOption = function(name, value) {\n            if (this.options === undefined)\n                this.options = {};\n            this.options[name] = value;\n        };\n        ifBlock(dummy, function parseEnumValue_block(token) {\n\n            /* istanbul ignore else */\n            if (token === \"option\") {\n                parseOption(dummy, token); // skip\n                skip(\";\");\n            } else\n                throw illegal(token);\n\n        }, function parseEnumValue_line() {\n            parseInlineOptions(dummy); // skip\n        });\n        parent.add(token, value, dummy.comment, dummy.options);\n    }\n\n    function parseOption(parent, token) {\n        var isCustom = skip(\"(\", true);\n\n        /* istanbul ignore if */\n        if (!typeRefRe.test(token = next()))\n            throw illegal(token, \"name\");\n\n        var name = token;\n        var option = name;\n        var propName;\n\n        if (isCustom) {\n            skip(\")\");\n            name = \"(\" + name + \")\";\n            option = name;\n            token = peek();\n            if (fqTypeRefRe.test(token)) {\n                propName = token.slice(1); //remove '.' before property name\n                name += token;\n                next();\n            }\n        }\n        skip(\"=\");\n        var optionValue = parseOptionValue(parent, name);\n        setParsedOption(parent, option, optionValue, propName);\n    }\n\n    function parseOptionValue(parent, name) {\n        // { a: \"foo\" b { c: \"bar\" } }\n        if (skip(\"{\", true)) {\n            var objectResult = {};\n\n            while (!skip(\"}\", true)) {\n                /* istanbul ignore if */\n                if (!nameRe.test(token = next())) {\n                    throw illegal(token, \"name\");\n                }\n\n                var value;\n                var propName = token;\n\n                skip(\":\", true);\n\n                if (peek() === \"{\")\n                    value = parseOptionValue(parent, name + \".\" + token);\n                else if (peek() === \"[\") {\n                    // option (my_option) = {\n                    //     repeated_value: [ \"foo\", \"bar\" ]\n                    // };\n                    value = [];\n                    var lastValue;\n                    if (skip(\"[\", true)) {\n                        do {\n                            lastValue = readValue(true);\n                            value.push(lastValue);\n                        } while (skip(\",\", true));\n                        skip(\"]\");\n                        if (typeof lastValue !== \"undefined\") {\n                            setOption(parent, name + \".\" + token, lastValue);\n                        }\n                    }\n                } else {\n                    value = readValue(true);\n                    setOption(parent, name + \".\" + token, value);\n                }\n\n                var prevValue = objectResult[propName];\n\n                if (prevValue)\n                    value = [].concat(prevValue).concat(value);\n\n                objectResult[propName] = value;\n\n                // Semicolons and commas can be optional\n                skip(\",\", true);\n                skip(\";\", true);\n            }\n\n            return objectResult;\n        }\n\n        var simpleValue = readValue(true);\n        setOption(parent, name, simpleValue);\n        return simpleValue;\n        // Does not enforce a delimiter to be universal\n    }\n\n    function setOption(parent, name, value) {\n        if (parent.setOption)\n            parent.setOption(name, value);\n    }\n\n    function setParsedOption(parent, name, value, propName) {\n        if (parent.setParsedOption)\n            parent.setParsedOption(name, value, propName);\n    }\n\n    function parseInlineOptions(parent) {\n        if (skip(\"[\", true)) {\n            do {\n                parseOption(parent, \"option\");\n            } while (skip(\",\", true));\n            skip(\"]\");\n        }\n        return parent;\n    }\n\n    function parseService(parent, token) {\n\n        /* istanbul ignore if */\n        if (!nameRe.test(token = next()))\n            throw illegal(token, \"service name\");\n\n        var service = new Service(token);\n        ifBlock(service, function parseService_block(token) {\n            if (parseCommon(service, token))\n                return;\n\n            /* istanbul ignore else */\n            if (token === \"rpc\")\n                parseMethod(service, token);\n            else\n                throw illegal(token);\n        });\n        parent.add(service);\n    }\n\n    function parseMethod(parent, token) {\n        // Get the comment of the preceding line now (if one exists) in case the\n        // method is defined across multiple lines.\n        var commentText = cmnt();\n\n        var type = token;\n\n        /* istanbul ignore if */\n        if (!nameRe.test(token = next()))\n            throw illegal(token, \"name\");\n\n        var name = token,\n            requestType, requestStream,\n            responseType, responseStream;\n\n        skip(\"(\");\n        if (skip(\"stream\", true))\n            requestStream = true;\n\n        /* istanbul ignore if */\n        if (!typeRefRe.test(token = next()))\n            throw illegal(token);\n\n        requestType = token;\n        skip(\")\"); skip(\"returns\"); skip(\"(\");\n        if (skip(\"stream\", true))\n            responseStream = true;\n\n        /* istanbul ignore if */\n        if (!typeRefRe.test(token = next()))\n            throw illegal(token);\n\n        responseType = token;\n        skip(\")\");\n\n        var method = new Method(name, type, requestType, responseType, requestStream, responseStream);\n        method.comment = commentText;\n        ifBlock(method, function parseMethod_block(token) {\n\n            /* istanbul ignore else */\n            if (token === \"option\") {\n                parseOption(method, token);\n                skip(\";\");\n            } else\n                throw illegal(token);\n\n        });\n        parent.add(method);\n    }\n\n    function parseExtension(parent, token) {\n\n        /* istanbul ignore if */\n        if (!typeRefRe.test(token = next()))\n            throw illegal(token, \"reference\");\n\n        var reference = token;\n        ifBlock(null, function parseExtension_block(token) {\n            switch (token) {\n\n                case \"required\":\n                case \"repeated\":\n                    parseField(parent, token, reference);\n                    break;\n\n                case \"optional\":\n                    /* istanbul ignore if */\n                    if (isProto3) {\n                        parseField(parent, \"proto3_optional\", reference);\n                    } else {\n                        parseField(parent, \"optional\", reference);\n                    }\n                    break;\n\n                default:\n                    /* istanbul ignore if */\n                    if (!isProto3 || !typeRefRe.test(token))\n                        throw illegal(token);\n                    push(token);\n                    parseField(parent, \"optional\", reference);\n                    break;\n            }\n        });\n    }\n\n    var token;\n    while ((token = next()) !== null) {\n        switch (token) {\n\n            case \"package\":\n\n                /* istanbul ignore if */\n                if (!head)\n                    throw illegal(token);\n\n                parsePackage();\n                break;\n\n            case \"import\":\n\n                /* istanbul ignore if */\n                if (!head)\n                    throw illegal(token);\n\n                parseImport();\n                break;\n\n            case \"syntax\":\n\n                /* istanbul ignore if */\n                if (!head)\n                    throw illegal(token);\n\n                parseSyntax();\n                break;\n\n            case \"option\":\n\n                parseOption(ptr, token);\n                skip(\";\");\n                break;\n\n            default:\n\n                /* istanbul ignore else */\n                if (parseCommon(ptr, token)) {\n                    head = false;\n                    continue;\n                }\n\n                /* istanbul ignore next */\n                throw illegal(token);\n        }\n    }\n\n    parse.filename = null;\n    return {\n        \"package\"     : pkg,\n        \"imports\"     : imports,\n         weakImports  : weakImports,\n         syntax       : syntax,\n         root         : root\n    };\n}\n\n/**\n * Parses the given .proto source and returns an object with the parsed contents.\n * @name parse\n * @function\n * @param {string} source Source contents\n * @param {IParseOptions} [options] Parse options. Defaults to {@link parse.defaults} when omitted.\n * @returns {IParserResult} Parser result\n * @property {string} filename=null Currently processing file name for error reporting, if known\n * @property {IParseOptions} defaults Default {@link IParseOptions}\n * @variation 2\n */\n", "\"use strict\";\nmodule.exports = Reader;\n\nvar util      = require(39);\n\nvar BufferReader; // cyclic\n\nvar LongBits  = util.LongBits,\n    utf8      = util.utf8;\n\n/* istanbul ignore next */\nfunction indexOutOfRange(reader, writeLength) {\n    return RangeError(\"index out of range: \" + reader.pos + \" + \" + (writeLength || 1) + \" > \" + reader.len);\n}\n\n/**\n * Constructs a new reader instance using the specified buffer.\n * @classdesc Wire format reader using `Uint8Array` if available, otherwise `Array`.\n * @constructor\n * @param {Uint8Array} buffer Buffer to read from\n */\nfunction Reader(buffer) {\n\n    /**\n     * Read buffer.\n     * @type {Uint8Array}\n     */\n    this.buf = buffer;\n\n    /**\n     * Read buffer position.\n     * @type {number}\n     */\n    this.pos = 0;\n\n    /**\n     * Read buffer length.\n     * @type {number}\n     */\n    this.len = buffer.length;\n}\n\nvar create_array = typeof Uint8Array !== \"undefined\"\n    ? function create_typed_array(buffer) {\n        if (buffer instanceof Uint8Array || Array.isArray(buffer))\n            return new Reader(buffer);\n        throw Error(\"illegal buffer\");\n    }\n    /* istanbul ignore next */\n    : function create_array(buffer) {\n        if (Array.isArray(buffer))\n            return new Reader(buffer);\n        throw Error(\"illegal buffer\");\n    };\n\nvar create = function create() {\n    return util.Buffer\n        ? function create_buffer_setup(buffer) {\n            return (Reader.create = function create_buffer(buffer) {\n                return util.Buffer.isBuffer(buffer)\n                    ? new BufferReader(buffer)\n                    /* istanbul ignore next */\n                    : create_array(buffer);\n            })(buffer);\n        }\n        /* istanbul ignore next */\n        : create_array;\n};\n\n/**\n * Creates a new reader using the specified buffer.\n * @function\n * @param {Uint8Array|Buffer} buffer Buffer to read from\n * @returns {Reader|BufferReader} A {@link BufferReader} if `buffer` is a Buffer, otherwise a {@link Reader}\n * @throws {Error} If `buffer` is not a valid buffer\n */\nReader.create = create();\n\nReader.prototype._slice = util.Array.prototype.subarray || /* istanbul ignore next */ util.Array.prototype.slice;\n\n/**\n * Reads a varint as an unsigned 32 bit value.\n * @function\n * @returns {number} Value read\n */\nReader.prototype.uint32 = (function read_uint32_setup() {\n    var value = 4294967295; // optimizer type-hint, tends to deopt otherwise (?!)\n    return function read_uint32() {\n        value = (         this.buf[this.pos] & 127       ) >>> 0; if (this.buf[this.pos++] < 128) return value;\n        value = (value | (this.buf[this.pos] & 127) <<  7) >>> 0; if (this.buf[this.pos++] < 128) return value;\n        value = (value | (this.buf[this.pos] & 127) << 14) >>> 0; if (this.buf[this.pos++] < 128) return value;\n        value = (value | (this.buf[this.pos] & 127) << 21) >>> 0; if (this.buf[this.pos++] < 128) return value;\n        value = (value | (this.buf[this.pos] &  15) << 28) >>> 0; if (this.buf[this.pos++] < 128) return value;\n\n        /* istanbul ignore if */\n        if ((this.pos += 5) > this.len) {\n            this.pos = this.len;\n            throw indexOutOfRange(this, 10);\n        }\n        return value;\n    };\n})();\n\n/**\n * Reads a varint as a signed 32 bit value.\n * @returns {number} Value read\n */\nReader.prototype.int32 = function read_int32() {\n    return this.uint32() | 0;\n};\n\n/**\n * Reads a zig-zag encoded varint as a signed 32 bit value.\n * @returns {number} Value read\n */\nReader.prototype.sint32 = function read_sint32() {\n    var value = this.uint32();\n    return value >>> 1 ^ -(value & 1) | 0;\n};\n\n/* eslint-disable no-invalid-this */\n\nfunction readLongVarint() {\n    // tends to deopt with local vars for octet etc.\n    var bits = new LongBits(0, 0);\n    var i = 0;\n    if (this.len - this.pos > 4) { // fast route (lo)\n        for (; i < 4; ++i) {\n            // 1st..4th\n            bits.lo = (bits.lo | (this.buf[this.pos] & 127) << i * 7) >>> 0;\n            if (this.buf[this.pos++] < 128)\n                return bits;\n        }\n        // 5th\n        bits.lo = (bits.lo | (this.buf[this.pos] & 127) << 28) >>> 0;\n        bits.hi = (bits.hi | (this.buf[this.pos] & 127) >>  4) >>> 0;\n        if (this.buf[this.pos++] < 128)\n            return bits;\n        i = 0;\n    } else {\n        for (; i < 3; ++i) {\n            /* istanbul ignore if */\n            if (this.pos >= this.len)\n                throw indexOutOfRange(this);\n            // 1st..3th\n            bits.lo = (bits.lo | (this.buf[this.pos] & 127) << i * 7) >>> 0;\n            if (this.buf[this.pos++] < 128)\n                return bits;\n        }\n        // 4th\n        bits.lo = (bits.lo | (this.buf[this.pos++] & 127) << i * 7) >>> 0;\n        return bits;\n    }\n    if (this.len - this.pos > 4) { // fast route (hi)\n        for (; i < 5; ++i) {\n            // 6th..10th\n            bits.hi = (bits.hi | (this.buf[this.pos] & 127) << i * 7 + 3) >>> 0;\n            if (this.buf[this.pos++] < 128)\n                return bits;\n        }\n    } else {\n        for (; i < 5; ++i) {\n            /* istanbul ignore if */\n            if (this.pos >= this.len)\n                throw indexOutOfRange(this);\n            // 6th..10th\n            bits.hi = (bits.hi | (this.buf[this.pos] & 127) << i * 7 + 3) >>> 0;\n            if (this.buf[this.pos++] < 128)\n                return bits;\n        }\n    }\n    /* istanbul ignore next */\n    throw Error(\"invalid varint encoding\");\n}\n\n/* eslint-enable no-invalid-this */\n\n/**\n * Reads a varint as a signed 64 bit value.\n * @name Reader#int64\n * @function\n * @returns {Long} Value read\n */\n\n/**\n * Reads a varint as an unsigned 64 bit value.\n * @name Reader#uint64\n * @function\n * @returns {Long} Value read\n */\n\n/**\n * Reads a zig-zag encoded varint as a signed 64 bit value.\n * @name Reader#sint64\n * @function\n * @returns {Long} Value read\n */\n\n/**\n * Reads a varint as a boolean.\n * @returns {boolean} Value read\n */\nReader.prototype.bool = function read_bool() {\n    return this.uint32() !== 0;\n};\n\nfunction readFixed32_end(buf, end) { // note that this uses `end`, not `pos`\n    return (buf[end - 4]\n          | buf[end - 3] << 8\n          | buf[end - 2] << 16\n          | buf[end - 1] << 24) >>> 0;\n}\n\n/**\n * Reads fixed 32 bits as an unsigned 32 bit integer.\n * @returns {number} Value read\n */\nReader.prototype.fixed32 = function read_fixed32() {\n\n    /* istanbul ignore if */\n    if (this.pos + 4 > this.len)\n        throw indexOutOfRange(this, 4);\n\n    return readFixed32_end(this.buf, this.pos += 4);\n};\n\n/**\n * Reads fixed 32 bits as a signed 32 bit integer.\n * @returns {number} Value read\n */\nReader.prototype.sfixed32 = function read_sfixed32() {\n\n    /* istanbul ignore if */\n    if (this.pos + 4 > this.len)\n        throw indexOutOfRange(this, 4);\n\n    return readFixed32_end(this.buf, this.pos += 4) | 0;\n};\n\n/* eslint-disable no-invalid-this */\n\nfunction readFixed64(/* this: Reader */) {\n\n    /* istanbul ignore if */\n    if (this.pos + 8 > this.len)\n        throw indexOutOfRange(this, 8);\n\n    return new LongBits(readFixed32_end(this.buf, this.pos += 4), readFixed32_end(this.buf, this.pos += 4));\n}\n\n/* eslint-enable no-invalid-this */\n\n/**\n * Reads fixed 64 bits.\n * @name Reader#fixed64\n * @function\n * @returns {Long} Value read\n */\n\n/**\n * Reads zig-zag encoded fixed 64 bits.\n * @name Reader#sfixed64\n * @function\n * @returns {Long} Value read\n */\n\n/**\n * Reads a float (32 bit) as a number.\n * @function\n * @returns {number} Value read\n */\nReader.prototype.float = function read_float() {\n\n    /* istanbul ignore if */\n    if (this.pos + 4 > this.len)\n        throw indexOutOfRange(this, 4);\n\n    var value = util.float.readFloatLE(this.buf, this.pos);\n    this.pos += 4;\n    return value;\n};\n\n/**\n * Reads a double (64 bit float) as a number.\n * @function\n * @returns {number} Value read\n */\nReader.prototype.double = function read_double() {\n\n    /* istanbul ignore if */\n    if (this.pos + 8 > this.len)\n        throw indexOutOfRange(this, 4);\n\n    var value = util.float.readDoubleLE(this.buf, this.pos);\n    this.pos += 8;\n    return value;\n};\n\n/**\n * Reads a sequence of bytes preceeded by its length as a varint.\n * @returns {Uint8Array} Value read\n */\nReader.prototype.bytes = function read_bytes() {\n    var length = this.uint32(),\n        start  = this.pos,\n        end    = this.pos + length;\n\n    /* istanbul ignore if */\n    if (end > this.len)\n        throw indexOutOfRange(this, length);\n\n    this.pos += length;\n    if (Array.isArray(this.buf)) // plain array\n        return this.buf.slice(start, end);\n    return start === end // fix for IE 10/Win8 and others' subarray returning array of size 1\n        ? new this.buf.constructor(0)\n        : this._slice.call(this.buf, start, end);\n};\n\n/**\n * Reads a string preceeded by its byte length as a varint.\n * @returns {string} Value read\n */\nReader.prototype.string = function read_string() {\n    var bytes = this.bytes();\n    return utf8.read(bytes, 0, bytes.length);\n};\n\n/**\n * Skips the specified number of bytes if specified, otherwise skips a varint.\n * @param {number} [length] Length if known, otherwise a varint is assumed\n * @returns {Reader} `this`\n */\nReader.prototype.skip = function skip(length) {\n    if (typeof length === \"number\") {\n        /* istanbul ignore if */\n        if (this.pos + length > this.len)\n            throw indexOutOfRange(this, length);\n        this.pos += length;\n    } else {\n        do {\n            /* istanbul ignore if */\n            if (this.pos >= this.len)\n                throw indexOutOfRange(this);\n        } while (this.buf[this.pos++] & 128);\n    }\n    return this;\n};\n\n/**\n * Skips the next element of the specified wire type.\n * @param {number} wireType Wire type received\n * @returns {Reader} `this`\n */\nReader.prototype.skipType = function(wireType) {\n    switch (wireType) {\n        case 0:\n            this.skip();\n            break;\n        case 1:\n            this.skip(8);\n            break;\n        case 2:\n            this.skip(this.uint32());\n            break;\n        case 3:\n            while ((wireType = this.uint32() & 7) !== 4) {\n                this.skipType(wireType);\n            }\n            break;\n        case 5:\n            this.skip(4);\n            break;\n\n        /* istanbul ignore next */\n        default:\n            throw Error(\"invalid wire type \" + wireType + \" at offset \" + this.pos);\n    }\n    return this;\n};\n\nReader._configure = function(BufferReader_) {\n    BufferReader = BufferReader_;\n    Reader.create = create();\n    BufferReader._configure();\n\n    var fn = util.Long ? \"toLong\" : /* istanbul ignore next */ \"toNumber\";\n    util.merge(Reader.prototype, {\n\n        int64: function read_int64() {\n            return readLongVarint.call(this)[fn](false);\n        },\n\n        uint64: function read_uint64() {\n            return readLongVarint.call(this)[fn](true);\n        },\n\n        sint64: function read_sint64() {\n            return readLongVarint.call(this).zzDecode()[fn](false);\n        },\n\n        fixed64: function read_fixed64() {\n            return readFixed64.call(this)[fn](true);\n        },\n\n        sfixed64: function read_sfixed64() {\n            return readFixed64.call(this)[fn](false);\n        }\n\n    });\n};\n", "\"use strict\";\nmodule.exports = B<PERSON>erReader;\n\n// extends Reader\nvar Reader = require(27);\n(BufferReader.prototype = Object.create(Reader.prototype)).constructor = BufferReader;\n\nvar util = require(39);\n\n/**\n * Constructs a new buffer reader instance.\n * @classdesc Wire format reader using node buffers.\n * @extends Reader\n * @constructor\n * @param {Buffer} buffer Buffer to read from\n */\nfunction BufferReader(buffer) {\n    Reader.call(this, buffer);\n\n    /**\n     * Read buffer.\n     * @name BufferReader#buf\n     * @type {Buffer}\n     */\n}\n\nBufferReader._configure = function () {\n    /* istanbul ignore else */\n    if (util.Buffer)\n        BufferReader.prototype._slice = util.Buffer.prototype.slice;\n};\n\n\n/**\n * @override\n */\nBufferReader.prototype.string = function read_string_buffer() {\n    var len = this.uint32(); // modifies pos\n    return this.buf.utf8Slice\n        ? this.buf.utf8Slice(this.pos, this.pos = Math.min(this.pos + len, this.len))\n        : this.buf.toString(\"utf-8\", this.pos, this.pos = Math.min(this.pos + len, this.len));\n};\n\n/**\n * Reads a sequence of bytes preceeded by its length as a varint.\n * @name BufferReader#bytes\n * @function\n * @returns {Buffer} Value read\n */\n\nBufferReader._configure();\n", "\"use strict\";\nmodule.exports = Root;\n\n// extends Namespace\nvar Namespace = require(23);\n((Root.prototype = Object.create(Namespace.prototype)).constructor = Root).className = \"Root\";\n\nvar Field   = require(16),\n    Enum    = require(15),\n    OneOf   = require(25),\n    util    = require(37);\n\nvar Type,   // cyclic\n    parse,  // might be excluded\n    common; // \"\n\n/**\n * Constructs a new root namespace instance.\n * @classdesc Root namespace wrapping all types, enums, services, sub-namespaces etc. that belong together.\n * @extends NamespaceBase\n * @constructor\n * @param {Object.<string,*>} [options] Top level options\n */\nfunction Root(options) {\n    Namespace.call(this, \"\", options);\n\n    /**\n     * Deferred extension fields.\n     * @type {Field[]}\n     */\n    this.deferred = [];\n\n    /**\n     * Resolved file names of loaded files.\n     * @type {string[]}\n     */\n    this.files = [];\n}\n\n/**\n * Loads a namespace descriptor into a root namespace.\n * @param {INamespace} json Nameespace descriptor\n * @param {Root} [root] Root namespace, defaults to create a new one if omitted\n * @returns {Root} Root namespace\n */\nRoot.fromJSON = function fromJSON(json, root) {\n    if (!root)\n        root = new Root();\n    if (json.options)\n        root.setOptions(json.options);\n    return root.addJSON(json.nested);\n};\n\n/**\n * Resolves the path of an imported file, relative to the importing origin.\n * This method exists so you can override it with your own logic in case your imports are scattered over multiple directories.\n * @function\n * @param {string} origin The file name of the importing file\n * @param {string} target The file name being imported\n * @returns {string|null} Resolved path to `target` or `null` to skip the file\n */\nRoot.prototype.resolvePath = util.path.resolve;\n\n/**\n * Fetch content from file path or url\n * This method exists so you can override it with your own logic.\n * @function\n * @param {string} path File path or url\n * @param {FetchCallback} callback Callback function\n * @returns {undefined}\n */\nRoot.prototype.fetch = util.fetch;\n\n// A symbol-like function to safely signal synchronous loading\n/* istanbul ignore next */\nfunction SYNC() {} // eslint-disable-line no-empty-function\n\n/**\n * Loads one or multiple .proto or preprocessed .json files into this root namespace and calls the callback.\n * @param {string|string[]} filename Names of one or multiple files to load\n * @param {IParseOptions} options Parse options\n * @param {LoadCallback} callback Callback function\n * @returns {undefined}\n */\nRoot.prototype.load = function load(filename, options, callback) {\n    if (typeof options === \"function\") {\n        callback = options;\n        options = undefined;\n    }\n    var self = this;\n    if (!callback)\n        return util.asPromise(load, self, filename, options);\n\n    var sync = callback === SYNC; // undocumented\n\n    // Finishes loading by calling the callback (exactly once)\n    function finish(err, root) {\n        /* istanbul ignore if */\n        if (!callback)\n            return;\n        var cb = callback;\n        callback = null;\n        if (sync)\n            throw err;\n        cb(err, root);\n    }\n\n    // Bundled definition existence checking\n    function getBundledFileName(filename) {\n        var idx = filename.lastIndexOf(\"google/protobuf/\");\n        if (idx > -1) {\n            var altname = filename.substring(idx);\n            if (altname in common) return altname;\n        }\n        return null;\n    }\n\n    // Processes a single file\n    function process(filename, source) {\n        try {\n            if (util.isString(source) && source.charAt(0) === \"{\")\n                source = JSON.parse(source);\n            if (!util.isString(source))\n                self.setOptions(source.options).addJSON(source.nested);\n            else {\n                parse.filename = filename;\n                var parsed = parse(source, self, options),\n                    resolved,\n                    i = 0;\n                if (parsed.imports)\n                    for (; i < parsed.imports.length; ++i)\n                        if (resolved = getBundledFileName(parsed.imports[i]) || self.resolvePath(filename, parsed.imports[i]))\n                            fetch(resolved);\n                if (parsed.weakImports)\n                    for (i = 0; i < parsed.weakImports.length; ++i)\n                        if (resolved = getBundledFileName(parsed.weakImports[i]) || self.resolvePath(filename, parsed.weakImports[i]))\n                            fetch(resolved, true);\n            }\n        } catch (err) {\n            finish(err);\n        }\n        if (!sync && !queued)\n            finish(null, self); // only once anyway\n    }\n\n    // Fetches a single file\n    function fetch(filename, weak) {\n\n        // Skip if already loaded / attempted\n        if (self.files.indexOf(filename) > -1)\n            return;\n        self.files.push(filename);\n\n        // Shortcut bundled definitions\n        if (filename in common) {\n            if (sync)\n                process(filename, common[filename]);\n            else {\n                ++queued;\n                setTimeout(function() {\n                    --queued;\n                    process(filename, common[filename]);\n                });\n            }\n            return;\n        }\n\n        // Otherwise fetch from disk or network\n        if (sync) {\n            var source;\n            try {\n                source = util.fs.readFileSync(filename).toString(\"utf8\");\n            } catch (err) {\n                if (!weak)\n                    finish(err);\n                return;\n            }\n            process(filename, source);\n        } else {\n            ++queued;\n            self.fetch(filename, function(err, source) {\n                --queued;\n                /* istanbul ignore if */\n                if (!callback)\n                    return; // terminated meanwhile\n                if (err) {\n                    /* istanbul ignore else */\n                    if (!weak)\n                        finish(err);\n                    else if (!queued) // can't be covered reliably\n                        finish(null, self);\n                    return;\n                }\n                process(filename, source);\n            });\n        }\n    }\n    var queued = 0;\n\n    // Assembling the root namespace doesn't require working type\n    // references anymore, so we can load everything in parallel\n    if (util.isString(filename))\n        filename = [ filename ];\n    for (var i = 0, resolved; i < filename.length; ++i)\n        if (resolved = self.resolvePath(\"\", filename[i]))\n            fetch(resolved);\n\n    if (sync)\n        return self;\n    if (!queued)\n        finish(null, self);\n    return undefined;\n};\n// function load(filename:string, options:IParseOptions, callback:LoadCallback):undefined\n\n/**\n * Loads one or multiple .proto or preprocessed .json files into this root namespace and calls the callback.\n * @function Root#load\n * @param {string|string[]} filename Names of one or multiple files to load\n * @param {LoadCallback} callback Callback function\n * @returns {undefined}\n * @variation 2\n */\n// function load(filename:string, callback:LoadCallback):undefined\n\n/**\n * Loads one or multiple .proto or preprocessed .json files into this root namespace and returns a promise.\n * @function Root#load\n * @param {string|string[]} filename Names of one or multiple files to load\n * @param {IParseOptions} [options] Parse options. Defaults to {@link parse.defaults} when omitted.\n * @returns {Promise<Root>} Promise\n * @variation 3\n */\n// function load(filename:string, [options:IParseOptions]):Promise<Root>\n\n/**\n * Synchronously loads one or multiple .proto or preprocessed .json files into this root namespace (node only).\n * @function Root#loadSync\n * @param {string|string[]} filename Names of one or multiple files to load\n * @param {IParseOptions} [options] Parse options. Defaults to {@link parse.defaults} when omitted.\n * @returns {Root} Root namespace\n * @throws {Error} If synchronous fetching is not supported (i.e. in browsers) or if a file's syntax is invalid\n */\nRoot.prototype.loadSync = function loadSync(filename, options) {\n    if (!util.isNode)\n        throw Error(\"not supported\");\n    return this.load(filename, options, SYNC);\n};\n\n/**\n * @override\n */\nRoot.prototype.resolveAll = function resolveAll() {\n    if (this.deferred.length)\n        throw Error(\"unresolvable extensions: \" + this.deferred.map(function(field) {\n            return \"'extend \" + field.extend + \"' in \" + field.parent.fullName;\n        }).join(\", \"));\n    return Namespace.prototype.resolveAll.call(this);\n};\n\n// only uppercased (and thus conflict-free) children are exposed, see below\nvar exposeRe = /^[A-Z]/;\n\n/**\n * Handles a deferred declaring extension field by creating a sister field to represent it within its extended type.\n * @param {Root} root Root instance\n * @param {Field} field Declaring extension field witin the declaring type\n * @returns {boolean} `true` if successfully added to the extended type, `false` otherwise\n * @inner\n * @ignore\n */\nfunction tryHandleExtension(root, field) {\n    var extendedType = field.parent.lookup(field.extend);\n    if (extendedType) {\n        var sisterField = new Field(field.fullName, field.id, field.type, field.rule, undefined, field.options);\n        sisterField.declaringField = field;\n        field.extensionField = sisterField;\n        extendedType.add(sisterField);\n        return true;\n    }\n    return false;\n}\n\n/**\n * Called when any object is added to this root or its sub-namespaces.\n * @param {ReflectionObject} object Object added\n * @returns {undefined}\n * @private\n */\nRoot.prototype._handleAdd = function _handleAdd(object) {\n    if (object instanceof Field) {\n\n        if (/* an extension field (implies not part of a oneof) */ object.extend !== undefined && /* not already handled */ !object.extensionField)\n            if (!tryHandleExtension(this, object))\n                this.deferred.push(object);\n\n    } else if (object instanceof Enum) {\n\n        if (exposeRe.test(object.name))\n            object.parent[object.name] = object.values; // expose enum values as property of its parent\n\n    } else if (!(object instanceof OneOf)) /* everything else is a namespace */ {\n\n        if (object instanceof Type) // Try to handle any deferred extensions\n            for (var i = 0; i < this.deferred.length;)\n                if (tryHandleExtension(this, this.deferred[i]))\n                    this.deferred.splice(i, 1);\n                else\n                    ++i;\n        for (var j = 0; j < /* initializes */ object.nestedArray.length; ++j) // recurse into the namespace\n            this._handleAdd(object._nestedArray[j]);\n        if (exposeRe.test(object.name))\n            object.parent[object.name] = object; // expose namespace as property of its parent\n    }\n\n    // The above also adds uppercased (and thus conflict-free) nested types, services and enums as\n    // properties of namespaces just like static code does. This allows using a .d.ts generated for\n    // a static module with reflection-based solutions where the condition is met.\n};\n\n/**\n * Called when any object is removed from this root or its sub-namespaces.\n * @param {ReflectionObject} object Object removed\n * @returns {undefined}\n * @private\n */\nRoot.prototype._handleRemove = function _handleRemove(object) {\n    if (object instanceof Field) {\n\n        if (/* an extension field */ object.extend !== undefined) {\n            if (/* already handled */ object.extensionField) { // remove its sister field\n                object.extensionField.parent.remove(object.extensionField);\n                object.extensionField = null;\n            } else { // cancel the extension\n                var index = this.deferred.indexOf(object);\n                /* istanbul ignore else */\n                if (index > -1)\n                    this.deferred.splice(index, 1);\n            }\n        }\n\n    } else if (object instanceof Enum) {\n\n        if (exposeRe.test(object.name))\n            delete object.parent[object.name]; // unexpose enum values\n\n    } else if (object instanceof Namespace) {\n\n        for (var i = 0; i < /* initializes */ object.nestedArray.length; ++i) // recurse into the namespace\n            this._handleRemove(object._nestedArray[i]);\n\n        if (exposeRe.test(object.name))\n            delete object.parent[object.name]; // unexpose namespaces\n\n    }\n};\n\n// Sets up cyclic dependencies (called in index-light)\nRoot._configure = function(Type_, parse_, common_) {\n    Type   = Type_;\n    parse  = parse_;\n    common = common_;\n};\n", "\"use strict\";\nmodule.exports = {};\n\n/**\n * Named roots.\n * This is where pbjs stores generated structures (the option `-r, --root` specifies a name).\n * Can also be used manually to make roots available across modules.\n * @name roots\n * @type {Object.<string,Root>}\n * @example\n * // pbjs -r myroot -o compiled.js ...\n *\n * // in another module:\n * require(\"./compiled.js\");\n *\n * // in any subsequent module:\n * var root = protobuf.roots[\"myroot\"];\n */\n", "\"use strict\";\n\n/**\n * Streaming RPC helpers.\n * @namespace\n */\nvar rpc = exports;\n\n/**\n * RPC implementation passed to {@link Service#create} performing a service request on network level, i.e. by utilizing http requests or websockets.\n * @typedef RPCImpl\n * @type {function}\n * @param {Method|rpc.ServiceMethod<Message<{}>,Message<{}>>} method Reflected or static method being called\n * @param {Uint8Array} requestData Request data\n * @param {RPCImplCallback} callback Callback function\n * @returns {undefined}\n * @example\n * function rpcImpl(method, requestData, callback) {\n *     if (protobuf.util.lcFirst(method.name) !== \"myMethod\") // compatible with static code\n *         throw Error(\"no such method\");\n *     asynchronouslyObtainAResponse(requestData, function(err, responseData) {\n *         callback(err, responseData);\n *     });\n * }\n */\n\n/**\n * Node-style callback as used by {@link RPCImpl}.\n * @typedef RPCImplCallback\n * @type {function}\n * @param {Error|null} error Error, if any, otherwise `null`\n * @param {Uint8Array|null} [response] Response data or `null` to signal end of stream, if there hasn't been an error\n * @returns {undefined}\n */\n\nrpc.Service = require(32);\n", "\"use strict\";\nmodule.exports = Service;\n\nvar util = require(39);\n\n// Extends EventEmitter\n(Service.prototype = Object.create(util.EventEmitter.prototype)).constructor = Service;\n\n/**\n * A service method callback as used by {@link rpc.ServiceMethod|ServiceMethod}.\n *\n * Differs from {@link RPCImplCallback} in that it is an actual callback of a service method which may not return `response = null`.\n * @typedef rpc.ServiceMethodCallback\n * @template TRes extends Message<TRes>\n * @type {function}\n * @param {Error|null} error Error, if any\n * @param {TRes} [response] Response message\n * @returns {undefined}\n */\n\n/**\n * A service method part of a {@link rpc.Service} as created by {@link Service.create}.\n * @typedef rpc.ServiceMethod\n * @template TReq extends Message<TReq>\n * @template TRes extends Message<TRes>\n * @type {function}\n * @param {TReq|Properties<TReq>} request Request message or plain object\n * @param {rpc.ServiceMethodCallback<TRes>} [callback] Node-style callback called with the error, if any, and the response message\n * @returns {Promise<Message<TRes>>} Promise if `callback` has been omitted, otherwise `undefined`\n */\n\n/**\n * Constructs a new RPC service instance.\n * @classdesc An RPC service as returned by {@link Service#create}.\n * @exports rpc.Service\n * @extends util.EventEmitter\n * @constructor\n * @param {RPCImpl} rpcImpl RPC implementation\n * @param {boolean} [requestDelimited=false] Whether requests are length-delimited\n * @param {boolean} [responseDelimited=false] Whether responses are length-delimited\n */\nfunction Service(rpcImpl, requestDelimited, responseDelimited) {\n\n    if (typeof rpcImpl !== \"function\")\n        throw TypeError(\"rpcImpl must be a function\");\n\n    util.EventEmitter.call(this);\n\n    /**\n     * RPC implementation. Becomes `null` once the service is ended.\n     * @type {RPCImpl|null}\n     */\n    this.rpcImpl = rpcImpl;\n\n    /**\n     * Whether requests are length-delimited.\n     * @type {boolean}\n     */\n    this.requestDelimited = Boolean(requestDelimited);\n\n    /**\n     * Whether responses are length-delimited.\n     * @type {boolean}\n     */\n    this.responseDelimited = Boolean(responseDelimited);\n}\n\n/**\n * Calls a service method through {@link rpc.Service#rpcImpl|rpcImpl}.\n * @param {Method|rpc.ServiceMethod<TReq,TRes>} method Reflected or static method\n * @param {Constructor<TReq>} requestCtor Request constructor\n * @param {Constructor<TRes>} responseCtor Response constructor\n * @param {TReq|Properties<TReq>} request Request message or plain object\n * @param {rpc.ServiceMethodCallback<TRes>} callback Service callback\n * @returns {undefined}\n * @template TReq extends Message<TReq>\n * @template TRes extends Message<TRes>\n */\nService.prototype.rpcCall = function rpcCall(method, requestCtor, responseCtor, request, callback) {\n\n    if (!request)\n        throw TypeError(\"request must be specified\");\n\n    var self = this;\n    if (!callback)\n        return util.asPromise(rpcCall, self, method, requestCtor, responseCtor, request);\n\n    if (!self.rpcImpl) {\n        setTimeout(function() { callback(Error(\"already ended\")); }, 0);\n        return undefined;\n    }\n\n    try {\n        return self.rpcImpl(\n            method,\n            requestCtor[self.requestDelimited ? \"encodeDelimited\" : \"encode\"](request).finish(),\n            function rpcCallback(err, response) {\n\n                if (err) {\n                    self.emit(\"error\", err, method);\n                    return callback(err);\n                }\n\n                if (response === null) {\n                    self.end(/* endedByRPC */ true);\n                    return undefined;\n                }\n\n                if (!(response instanceof responseCtor)) {\n                    try {\n                        response = responseCtor[self.responseDelimited ? \"decodeDelimited\" : \"decode\"](response);\n                    } catch (err) {\n                        self.emit(\"error\", err, method);\n                        return callback(err);\n                    }\n                }\n\n                self.emit(\"data\", response, method);\n                return callback(null, response);\n            }\n        );\n    } catch (err) {\n        self.emit(\"error\", err, method);\n        setTimeout(function() { callback(err); }, 0);\n        return undefined;\n    }\n};\n\n/**\n * Ends this service and emits the `end` event.\n * @param {boolean} [endedByRPC=false] Whether the service has been ended by the RPC implementation.\n * @returns {rpc.Service} `this`\n */\nService.prototype.end = function end(endedByRPC) {\n    if (this.rpcImpl) {\n        if (!endedByRPC) // signal end to rpcImpl\n            this.rpcImpl(null, null, null);\n        this.rpcImpl = null;\n        this.emit(\"end\").off();\n    }\n    return this;\n};\n", "\"use strict\";\nmodule.exports = Service;\n\n// extends Namespace\nvar Namespace = require(23);\n((Service.prototype = Object.create(Namespace.prototype)).constructor = Service).className = \"Service\";\n\nvar Method = require(22),\n    util   = require(37),\n    rpc    = require(31);\n\n/**\n * Constructs a new service instance.\n * @classdesc Reflected service.\n * @extends NamespaceBase\n * @constructor\n * @param {string} name Service name\n * @param {Object.<string,*>} [options] Service options\n * @throws {TypeError} If arguments are invalid\n */\nfunction Service(name, options) {\n    Namespace.call(this, name, options);\n\n    /**\n     * Service methods.\n     * @type {Object.<string,Method>}\n     */\n    this.methods = {}; // toJSON, marker\n\n    /**\n     * Cached methods as an array.\n     * @type {Method[]|null}\n     * @private\n     */\n    this._methodsArray = null;\n}\n\n/**\n * Service descriptor.\n * @interface IService\n * @extends INamespace\n * @property {Object.<string,IMethod>} methods Method descriptors\n */\n\n/**\n * Constructs a service from a service descriptor.\n * @param {string} name Service name\n * @param {IService} json Service descriptor\n * @returns {Service} Created service\n * @throws {TypeError} If arguments are invalid\n */\nService.fromJSON = function fromJSON(name, json) {\n    var service = new Service(name, json.options);\n    /* istanbul ignore else */\n    if (json.methods)\n        for (var names = Object.keys(json.methods), i = 0; i < names.length; ++i)\n            service.add(Method.fromJSON(names[i], json.methods[names[i]]));\n    if (json.nested)\n        service.addJSON(json.nested);\n    service.comment = json.comment;\n    return service;\n};\n\n/**\n * Converts this service to a service descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IService} Service descriptor\n */\nService.prototype.toJSON = function toJSON(toJSONOptions) {\n    var inherited = Namespace.prototype.toJSON.call(this, toJSONOptions);\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"options\" , inherited && inherited.options || undefined,\n        \"methods\" , Namespace.arrayToJSON(this.methodsArray, toJSONOptions) || /* istanbul ignore next */ {},\n        \"nested\"  , inherited && inherited.nested || undefined,\n        \"comment\" , keepComments ? this.comment : undefined\n    ]);\n};\n\n/**\n * Methods of this service as an array for iteration.\n * @name Service#methodsArray\n * @type {Method[]}\n * @readonly\n */\nObject.defineProperty(Service.prototype, \"methodsArray\", {\n    get: function() {\n        return this._methodsArray || (this._methodsArray = util.toArray(this.methods));\n    }\n});\n\nfunction clearCache(service) {\n    service._methodsArray = null;\n    return service;\n}\n\n/**\n * @override\n */\nService.prototype.get = function get(name) {\n    return this.methods[name]\n        || Namespace.prototype.get.call(this, name);\n};\n\n/**\n * @override\n */\nService.prototype.resolveAll = function resolveAll() {\n    var methods = this.methodsArray;\n    for (var i = 0; i < methods.length; ++i)\n        methods[i].resolve();\n    return Namespace.prototype.resolve.call(this);\n};\n\n/**\n * @override\n */\nService.prototype.add = function add(object) {\n\n    /* istanbul ignore if */\n    if (this.get(object.name))\n        throw Error(\"duplicate name '\" + object.name + \"' in \" + this);\n\n    if (object instanceof Method) {\n        this.methods[object.name] = object;\n        object.parent = this;\n        return clearCache(this);\n    }\n    return Namespace.prototype.add.call(this, object);\n};\n\n/**\n * @override\n */\nService.prototype.remove = function remove(object) {\n    if (object instanceof Method) {\n\n        /* istanbul ignore if */\n        if (this.methods[object.name] !== object)\n            throw Error(object + \" is not a member of \" + this);\n\n        delete this.methods[object.name];\n        object.parent = null;\n        return clearCache(this);\n    }\n    return Namespace.prototype.remove.call(this, object);\n};\n\n/**\n * Creates a runtime service using the specified rpc implementation.\n * @param {RPCImpl} rpcImpl RPC implementation\n * @param {boolean} [requestDelimited=false] Whether requests are length-delimited\n * @param {boolean} [responseDelimited=false] Whether responses are length-delimited\n * @returns {rpc.Service} RPC service. Useful where requests and/or responses are streamed.\n */\nService.prototype.create = function create(rpcImpl, requestDelimited, responseDelimited) {\n    var rpcService = new rpc.Service(rpcImpl, requestDelimited, responseDelimited);\n    for (var i = 0, method; i < /* initializes */ this.methodsArray.length; ++i) {\n        var methodName = util.lcFirst((method = this._methodsArray[i]).resolve().name).replace(/[^$\\w_]/g, \"\");\n        rpcService[methodName] = util.codegen([\"r\",\"c\"], util.isReserved(methodName) ? methodName + \"_\" : methodName)(\"return this.rpcCall(m,q,s,r,c)\")({\n            m: method,\n            q: method.resolvedRequestType.ctor,\n            s: method.resolvedResponseType.ctor\n        });\n    }\n    return rpcService;\n};\n", "\"use strict\";\nmodule.exports = tokenize;\n\nvar delimRe        = /[\\s{}=;:[\\],'\"()<>]/g,\n    stringDoubleRe = /(?:\"([^\"\\\\]*(?:\\\\.[^\"\\\\]*)*)\")/g,\n    stringSingleRe = /(?:'([^'\\\\]*(?:\\\\.[^'\\\\]*)*)')/g;\n\nvar setCommentRe = /^ *[*/]+ */,\n    setCommentAltRe = /^\\s*\\*?\\/*/,\n    setCommentSplitRe = /\\n/g,\n    whitespaceRe = /\\s/,\n    unescapeRe = /\\\\(.?)/g;\n\nvar unescapeMap = {\n    \"0\": \"\\0\",\n    \"r\": \"\\r\",\n    \"n\": \"\\n\",\n    \"t\": \"\\t\"\n};\n\n/**\n * Unescapes a string.\n * @param {string} str String to unescape\n * @returns {string} Unescaped string\n * @property {Object.<string,string>} map Special characters map\n * @memberof tokenize\n */\nfunction unescape(str) {\n    return str.replace(unescapeRe, function($0, $1) {\n        switch ($1) {\n            case \"\\\\\":\n            case \"\":\n                return $1;\n            default:\n                return unescapeMap[$1] || \"\";\n        }\n    });\n}\n\ntokenize.unescape = unescape;\n\n/**\n * Gets the next token and advances.\n * @typedef TokenizerHandleNext\n * @type {function}\n * @returns {string|null} Next token or `null` on eof\n */\n\n/**\n * Peeks for the next token.\n * @typedef TokenizerHandlePeek\n * @type {function}\n * @returns {string|null} Next token or `null` on eof\n */\n\n/**\n * Pushes a token back to the stack.\n * @typedef TokenizerHandlePush\n * @type {function}\n * @param {string} token Token\n * @returns {undefined}\n */\n\n/**\n * Skips the next token.\n * @typedef TokenizerHandleSkip\n * @type {function}\n * @param {string} expected Expected token\n * @param {boolean} [optional=false] If optional\n * @returns {boolean} Whether the token matched\n * @throws {Error} If the token didn't match and is not optional\n */\n\n/**\n * Gets the comment on the previous line or, alternatively, the line comment on the specified line.\n * @typedef TokenizerHandleCmnt\n * @type {function}\n * @param {number} [line] Line number\n * @returns {string|null} Comment text or `null` if none\n */\n\n/**\n * Handle object returned from {@link tokenize}.\n * @interface ITokenizerHandle\n * @property {TokenizerHandleNext} next Gets the next token and advances (`null` on eof)\n * @property {TokenizerHandlePeek} peek Peeks for the next token (`null` on eof)\n * @property {TokenizerHandlePush} push Pushes a token back to the stack\n * @property {TokenizerHandleSkip} skip Skips a token, returns its presence and advances or, if non-optional and not present, throws\n * @property {TokenizerHandleCmnt} cmnt Gets the comment on the previous line or the line comment on the specified line, if any\n * @property {number} line Current line number\n */\n\n/**\n * Tokenizes the given .proto source and returns an object with useful utility functions.\n * @param {string} source Source contents\n * @param {boolean} alternateCommentMode Whether we should activate alternate comment parsing mode.\n * @returns {ITokenizerHandle} Tokenizer handle\n */\nfunction tokenize(source, alternateCommentMode) {\n    /* eslint-disable callback-return */\n    source = source.toString();\n\n    var offset = 0,\n        length = source.length,\n        line = 1,\n        lastCommentLine = 0,\n        comments = {};\n\n    var stack = [];\n\n    var stringDelim = null;\n\n    /* istanbul ignore next */\n    /**\n     * Creates an error for illegal syntax.\n     * @param {string} subject Subject\n     * @returns {Error} Error created\n     * @inner\n     */\n    function illegal(subject) {\n        return Error(\"illegal \" + subject + \" (line \" + line + \")\");\n    }\n\n    /**\n     * Reads a string till its end.\n     * @returns {string} String read\n     * @inner\n     */\n    function readString() {\n        var re = stringDelim === \"'\" ? stringSingleRe : stringDoubleRe;\n        re.lastIndex = offset - 1;\n        var match = re.exec(source);\n        if (!match)\n            throw illegal(\"string\");\n        offset = re.lastIndex;\n        push(stringDelim);\n        stringDelim = null;\n        return unescape(match[1]);\n    }\n\n    /**\n     * Gets the character at `pos` within the source.\n     * @param {number} pos Position\n     * @returns {string} Character\n     * @inner\n     */\n    function charAt(pos) {\n        return source.charAt(pos);\n    }\n\n    /**\n     * Sets the current comment text.\n     * @param {number} start Start offset\n     * @param {number} end End offset\n     * @param {boolean} isLeading set if a leading comment\n     * @returns {undefined}\n     * @inner\n     */\n    function setComment(start, end, isLeading) {\n        var comment = {\n            type: source.charAt(start++),\n            lineEmpty: false,\n            leading: isLeading,\n        };\n        var lookback;\n        if (alternateCommentMode) {\n            lookback = 2;  // alternate comment parsing: \"//\" or \"/*\"\n        } else {\n            lookback = 3;  // \"///\" or \"/**\"\n        }\n        var commentOffset = start - lookback,\n            c;\n        do {\n            if (--commentOffset < 0 ||\n                    (c = source.charAt(commentOffset)) === \"\\n\") {\n                comment.lineEmpty = true;\n                break;\n            }\n        } while (c === \" \" || c === \"\\t\");\n        var lines = source\n            .substring(start, end)\n            .split(setCommentSplitRe);\n        for (var i = 0; i < lines.length; ++i)\n            lines[i] = lines[i]\n                .replace(alternateCommentMode ? setCommentAltRe : setCommentRe, \"\")\n                .trim();\n        comment.text = lines\n            .join(\"\\n\")\n            .trim();\n\n        comments[line] = comment;\n        lastCommentLine = line;\n    }\n\n    function isDoubleSlashCommentLine(startOffset) {\n        var endOffset = findEndOfLine(startOffset);\n\n        // see if remaining line matches comment pattern\n        var lineText = source.substring(startOffset, endOffset);\n        // look for 1 or 2 slashes since startOffset would already point past\n        // the first slash that started the comment.\n        var isComment = /^\\s*\\/{1,2}/.test(lineText);\n        return isComment;\n    }\n\n    function findEndOfLine(cursor) {\n        // find end of cursor's line\n        var endOffset = cursor;\n        while (endOffset < length && charAt(endOffset) !== \"\\n\") {\n            endOffset++;\n        }\n        return endOffset;\n    }\n\n    /**\n     * Obtains the next token.\n     * @returns {string|null} Next token or `null` on eof\n     * @inner\n     */\n    function next() {\n        if (stack.length > 0)\n            return stack.shift();\n        if (stringDelim)\n            return readString();\n        var repeat,\n            prev,\n            curr,\n            start,\n            isDoc,\n            isLeadingComment = offset === 0;\n        do {\n            if (offset === length)\n                return null;\n            repeat = false;\n            while (whitespaceRe.test(curr = charAt(offset))) {\n                if (curr === \"\\n\") {\n                    isLeadingComment = true;\n                    ++line;\n                }\n                if (++offset === length)\n                    return null;\n            }\n\n            if (charAt(offset) === \"/\") {\n                if (++offset === length) {\n                    throw illegal(\"comment\");\n                }\n                if (charAt(offset) === \"/\") { // Line\n                    if (!alternateCommentMode) {\n                        // check for triple-slash comment\n                        isDoc = charAt(start = offset + 1) === \"/\";\n\n                        while (charAt(++offset) !== \"\\n\") {\n                            if (offset === length) {\n                                return null;\n                            }\n                        }\n                        ++offset;\n                        if (isDoc) {\n                            setComment(start, offset - 1, isLeadingComment);\n                            // Trailing comment cannot not be multi-line,\n                            // so leading comment state should be reset to handle potential next comments\n                            isLeadingComment = true;\n                        }\n                        ++line;\n                        repeat = true;\n                    } else {\n                        // check for double-slash comments, consolidating consecutive lines\n                        start = offset;\n                        isDoc = false;\n                        if (isDoubleSlashCommentLine(offset)) {\n                            isDoc = true;\n                            do {\n                                offset = findEndOfLine(offset);\n                                if (offset === length) {\n                                    break;\n                                }\n                                offset++;\n                                if (!isLeadingComment) {\n                                    // Trailing comment cannot not be multi-line\n                                    break;\n                                }\n                            } while (isDoubleSlashCommentLine(offset));\n                        } else {\n                            offset = Math.min(length, findEndOfLine(offset) + 1);\n                        }\n                        if (isDoc) {\n                            setComment(start, offset, isLeadingComment);\n                            isLeadingComment = true;\n                        }\n                        line++;\n                        repeat = true;\n                    }\n                } else if ((curr = charAt(offset)) === \"*\") { /* Block */\n                    // check for /** (regular comment mode) or /* (alternate comment mode)\n                    start = offset + 1;\n                    isDoc = alternateCommentMode || charAt(start) === \"*\";\n                    do {\n                        if (curr === \"\\n\") {\n                            ++line;\n                        }\n                        if (++offset === length) {\n                            throw illegal(\"comment\");\n                        }\n                        prev = curr;\n                        curr = charAt(offset);\n                    } while (prev !== \"*\" || curr !== \"/\");\n                    ++offset;\n                    if (isDoc) {\n                        setComment(start, offset - 2, isLeadingComment);\n                        isLeadingComment = true;\n                    }\n                    repeat = true;\n                } else {\n                    return \"/\";\n                }\n            }\n        } while (repeat);\n\n        // offset !== length if we got here\n\n        var end = offset;\n        delimRe.lastIndex = 0;\n        var delim = delimRe.test(charAt(end++));\n        if (!delim)\n            while (end < length && !delimRe.test(charAt(end)))\n                ++end;\n        var token = source.substring(offset, offset = end);\n        if (token === \"\\\"\" || token === \"'\")\n            stringDelim = token;\n        return token;\n    }\n\n    /**\n     * Pushes a token back to the stack.\n     * @param {string} token Token\n     * @returns {undefined}\n     * @inner\n     */\n    function push(token) {\n        stack.push(token);\n    }\n\n    /**\n     * Peeks for the next token.\n     * @returns {string|null} Token or `null` on eof\n     * @inner\n     */\n    function peek() {\n        if (!stack.length) {\n            var token = next();\n            if (token === null)\n                return null;\n            push(token);\n        }\n        return stack[0];\n    }\n\n    /**\n     * Skips a token.\n     * @param {string} expected Expected token\n     * @param {boolean} [optional=false] Whether the token is optional\n     * @returns {boolean} `true` when skipped, `false` if not\n     * @throws {Error} When a required token is not present\n     * @inner\n     */\n    function skip(expected, optional) {\n        var actual = peek(),\n            equals = actual === expected;\n        if (equals) {\n            next();\n            return true;\n        }\n        if (!optional)\n            throw illegal(\"token '\" + actual + \"', '\" + expected + \"' expected\");\n        return false;\n    }\n\n    /**\n     * Gets a comment.\n     * @param {number} [trailingLine] Line number if looking for a trailing comment\n     * @returns {string|null} Comment text\n     * @inner\n     */\n    function cmnt(trailingLine) {\n        var ret = null;\n        var comment;\n        if (trailingLine === undefined) {\n            comment = comments[line - 1];\n            delete comments[line - 1];\n            if (comment && (alternateCommentMode || comment.type === \"*\" || comment.lineEmpty)) {\n                ret = comment.leading ? comment.text : null;\n            }\n        } else {\n            /* istanbul ignore else */\n            if (lastCommentLine < trailingLine) {\n                peek();\n            }\n            comment = comments[trailingLine];\n            delete comments[trailingLine];\n            if (comment && !comment.lineEmpty && (alternateCommentMode || comment.type === \"/\")) {\n                ret = comment.leading ? null : comment.text;\n            }\n        }\n        return ret;\n    }\n\n    return Object.defineProperty({\n        next: next,\n        peek: peek,\n        push: push,\n        skip: skip,\n        cmnt: cmnt\n    }, \"line\", {\n        get: function() { return line; }\n    });\n    /* eslint-enable callback-return */\n}\n", "\"use strict\";\nmodule.exports = Type;\n\n// extends Namespace\nvar Namespace = require(23);\n((Type.prototype = Object.create(Namespace.prototype)).constructor = Type).className = \"Type\";\n\nvar Enum      = require(15),\n    OneOf     = require(25),\n    Field     = require(16),\n    MapField  = require(20),\n    Service   = require(33),\n    Message   = require(21),\n    Reader    = require(27),\n    Writer    = require(42),\n    util      = require(37),\n    encoder   = require(14),\n    decoder   = require(13),\n    verifier  = require(40),\n    converter = require(12),\n    wrappers  = require(41);\n\n/**\n * Constructs a new reflected message type instance.\n * @classdesc Reflected message type.\n * @extends NamespaceBase\n * @constructor\n * @param {string} name Message name\n * @param {Object.<string,*>} [options] Declared options\n */\nfunction Type(name, options) {\n    Namespace.call(this, name, options);\n\n    /**\n     * Message fields.\n     * @type {Object.<string,Field>}\n     */\n    this.fields = {};  // toJSON, marker\n\n    /**\n     * Oneofs declared within this namespace, if any.\n     * @type {Object.<string,OneOf>}\n     */\n    this.oneofs = undefined; // toJSON\n\n    /**\n     * Extension ranges, if any.\n     * @type {number[][]}\n     */\n    this.extensions = undefined; // toJSON\n\n    /**\n     * Reserved ranges, if any.\n     * @type {Array.<number[]|string>}\n     */\n    this.reserved = undefined; // toJSON\n\n    /*?\n     * Whether this type is a legacy group.\n     * @type {boolean|undefined}\n     */\n    this.group = undefined; // toJSON\n\n    /**\n     * Cached fields by id.\n     * @type {Object.<number,Field>|null}\n     * @private\n     */\n    this._fieldsById = null;\n\n    /**\n     * Cached fields as an array.\n     * @type {Field[]|null}\n     * @private\n     */\n    this._fieldsArray = null;\n\n    /**\n     * Cached oneofs as an array.\n     * @type {OneOf[]|null}\n     * @private\n     */\n    this._oneofsArray = null;\n\n    /**\n     * Cached constructor.\n     * @type {Constructor<{}>}\n     * @private\n     */\n    this._ctor = null;\n}\n\nObject.defineProperties(Type.prototype, {\n\n    /**\n     * Message fields by id.\n     * @name Type#fieldsById\n     * @type {Object.<number,Field>}\n     * @readonly\n     */\n    fieldsById: {\n        get: function() {\n\n            /* istanbul ignore if */\n            if (this._fieldsById)\n                return this._fieldsById;\n\n            this._fieldsById = {};\n            for (var names = Object.keys(this.fields), i = 0; i < names.length; ++i) {\n                var field = this.fields[names[i]],\n                    id = field.id;\n\n                /* istanbul ignore if */\n                if (this._fieldsById[id])\n                    throw Error(\"duplicate id \" + id + \" in \" + this);\n\n                this._fieldsById[id] = field;\n            }\n            return this._fieldsById;\n        }\n    },\n\n    /**\n     * Fields of this message as an array for iteration.\n     * @name Type#fieldsArray\n     * @type {Field[]}\n     * @readonly\n     */\n    fieldsArray: {\n        get: function() {\n            return this._fieldsArray || (this._fieldsArray = util.toArray(this.fields));\n        }\n    },\n\n    /**\n     * Oneofs of this message as an array for iteration.\n     * @name Type#oneofsArray\n     * @type {OneOf[]}\n     * @readonly\n     */\n    oneofsArray: {\n        get: function() {\n            return this._oneofsArray || (this._oneofsArray = util.toArray(this.oneofs));\n        }\n    },\n\n    /**\n     * The registered constructor, if any registered, otherwise a generic constructor.\n     * Assigning a function replaces the internal constructor. If the function does not extend {@link Message} yet, its prototype will be setup accordingly and static methods will be populated. If it already extends {@link Message}, it will just replace the internal constructor.\n     * @name Type#ctor\n     * @type {Constructor<{}>}\n     */\n    ctor: {\n        get: function() {\n            return this._ctor || (this.ctor = Type.generateConstructor(this)());\n        },\n        set: function(ctor) {\n\n            // Ensure proper prototype\n            var prototype = ctor.prototype;\n            if (!(prototype instanceof Message)) {\n                (ctor.prototype = new Message()).constructor = ctor;\n                util.merge(ctor.prototype, prototype);\n            }\n\n            // Classes and messages reference their reflected type\n            ctor.$type = ctor.prototype.$type = this;\n\n            // Mix in static methods\n            util.merge(ctor, Message, true);\n\n            this._ctor = ctor;\n\n            // Messages have non-enumerable default values on their prototype\n            var i = 0;\n            for (; i < /* initializes */ this.fieldsArray.length; ++i)\n                this._fieldsArray[i].resolve(); // ensures a proper value\n\n            // Messages have non-enumerable getters and setters for each virtual oneof field\n            var ctorProperties = {};\n            for (i = 0; i < /* initializes */ this.oneofsArray.length; ++i)\n                ctorProperties[this._oneofsArray[i].resolve().name] = {\n                    get: util.oneOfGetter(this._oneofsArray[i].oneof),\n                    set: util.oneOfSetter(this._oneofsArray[i].oneof)\n                };\n            if (i)\n                Object.defineProperties(ctor.prototype, ctorProperties);\n        }\n    }\n});\n\n/**\n * Generates a constructor function for the specified type.\n * @param {Type} mtype Message type\n * @returns {Codegen} Codegen instance\n */\nType.generateConstructor = function generateConstructor(mtype) {\n    /* eslint-disable no-unexpected-multiline */\n    var gen = util.codegen([\"p\"], mtype.name);\n    // explicitly initialize mutable object/array fields so that these aren't just inherited from the prototype\n    for (var i = 0, field; i < mtype.fieldsArray.length; ++i)\n        if ((field = mtype._fieldsArray[i]).map) gen\n            (\"this%s={}\", util.safeProp(field.name));\n        else if (field.repeated) gen\n            (\"this%s=[]\", util.safeProp(field.name));\n    return gen\n    (\"if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)\") // omit undefined or null\n        (\"this[ks[i]]=p[ks[i]]\");\n    /* eslint-enable no-unexpected-multiline */\n};\n\nfunction clearCache(type) {\n    type._fieldsById = type._fieldsArray = type._oneofsArray = null;\n    delete type.encode;\n    delete type.decode;\n    delete type.verify;\n    return type;\n}\n\n/**\n * Message type descriptor.\n * @interface IType\n * @extends INamespace\n * @property {Object.<string,IOneOf>} [oneofs] Oneof descriptors\n * @property {Object.<string,IField>} fields Field descriptors\n * @property {number[][]} [extensions] Extension ranges\n * @property {number[][]} [reserved] Reserved ranges\n * @property {boolean} [group=false] Whether a legacy group or not\n */\n\n/**\n * Creates a message type from a message type descriptor.\n * @param {string} name Message name\n * @param {IType} json Message type descriptor\n * @returns {Type} Created message type\n */\nType.fromJSON = function fromJSON(name, json) {\n    var type = new Type(name, json.options);\n    type.extensions = json.extensions;\n    type.reserved = json.reserved;\n    var names = Object.keys(json.fields),\n        i = 0;\n    for (; i < names.length; ++i)\n        type.add(\n            ( typeof json.fields[names[i]].keyType !== \"undefined\"\n            ? MapField.fromJSON\n            : Field.fromJSON )(names[i], json.fields[names[i]])\n        );\n    if (json.oneofs)\n        for (names = Object.keys(json.oneofs), i = 0; i < names.length; ++i)\n            type.add(OneOf.fromJSON(names[i], json.oneofs[names[i]]));\n    if (json.nested)\n        for (names = Object.keys(json.nested), i = 0; i < names.length; ++i) {\n            var nested = json.nested[names[i]];\n            type.add( // most to least likely\n                ( nested.id !== undefined\n                ? Field.fromJSON\n                : nested.fields !== undefined\n                ? Type.fromJSON\n                : nested.values !== undefined\n                ? Enum.fromJSON\n                : nested.methods !== undefined\n                ? Service.fromJSON\n                : Namespace.fromJSON )(names[i], nested)\n            );\n        }\n    if (json.extensions && json.extensions.length)\n        type.extensions = json.extensions;\n    if (json.reserved && json.reserved.length)\n        type.reserved = json.reserved;\n    if (json.group)\n        type.group = true;\n    if (json.comment)\n        type.comment = json.comment;\n    return type;\n};\n\n/**\n * Converts this message type to a message type descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IType} Message type descriptor\n */\nType.prototype.toJSON = function toJSON(toJSONOptions) {\n    var inherited = Namespace.prototype.toJSON.call(this, toJSONOptions);\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"options\"    , inherited && inherited.options || undefined,\n        \"oneofs\"     , Namespace.arrayToJSON(this.oneofsArray, toJSONOptions),\n        \"fields\"     , Namespace.arrayToJSON(this.fieldsArray.filter(function(obj) { return !obj.declaringField; }), toJSONOptions) || {},\n        \"extensions\" , this.extensions && this.extensions.length ? this.extensions : undefined,\n        \"reserved\"   , this.reserved && this.reserved.length ? this.reserved : undefined,\n        \"group\"      , this.group || undefined,\n        \"nested\"     , inherited && inherited.nested || undefined,\n        \"comment\"    , keepComments ? this.comment : undefined\n    ]);\n};\n\n/**\n * @override\n */\nType.prototype.resolveAll = function resolveAll() {\n    var fields = this.fieldsArray, i = 0;\n    while (i < fields.length)\n        fields[i++].resolve();\n    var oneofs = this.oneofsArray; i = 0;\n    while (i < oneofs.length)\n        oneofs[i++].resolve();\n    return Namespace.prototype.resolveAll.call(this);\n};\n\n/**\n * @override\n */\nType.prototype.get = function get(name) {\n    return this.fields[name]\n        || this.oneofs && this.oneofs[name]\n        || this.nested && this.nested[name]\n        || null;\n};\n\n/**\n * Adds a nested object to this type.\n * @param {ReflectionObject} object Nested object to add\n * @returns {Type} `this`\n * @throws {TypeError} If arguments are invalid\n * @throws {Error} If there is already a nested object with this name or, if a field, when there is already a field with this id\n */\nType.prototype.add = function add(object) {\n\n    if (this.get(object.name))\n        throw Error(\"duplicate name '\" + object.name + \"' in \" + this);\n\n    if (object instanceof Field && object.extend === undefined) {\n        // NOTE: Extension fields aren't actual fields on the declaring type, but nested objects.\n        // The root object takes care of adding distinct sister-fields to the respective extended\n        // type instead.\n\n        // avoids calling the getter if not absolutely necessary because it's called quite frequently\n        if (this._fieldsById ? /* istanbul ignore next */ this._fieldsById[object.id] : this.fieldsById[object.id])\n            throw Error(\"duplicate id \" + object.id + \" in \" + this);\n        if (this.isReservedId(object.id))\n            throw Error(\"id \" + object.id + \" is reserved in \" + this);\n        if (this.isReservedName(object.name))\n            throw Error(\"name '\" + object.name + \"' is reserved in \" + this);\n\n        if (object.parent)\n            object.parent.remove(object);\n        this.fields[object.name] = object;\n        object.message = this;\n        object.onAdd(this);\n        return clearCache(this);\n    }\n    if (object instanceof OneOf) {\n        if (!this.oneofs)\n            this.oneofs = {};\n        this.oneofs[object.name] = object;\n        object.onAdd(this);\n        return clearCache(this);\n    }\n    return Namespace.prototype.add.call(this, object);\n};\n\n/**\n * Removes a nested object from this type.\n * @param {ReflectionObject} object Nested object to remove\n * @returns {Type} `this`\n * @throws {TypeError} If arguments are invalid\n * @throws {Error} If `object` is not a member of this type\n */\nType.prototype.remove = function remove(object) {\n    if (object instanceof Field && object.extend === undefined) {\n        // See Type#add for the reason why extension fields are excluded here.\n\n        /* istanbul ignore if */\n        if (!this.fields || this.fields[object.name] !== object)\n            throw Error(object + \" is not a member of \" + this);\n\n        delete this.fields[object.name];\n        object.parent = null;\n        object.onRemove(this);\n        return clearCache(this);\n    }\n    if (object instanceof OneOf) {\n\n        /* istanbul ignore if */\n        if (!this.oneofs || this.oneofs[object.name] !== object)\n            throw Error(object + \" is not a member of \" + this);\n\n        delete this.oneofs[object.name];\n        object.parent = null;\n        object.onRemove(this);\n        return clearCache(this);\n    }\n    return Namespace.prototype.remove.call(this, object);\n};\n\n/**\n * Tests if the specified id is reserved.\n * @param {number} id Id to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nType.prototype.isReservedId = function isReservedId(id) {\n    return Namespace.isReservedId(this.reserved, id);\n};\n\n/**\n * Tests if the specified name is reserved.\n * @param {string} name Name to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nType.prototype.isReservedName = function isReservedName(name) {\n    return Namespace.isReservedName(this.reserved, name);\n};\n\n/**\n * Creates a new message of this type using the specified properties.\n * @param {Object.<string,*>} [properties] Properties to set\n * @returns {Message<{}>} Message instance\n */\nType.prototype.create = function create(properties) {\n    return new this.ctor(properties);\n};\n\n/**\n * Sets up {@link Type#encode|encode}, {@link Type#decode|decode} and {@link Type#verify|verify}.\n * @returns {Type} `this`\n */\nType.prototype.setup = function setup() {\n    // Sets up everything at once so that the prototype chain does not have to be re-evaluated\n    // multiple times (V8, soft-deopt prototype-check).\n\n    var fullName = this.fullName,\n        types    = [];\n    for (var i = 0; i < /* initializes */ this.fieldsArray.length; ++i)\n        types.push(this._fieldsArray[i].resolve().resolvedType);\n\n    // Replace setup methods with type-specific generated functions\n    this.encode = encoder(this)({\n        Writer : Writer,\n        types  : types,\n        util   : util\n    });\n    this.decode = decoder(this)({\n        Reader : Reader,\n        types  : types,\n        util   : util\n    });\n    this.verify = verifier(this)({\n        types : types,\n        util  : util\n    });\n    this.fromObject = converter.fromObject(this)({\n        types : types,\n        util  : util\n    });\n    this.toObject = converter.toObject(this)({\n        types : types,\n        util  : util\n    });\n\n    // Inject custom wrappers for common types\n    var wrapper = wrappers[fullName];\n    if (wrapper) {\n        var originalThis = Object.create(this);\n        // if (wrapper.fromObject) {\n            originalThis.fromObject = this.fromObject;\n            this.fromObject = wrapper.fromObject.bind(originalThis);\n        // }\n        // if (wrapper.toObject) {\n            originalThis.toObject = this.toObject;\n            this.toObject = wrapper.toObject.bind(originalThis);\n        // }\n    }\n\n    return this;\n};\n\n/**\n * Encodes a message of this type. Does not implicitly {@link Type#verify|verify} messages.\n * @param {Message<{}>|Object.<string,*>} message Message instance or plain object\n * @param {Writer} [writer] Writer to encode to\n * @returns {Writer} writer\n */\nType.prototype.encode = function encode_setup(message, writer) {\n    return this.setup().encode(message, writer); // overrides this method\n};\n\n/**\n * Encodes a message of this type preceeded by its byte length as a varint. Does not implicitly {@link Type#verify|verify} messages.\n * @param {Message<{}>|Object.<string,*>} message Message instance or plain object\n * @param {Writer} [writer] Writer to encode to\n * @returns {Writer} writer\n */\nType.prototype.encodeDelimited = function encodeDelimited(message, writer) {\n    return this.encode(message, writer && writer.len ? writer.fork() : writer).ldelim();\n};\n\n/**\n * Decodes a message of this type.\n * @param {Reader|Uint8Array} reader Reader or buffer to decode from\n * @param {number} [length] Length of the message, if known beforehand\n * @returns {Message<{}>} Decoded message\n * @throws {Error} If the payload is not a reader or valid buffer\n * @throws {util.ProtocolError<{}>} If required fields are missing\n */\nType.prototype.decode = function decode_setup(reader, length) {\n    return this.setup().decode(reader, length); // overrides this method\n};\n\n/**\n * Decodes a message of this type preceeded by its byte length as a varint.\n * @param {Reader|Uint8Array} reader Reader or buffer to decode from\n * @returns {Message<{}>} Decoded message\n * @throws {Error} If the payload is not a reader or valid buffer\n * @throws {util.ProtocolError} If required fields are missing\n */\nType.prototype.decodeDelimited = function decodeDelimited(reader) {\n    if (!(reader instanceof Reader))\n        reader = Reader.create(reader);\n    return this.decode(reader, reader.uint32());\n};\n\n/**\n * Verifies that field values are valid and that required fields are present.\n * @param {Object.<string,*>} message Plain object to verify\n * @returns {null|string} `null` if valid, otherwise the reason why it is not\n */\nType.prototype.verify = function verify_setup(message) {\n    return this.setup().verify(message); // overrides this method\n};\n\n/**\n * Creates a new message of this type from a plain object. Also converts values to their respective internal types.\n * @param {Object.<string,*>} object Plain object to convert\n * @returns {Message<{}>} Message instance\n */\nType.prototype.fromObject = function fromObject(object) {\n    return this.setup().fromObject(object);\n};\n\n/**\n * Conversion options as used by {@link Type#toObject} and {@link Message.toObject}.\n * @interface IConversionOptions\n * @property {Function} [longs] Long conversion type.\n * Valid values are `String` and `Number` (the global types).\n * Defaults to copy the present value, which is a possibly unsafe number without and a {@link Long} with a long library.\n * @property {Function} [enums] Enum value conversion type.\n * Only valid value is `String` (the global type).\n * Defaults to copy the present value, which is the numeric id.\n * @property {Function} [bytes] Bytes value conversion type.\n * Valid values are `Array` and (a base64 encoded) `String` (the global types).\n * Defaults to copy the present value, which usually is a Buffer under node and an Uint8Array in the browser.\n * @property {boolean} [defaults=false] Also sets default values on the resulting object\n * @property {boolean} [arrays=false] Sets empty arrays for missing repeated fields even if `defaults=false`\n * @property {boolean} [objects=false] Sets empty objects for missing map fields even if `defaults=false`\n * @property {boolean} [oneofs=false] Includes virtual oneof properties set to the present field's name, if any\n * @property {boolean} [json=false] Performs additional JSON compatibility conversions, i.e. NaN and Infinity to strings\n */\n\n/**\n * Creates a plain object from a message of this type. Also converts values to other types if specified.\n * @param {Message<{}>} message Message instance\n * @param {IConversionOptions} [options] Conversion options\n * @returns {Object.<string,*>} Plain object\n */\nType.prototype.toObject = function toObject(message, options) {\n    return this.setup().toObject(message, options);\n};\n\n/**\n * Decorator function as returned by {@link Type.d} (TypeScript).\n * @typedef TypeDecorator\n * @type {function}\n * @param {Constructor<T>} target Target constructor\n * @returns {undefined}\n * @template T extends Message<T>\n */\n\n/**\n * Type decorator (TypeScript).\n * @param {string} [typeName] Type name, defaults to the constructor's name\n * @returns {TypeDecorator<T>} Decorator function\n * @template T extends Message<T>\n */\nType.d = function decorateType(typeName) {\n    return function typeDecorator(target) {\n        util.decorateType(target, typeName);\n    };\n};\n", "\"use strict\";\n\n/**\n * Common type constants.\n * @namespace\n */\nvar types = exports;\n\nvar util = require(37);\n\nvar s = [\n    \"double\",   // 0\n    \"float\",    // 1\n    \"int32\",    // 2\n    \"uint32\",   // 3\n    \"sint32\",   // 4\n    \"fixed32\",  // 5\n    \"sfixed32\", // 6\n    \"int64\",    // 7\n    \"uint64\",   // 8\n    \"sint64\",   // 9\n    \"fixed64\",  // 10\n    \"sfixed64\", // 11\n    \"bool\",     // 12\n    \"string\",   // 13\n    \"bytes\"     // 14\n];\n\nfunction bake(values, offset) {\n    var i = 0, o = {};\n    offset |= 0;\n    while (i < values.length) o[s[i + offset]] = values[i++];\n    return o;\n}\n\n/**\n * Basic type wire types.\n * @type {Object.<string,number>}\n * @const\n * @property {number} double=1 Fixed64 wire type\n * @property {number} float=5 Fixed32 wire type\n * @property {number} int32=0 Varint wire type\n * @property {number} uint32=0 Varint wire type\n * @property {number} sint32=0 Varint wire type\n * @property {number} fixed32=5 Fixed32 wire type\n * @property {number} sfixed32=5 Fixed32 wire type\n * @property {number} int64=0 Varint wire type\n * @property {number} uint64=0 Varint wire type\n * @property {number} sint64=0 Varint wire type\n * @property {number} fixed64=1 Fixed64 wire type\n * @property {number} sfixed64=1 Fixed64 wire type\n * @property {number} bool=0 Varint wire type\n * @property {number} string=2 Ldelim wire type\n * @property {number} bytes=2 Ldelim wire type\n */\ntypes.basic = bake([\n    /* double   */ 1,\n    /* float    */ 5,\n    /* int32    */ 0,\n    /* uint32   */ 0,\n    /* sint32   */ 0,\n    /* fixed32  */ 5,\n    /* sfixed32 */ 5,\n    /* int64    */ 0,\n    /* uint64   */ 0,\n    /* sint64   */ 0,\n    /* fixed64  */ 1,\n    /* sfixed64 */ 1,\n    /* bool     */ 0,\n    /* string   */ 2,\n    /* bytes    */ 2\n]);\n\n/**\n * Basic type defaults.\n * @type {Object.<string,*>}\n * @const\n * @property {number} double=0 Double default\n * @property {number} float=0 Float default\n * @property {number} int32=0 Int32 default\n * @property {number} uint32=0 Uint32 default\n * @property {number} sint32=0 Sint32 default\n * @property {number} fixed32=0 Fixed32 default\n * @property {number} sfixed32=0 Sfixed32 default\n * @property {number} int64=0 Int64 default\n * @property {number} uint64=0 Uint64 default\n * @property {number} sint64=0 Sint32 default\n * @property {number} fixed64=0 Fixed64 default\n * @property {number} sfixed64=0 Sfixed64 default\n * @property {boolean} bool=false Bool default\n * @property {string} string=\"\" String default\n * @property {Array.<number>} bytes=Array(0) Bytes default\n * @property {null} message=null Message default\n */\ntypes.defaults = bake([\n    /* double   */ 0,\n    /* float    */ 0,\n    /* int32    */ 0,\n    /* uint32   */ 0,\n    /* sint32   */ 0,\n    /* fixed32  */ 0,\n    /* sfixed32 */ 0,\n    /* int64    */ 0,\n    /* uint64   */ 0,\n    /* sint64   */ 0,\n    /* fixed64  */ 0,\n    /* sfixed64 */ 0,\n    /* bool     */ false,\n    /* string   */ \"\",\n    /* bytes    */ util.emptyArray,\n    /* message  */ null\n]);\n\n/**\n * Basic long type wire types.\n * @type {Object.<string,number>}\n * @const\n * @property {number} int64=0 Varint wire type\n * @property {number} uint64=0 Varint wire type\n * @property {number} sint64=0 Varint wire type\n * @property {number} fixed64=1 Fixed64 wire type\n * @property {number} sfixed64=1 Fixed64 wire type\n */\ntypes.long = bake([\n    /* int64    */ 0,\n    /* uint64   */ 0,\n    /* sint64   */ 0,\n    /* fixed64  */ 1,\n    /* sfixed64 */ 1\n], 7);\n\n/**\n * Allowed types for map keys with their associated wire type.\n * @type {Object.<string,number>}\n * @const\n * @property {number} int32=0 Varint wire type\n * @property {number} uint32=0 Varint wire type\n * @property {number} sint32=0 Varint wire type\n * @property {number} fixed32=5 Fixed32 wire type\n * @property {number} sfixed32=5 Fixed32 wire type\n * @property {number} int64=0 Varint wire type\n * @property {number} uint64=0 Varint wire type\n * @property {number} sint64=0 Varint wire type\n * @property {number} fixed64=1 Fixed64 wire type\n * @property {number} sfixed64=1 Fixed64 wire type\n * @property {number} bool=0 Varint wire type\n * @property {number} string=2 Ldelim wire type\n */\ntypes.mapKey = bake([\n    /* int32    */ 0,\n    /* uint32   */ 0,\n    /* sint32   */ 0,\n    /* fixed32  */ 5,\n    /* sfixed32 */ 5,\n    /* int64    */ 0,\n    /* uint64   */ 0,\n    /* sint64   */ 0,\n    /* fixed64  */ 1,\n    /* sfixed64 */ 1,\n    /* bool     */ 0,\n    /* string   */ 2\n], 2);\n\n/**\n * Allowed types for packed repeated fields with their associated wire type.\n * @type {Object.<string,number>}\n * @const\n * @property {number} double=1 Fixed64 wire type\n * @property {number} float=5 Fixed32 wire type\n * @property {number} int32=0 Varint wire type\n * @property {number} uint32=0 Varint wire type\n * @property {number} sint32=0 Varint wire type\n * @property {number} fixed32=5 Fixed32 wire type\n * @property {number} sfixed32=5 Fixed32 wire type\n * @property {number} int64=0 Varint wire type\n * @property {number} uint64=0 Varint wire type\n * @property {number} sint64=0 Varint wire type\n * @property {number} fixed64=1 Fixed64 wire type\n * @property {number} sfixed64=1 Fixed64 wire type\n * @property {number} bool=0 Varint wire type\n */\ntypes.packed = bake([\n    /* double   */ 1,\n    /* float    */ 5,\n    /* int32    */ 0,\n    /* uint32   */ 0,\n    /* sint32   */ 0,\n    /* fixed32  */ 5,\n    /* sfixed32 */ 5,\n    /* int64    */ 0,\n    /* uint64   */ 0,\n    /* sint64   */ 0,\n    /* fixed64  */ 1,\n    /* sfixed64 */ 1,\n    /* bool     */ 0\n]);\n", "\"use strict\";\n\n/**\n * Various utility functions.\n * @namespace\n */\nvar util = module.exports = require(39);\n\nvar roots = require(30);\n\nvar Type, // cyclic\n    Enum;\n\nutil.codegen = require(3);\nutil.fetch   = require(5);\nutil.path    = require(8);\n\n/**\n * Node's fs module if available.\n * @type {Object.<string,*>}\n */\nutil.fs = util.inquire(\"fs\");\n\n/**\n * Converts an object's values to an array.\n * @param {Object.<string,*>} object Object to convert\n * @returns {Array.<*>} Converted array\n */\nutil.toArray = function toArray(object) {\n    if (object) {\n        var keys  = Object.keys(object),\n            array = new Array(keys.length),\n            index = 0;\n        while (index < keys.length)\n            array[index] = object[keys[index++]];\n        return array;\n    }\n    return [];\n};\n\n/**\n * Converts an array of keys immediately followed by their respective value to an object, omitting undefined values.\n * @param {Array.<*>} array Array to convert\n * @returns {Object.<string,*>} Converted object\n */\nutil.toObject = function toObject(array) {\n    var object = {},\n        index  = 0;\n    while (index < array.length) {\n        var key = array[index++],\n            val = array[index++];\n        if (val !== undefined)\n            object[key] = val;\n    }\n    return object;\n};\n\nvar safePropBackslashRe = /\\\\/g,\n    safePropQuoteRe     = /\"/g;\n\n/**\n * Tests whether the specified name is a reserved word in JS.\n * @param {string} name Name to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nutil.isReserved = function isReserved(name) {\n    return /^(?:do|if|in|for|let|new|try|var|case|else|enum|eval|false|null|this|true|void|with|break|catch|class|const|super|throw|while|yield|delete|export|import|public|return|static|switch|typeof|default|extends|finally|package|private|continue|debugger|function|arguments|interface|protected|implements|instanceof)$/.test(name);\n};\n\n/**\n * Returns a safe property accessor for the specified property name.\n * @param {string} prop Property name\n * @returns {string} Safe accessor\n */\nutil.safeProp = function safeProp(prop) {\n    if (!/^[$\\w_]+$/.test(prop) || util.isReserved(prop))\n        return \"[\\\"\" + prop.replace(safePropBackslashRe, \"\\\\\\\\\").replace(safePropQuoteRe, \"\\\\\\\"\") + \"\\\"]\";\n    return \".\" + prop;\n};\n\n/**\n * Converts the first character of a string to upper case.\n * @param {string} str String to convert\n * @returns {string} Converted string\n */\nutil.ucFirst = function ucFirst(str) {\n    return str.charAt(0).toUpperCase() + str.substring(1);\n};\n\nvar camelCaseRe = /_([a-z])/g;\n\n/**\n * Converts a string to camel case.\n * @param {string} str String to convert\n * @returns {string} Converted string\n */\nutil.camelCase = function camelCase(str) {\n    return str.substring(0, 1)\n         + str.substring(1)\n               .replace(camelCaseRe, function($0, $1) { return $1.toUpperCase(); });\n};\n\n/**\n * Compares reflected fields by id.\n * @param {Field} a First field\n * @param {Field} b Second field\n * @returns {number} Comparison value\n */\nutil.compareFieldsById = function compareFieldsById(a, b) {\n    return a.id - b.id;\n};\n\n/**\n * Decorator helper for types (TypeScript).\n * @param {Constructor<T>} ctor Constructor function\n * @param {string} [typeName] Type name, defaults to the constructor's name\n * @returns {Type} Reflected type\n * @template T extends Message<T>\n * @property {Root} root Decorators root\n */\nutil.decorateType = function decorateType(ctor, typeName) {\n\n    /* istanbul ignore if */\n    if (ctor.$type) {\n        if (typeName && ctor.$type.name !== typeName) {\n            util.decorateRoot.remove(ctor.$type);\n            ctor.$type.name = typeName;\n            util.decorateRoot.add(ctor.$type);\n        }\n        return ctor.$type;\n    }\n\n    /* istanbul ignore next */\n    if (!Type)\n        Type = require(35);\n\n    var type = new Type(typeName || ctor.name);\n    util.decorateRoot.add(type);\n    type.ctor = ctor; // sets up .encode, .decode etc.\n    Object.defineProperty(ctor, \"$type\", { value: type, enumerable: false });\n    Object.defineProperty(ctor.prototype, \"$type\", { value: type, enumerable: false });\n    return type;\n};\n\nvar decorateEnumIndex = 0;\n\n/**\n * Decorator helper for enums (TypeScript).\n * @param {Object} object Enum object\n * @returns {Enum} Reflected enum\n */\nutil.decorateEnum = function decorateEnum(object) {\n\n    /* istanbul ignore if */\n    if (object.$type)\n        return object.$type;\n\n    /* istanbul ignore next */\n    if (!Enum)\n        Enum = require(15);\n\n    var enm = new Enum(\"Enum\" + decorateEnumIndex++, object);\n    util.decorateRoot.add(enm);\n    Object.defineProperty(object, \"$type\", { value: enm, enumerable: false });\n    return enm;\n};\n\n\n/**\n * Sets the value of a property by property path. If a value already exists, it is turned to an array\n * @param {Object.<string,*>} dst Destination object\n * @param {string} path dot '.' delimited path of the property to set\n * @param {Object} value the value to set\n * @returns {Object.<string,*>} Destination object\n */\nutil.setProperty = function setProperty(dst, path, value) {\n    function setProp(dst, path, value) {\n        var part = path.shift();\n        if (part === \"__proto__\") {\n          return dst;\n        }\n        if (path.length > 0) {\n            dst[part] = setProp(dst[part] || {}, path, value);\n        } else {\n            var prevValue = dst[part];\n            if (prevValue)\n                value = [].concat(prevValue).concat(value);\n            dst[part] = value;\n        }\n        return dst;\n    }\n\n    if (typeof dst !== \"object\")\n        throw TypeError(\"dst must be an object\");\n    if (!path)\n        throw TypeError(\"path must be specified\");\n\n    path = path.split(\".\");\n    return setProp(dst, path, value);\n};\n\n/**\n * Decorator root (TypeScript).\n * @name util.decorateRoot\n * @type {Root}\n * @readonly\n */\nObject.defineProperty(util, \"decorateRoot\", {\n    get: function() {\n        return roots[\"decorated\"] || (roots[\"decorated\"] = new (require(29))());\n    }\n});\n", "\"use strict\";\nmodule.exports = LongBits;\n\nvar util = require(39);\n\n/**\n * Constructs new long bits.\n * @classdesc Helper class for working with the low and high bits of a 64 bit value.\n * @memberof util\n * @constructor\n * @param {number} lo Low 32 bits, unsigned\n * @param {number} hi High 32 bits, unsigned\n */\nfunction LongBits(lo, hi) {\n\n    // note that the casts below are theoretically unnecessary as of today, but older statically\n    // generated converter code might still call the ctor with signed 32bits. kept for compat.\n\n    /**\n     * Low bits.\n     * @type {number}\n     */\n    this.lo = lo >>> 0;\n\n    /**\n     * High bits.\n     * @type {number}\n     */\n    this.hi = hi >>> 0;\n}\n\n/**\n * Zero bits.\n * @memberof util.LongBits\n * @type {util.LongBits}\n */\nvar zero = LongBits.zero = new LongBits(0, 0);\n\nzero.toNumber = function() { return 0; };\nzero.zzEncode = zero.zzDecode = function() { return this; };\nzero.length = function() { return 1; };\n\n/**\n * Zero hash.\n * @memberof util.LongBits\n * @type {string}\n */\nvar zeroHash = LongBits.zeroHash = \"\\0\\0\\0\\0\\0\\0\\0\\0\";\n\n/**\n * Constructs new long bits from the specified number.\n * @param {number} value Value\n * @returns {util.LongBits} Instance\n */\nLongBits.fromNumber = function fromNumber(value) {\n    if (value === 0)\n        return zero;\n    var sign = value < 0;\n    if (sign)\n        value = -value;\n    var lo = value >>> 0,\n        hi = (value - lo) / 4294967296 >>> 0;\n    if (sign) {\n        hi = ~hi >>> 0;\n        lo = ~lo >>> 0;\n        if (++lo > 4294967295) {\n            lo = 0;\n            if (++hi > 4294967295)\n                hi = 0;\n        }\n    }\n    return new LongBits(lo, hi);\n};\n\n/**\n * Constructs new long bits from a number, long or string.\n * @param {Long|number|string} value Value\n * @returns {util.LongBits} Instance\n */\nLongBits.from = function from(value) {\n    if (typeof value === \"number\")\n        return LongBits.fromNumber(value);\n    if (util.isString(value)) {\n        /* istanbul ignore else */\n        if (util.Long)\n            value = util.Long.fromString(value);\n        else\n            return LongBits.fromNumber(parseInt(value, 10));\n    }\n    return value.low || value.high ? new LongBits(value.low >>> 0, value.high >>> 0) : zero;\n};\n\n/**\n * Converts this long bits to a possibly unsafe JavaScript number.\n * @param {boolean} [unsigned=false] Whether unsigned or not\n * @returns {number} Possibly unsafe number\n */\nLongBits.prototype.toNumber = function toNumber(unsigned) {\n    if (!unsigned && this.hi >>> 31) {\n        var lo = ~this.lo + 1 >>> 0,\n            hi = ~this.hi     >>> 0;\n        if (!lo)\n            hi = hi + 1 >>> 0;\n        return -(lo + hi * 4294967296);\n    }\n    return this.lo + this.hi * 4294967296;\n};\n\n/**\n * Converts this long bits to a long.\n * @param {boolean} [unsigned=false] Whether unsigned or not\n * @returns {Long} Long\n */\nLongBits.prototype.toLong = function toLong(unsigned) {\n    return util.Long\n        ? new util.Long(this.lo | 0, this.hi | 0, Boolean(unsigned))\n        /* istanbul ignore next */\n        : { low: this.lo | 0, high: this.hi | 0, unsigned: Boolean(unsigned) };\n};\n\nvar charCodeAt = String.prototype.charCodeAt;\n\n/**\n * Constructs new long bits from the specified 8 characters long hash.\n * @param {string} hash Hash\n * @returns {util.LongBits} Bits\n */\nLongBits.fromHash = function fromHash(hash) {\n    if (hash === zeroHash)\n        return zero;\n    return new LongBits(\n        ( charCodeAt.call(hash, 0)\n        | charCodeAt.call(hash, 1) << 8\n        | charCodeAt.call(hash, 2) << 16\n        | charCodeAt.call(hash, 3) << 24) >>> 0\n    ,\n        ( charCodeAt.call(hash, 4)\n        | charCodeAt.call(hash, 5) << 8\n        | charCodeAt.call(hash, 6) << 16\n        | charCodeAt.call(hash, 7) << 24) >>> 0\n    );\n};\n\n/**\n * Converts this long bits to a 8 characters long hash.\n * @returns {string} Hash\n */\nLongBits.prototype.toHash = function toHash() {\n    return String.fromCharCode(\n        this.lo        & 255,\n        this.lo >>> 8  & 255,\n        this.lo >>> 16 & 255,\n        this.lo >>> 24      ,\n        this.hi        & 255,\n        this.hi >>> 8  & 255,\n        this.hi >>> 16 & 255,\n        this.hi >>> 24\n    );\n};\n\n/**\n * Zig-zag encodes this long bits.\n * @returns {util.LongBits} `this`\n */\nLongBits.prototype.zzEncode = function zzEncode() {\n    var mask =   this.hi >> 31;\n    this.hi  = ((this.hi << 1 | this.lo >>> 31) ^ mask) >>> 0;\n    this.lo  = ( this.lo << 1                   ^ mask) >>> 0;\n    return this;\n};\n\n/**\n * Zig-zag decodes this long bits.\n * @returns {util.LongBits} `this`\n */\nLongBits.prototype.zzDecode = function zzDecode() {\n    var mask = -(this.lo & 1);\n    this.lo  = ((this.lo >>> 1 | this.hi << 31) ^ mask) >>> 0;\n    this.hi  = ( this.hi >>> 1                  ^ mask) >>> 0;\n    return this;\n};\n\n/**\n * Calculates the length of this longbits when encoded as a varint.\n * @returns {number} Length\n */\nLongBits.prototype.length = function length() {\n    var part0 =  this.lo,\n        part1 = (this.lo >>> 28 | this.hi << 4) >>> 0,\n        part2 =  this.hi >>> 24;\n    return part2 === 0\n         ? part1 === 0\n           ? part0 < 16384\n             ? part0 < 128 ? 1 : 2\n             : part0 < 2097152 ? 3 : 4\n           : part1 < 16384\n             ? part1 < 128 ? 5 : 6\n             : part1 < 2097152 ? 7 : 8\n         : part2 < 128 ? 9 : 10;\n};\n", "\"use strict\";\nvar util = exports;\n\n// used to return a Promise where callback is omitted\nutil.asPromise = require(1);\n\n// converts to / from base64 encoded strings\nutil.base64 = require(2);\n\n// base class of rpc.Service\nutil.EventEmitter = require(4);\n\n// float handling accross browsers\nutil.float = require(6);\n\n// requires modules optionally and hides the call from bundlers\nutil.inquire = require(7);\n\n// converts to / from utf8 encoded strings\nutil.utf8 = require(10);\n\n// provides a node-like buffer pool in the browser\nutil.pool = require(9);\n\n// utility to work with the low and high bits of a 64 bit value\nutil.LongBits = require(38);\n\n/**\n * Whether running within node or not.\n * @memberof util\n * @type {boolean}\n */\nutil.isNode = Boolean(typeof global !== \"undefined\"\n                   && global\n                   && global.process\n                   && global.process.versions\n                   && global.process.versions.node);\n\n/**\n * Global object reference.\n * @memberof util\n * @type {Object}\n */\nutil.global = util.isNode && global\n           || typeof window !== \"undefined\" && window\n           || typeof self   !== \"undefined\" && self\n           || this; // eslint-disable-line no-invalid-this\n\n/**\n * An immuable empty array.\n * @memberof util\n * @type {Array.<*>}\n * @const\n */\nutil.emptyArray = Object.freeze ? Object.freeze([]) : /* istanbul ignore next */ []; // used on prototypes\n\n/**\n * An immutable empty object.\n * @type {Object}\n * @const\n */\nutil.emptyObject = Object.freeze ? Object.freeze({}) : /* istanbul ignore next */ {}; // used on prototypes\n\n/**\n * Tests if the specified value is an integer.\n * @function\n * @param {*} value Value to test\n * @returns {boolean} `true` if the value is an integer\n */\nutil.isInteger = Number.isInteger || /* istanbul ignore next */ function isInteger(value) {\n    return typeof value === \"number\" && isFinite(value) && Math.floor(value) === value;\n};\n\n/**\n * Tests if the specified value is a string.\n * @param {*} value Value to test\n * @returns {boolean} `true` if the value is a string\n */\nutil.isString = function isString(value) {\n    return typeof value === \"string\" || value instanceof String;\n};\n\n/**\n * Tests if the specified value is a non-null object.\n * @param {*} value Value to test\n * @returns {boolean} `true` if the value is a non-null object\n */\nutil.isObject = function isObject(value) {\n    return value && typeof value === \"object\";\n};\n\n/**\n * Checks if a property on a message is considered to be present.\n * This is an alias of {@link util.isSet}.\n * @function\n * @param {Object} obj Plain object or message instance\n * @param {string} prop Property name\n * @returns {boolean} `true` if considered to be present, otherwise `false`\n */\nutil.isset =\n\n/**\n * Checks if a property on a message is considered to be present.\n * @param {Object} obj Plain object or message instance\n * @param {string} prop Property name\n * @returns {boolean} `true` if considered to be present, otherwise `false`\n */\nutil.isSet = function isSet(obj, prop) {\n    var value = obj[prop];\n    if (value != null && obj.hasOwnProperty(prop)) // eslint-disable-line eqeqeq, no-prototype-builtins\n        return typeof value !== \"object\" || (Array.isArray(value) ? value.length : Object.keys(value).length) > 0;\n    return false;\n};\n\n/**\n * Any compatible Buffer instance.\n * This is a minimal stand-alone definition of a Buffer instance. The actual type is that exported by node's typings.\n * @interface Buffer\n * @extends Uint8Array\n */\n\n/**\n * Node's Buffer class if available.\n * @type {Constructor<Buffer>}\n */\nutil.Buffer = (function() {\n    try {\n        var Buffer = util.inquire(\"buffer\").Buffer;\n        // refuse to use non-node buffers if not explicitly assigned (perf reasons):\n        return Buffer.prototype.utf8Write ? Buffer : /* istanbul ignore next */ null;\n    } catch (e) {\n        /* istanbul ignore next */\n        return null;\n    }\n})();\n\n// Internal alias of or polyfull for Buffer.from.\nutil._Buffer_from = null;\n\n// Internal alias of or polyfill for Buffer.allocUnsafe.\nutil._Buffer_allocUnsafe = null;\n\n/**\n * Creates a new buffer of whatever type supported by the environment.\n * @param {number|number[]} [sizeOrArray=0] Buffer size or number array\n * @returns {Uint8Array|Buffer} Buffer\n */\nutil.newBuffer = function newBuffer(sizeOrArray) {\n    /* istanbul ignore next */\n    return typeof sizeOrArray === \"number\"\n        ? util.Buffer\n            ? util._Buffer_allocUnsafe(sizeOrArray)\n            : new util.Array(sizeOrArray)\n        : util.Buffer\n            ? util._Buffer_from(sizeOrArray)\n            : typeof Uint8Array === \"undefined\"\n                ? sizeOrArray\n                : new Uint8Array(sizeOrArray);\n};\n\n/**\n * Array implementation used in the browser. `Uint8Array` if supported, otherwise `Array`.\n * @type {Constructor<Uint8Array>}\n */\nutil.Array = typeof Uint8Array !== \"undefined\" ? Uint8Array /* istanbul ignore next */ : Array;\n\n/**\n * Any compatible Long instance.\n * This is a minimal stand-alone definition of a Long instance. The actual type is that exported by long.js.\n * @interface Long\n * @property {number} low Low bits\n * @property {number} high High bits\n * @property {boolean} unsigned Whether unsigned or not\n */\n\n/**\n * Long.js's Long class if available.\n * @type {Constructor<Long>}\n */\nutil.Long = /* istanbul ignore next */ util.global.dcodeIO && /* istanbul ignore next */ util.global.dcodeIO.Long\n         || /* istanbul ignore next */ util.global.Long\n         || util.inquire(\"long\");\n\n/**\n * Regular expression used to verify 2 bit (`bool`) map keys.\n * @type {RegExp}\n * @const\n */\nutil.key2Re = /^true|false|0|1$/;\n\n/**\n * Regular expression used to verify 32 bit (`int32` etc.) map keys.\n * @type {RegExp}\n * @const\n */\nutil.key32Re = /^-?(?:0|[1-9][0-9]*)$/;\n\n/**\n * Regular expression used to verify 64 bit (`int64` etc.) map keys.\n * @type {RegExp}\n * @const\n */\nutil.key64Re = /^(?:[\\\\x00-\\\\xff]{8}|-?(?:0|[1-9][0-9]*))$/;\n\n/**\n * Converts a number or long to an 8 characters long hash string.\n * @param {Long|number} value Value to convert\n * @returns {string} Hash\n */\nutil.longToHash = function longToHash(value) {\n    return value\n        ? util.LongBits.from(value).toHash()\n        : util.LongBits.zeroHash;\n};\n\n/**\n * Converts an 8 characters long hash string to a long or number.\n * @param {string} hash Hash\n * @param {boolean} [unsigned=false] Whether unsigned or not\n * @returns {Long|number} Original value\n */\nutil.longFromHash = function longFromHash(hash, unsigned) {\n    var bits = util.LongBits.fromHash(hash);\n    if (util.Long)\n        return util.Long.fromBits(bits.lo, bits.hi, unsigned);\n    return bits.toNumber(Boolean(unsigned));\n};\n\n/**\n * Merges the properties of the source object into the destination object.\n * @memberof util\n * @param {Object.<string,*>} dst Destination object\n * @param {Object.<string,*>} src Source object\n * @param {boolean} [ifNotSet=false] Merges only if the key is not already set\n * @returns {Object.<string,*>} Destination object\n */\nfunction merge(dst, src, ifNotSet) { // used by converters\n    for (var keys = Object.keys(src), i = 0; i < keys.length; ++i)\n        if (dst[keys[i]] === undefined || !ifNotSet)\n            dst[keys[i]] = src[keys[i]];\n    return dst;\n}\n\nutil.merge = merge;\n\n/**\n * Converts the first character of a string to lower case.\n * @param {string} str String to convert\n * @returns {string} Converted string\n */\nutil.lcFirst = function lcFirst(str) {\n    return str.charAt(0).toLowerCase() + str.substring(1);\n};\n\n/**\n * Creates a custom error constructor.\n * @memberof util\n * @param {string} name Error name\n * @returns {Constructor<Error>} Custom error constructor\n */\nfunction newError(name) {\n\n    function CustomError(message, properties) {\n\n        if (!(this instanceof CustomError))\n            return new CustomError(message, properties);\n\n        // Error.call(this, message);\n        // ^ just returns a new error instance because the ctor can be called as a function\n\n        Object.defineProperty(this, \"message\", { get: function() { return message; } });\n\n        /* istanbul ignore next */\n        if (Error.captureStackTrace) // node\n            Error.captureStackTrace(this, CustomError);\n        else\n            Object.defineProperty(this, \"stack\", { value: new Error().stack || \"\" });\n\n        if (properties)\n            merge(this, properties);\n    }\n\n    CustomError.prototype = Object.create(Error.prototype, {\n        constructor: {\n            value: CustomError,\n            writable: true,\n            enumerable: false,\n            configurable: true,\n        },\n        name: {\n            get() { return name; },\n            set: undefined,\n            enumerable: false,\n            // configurable: false would accurately preserve the behavior of\n            // the original, but I'm guessing that was not intentional.\n            // For an actual error subclass, this property would\n            // be configurable.\n            configurable: true,\n        },\n        toString: {\n            value() { return this.name + \": \" + this.message; },\n            writable: true,\n            enumerable: false,\n            configurable: true,\n        },\n    });\n\n    return CustomError;\n}\n\nutil.newError = newError;\n\n/**\n * Constructs a new protocol error.\n * @classdesc Error subclass indicating a protocol specifc error.\n * @memberof util\n * @extends Error\n * @template T extends Message<T>\n * @constructor\n * @param {string} message Error message\n * @param {Object.<string,*>} [properties] Additional properties\n * @example\n * try {\n *     MyMessage.decode(someBuffer); // throws if required fields are missing\n * } catch (e) {\n *     if (e instanceof ProtocolError && e.instance)\n *         console.log(\"decoded so far: \" + JSON.stringify(e.instance));\n * }\n */\nutil.ProtocolError = newError(\"ProtocolError\");\n\n/**\n * So far decoded message instance.\n * @name util.ProtocolError#instance\n * @type {Message<T>}\n */\n\n/**\n * A OneOf getter as returned by {@link util.oneOfGetter}.\n * @typedef OneOfGetter\n * @type {function}\n * @returns {string|undefined} Set field name, if any\n */\n\n/**\n * Builds a getter for a oneof's present field name.\n * @param {string[]} fieldNames Field names\n * @returns {OneOfGetter} Unbound getter\n */\nutil.oneOfGetter = function getOneOf(fieldNames) {\n    var fieldMap = {};\n    for (var i = 0; i < fieldNames.length; ++i)\n        fieldMap[fieldNames[i]] = 1;\n\n    /**\n     * @returns {string|undefined} Set field name, if any\n     * @this Object\n     * @ignore\n     */\n    return function() { // eslint-disable-line consistent-return\n        for (var keys = Object.keys(this), i = keys.length - 1; i > -1; --i)\n            if (fieldMap[keys[i]] === 1 && this[keys[i]] !== undefined && this[keys[i]] !== null)\n                return keys[i];\n    };\n};\n\n/**\n * A OneOf setter as returned by {@link util.oneOfSetter}.\n * @typedef OneOfSetter\n * @type {function}\n * @param {string|undefined} value Field name\n * @returns {undefined}\n */\n\n/**\n * Builds a setter for a oneof's present field name.\n * @param {string[]} fieldNames Field names\n * @returns {OneOfSetter} Unbound setter\n */\nutil.oneOfSetter = function setOneOf(fieldNames) {\n\n    /**\n     * @param {string} name Field name\n     * @returns {undefined}\n     * @this Object\n     * @ignore\n     */\n    return function(name) {\n        for (var i = 0; i < fieldNames.length; ++i)\n            if (fieldNames[i] !== name)\n                delete this[fieldNames[i]];\n    };\n};\n\n/**\n * Default conversion options used for {@link Message#toJSON} implementations.\n *\n * These options are close to proto3's JSON mapping with the exception that internal types like Any are handled just like messages. More precisely:\n *\n * - Longs become strings\n * - Enums become string keys\n * - Bytes become base64 encoded strings\n * - (Sub-)Messages become plain objects\n * - Maps become plain objects with all string keys\n * - Repeated fields become arrays\n * - NaN and Infinity for float and double fields become strings\n *\n * @type {IConversionOptions}\n * @see https://developers.google.com/protocol-buffers/docs/proto3?hl=en#json\n */\nutil.toJSONOptions = {\n    longs: String,\n    enums: String,\n    bytes: String,\n    json: true\n};\n\n// Sets up buffer utility according to the environment (called in index-minimal)\nutil._configure = function() {\n    var Buffer = util.Buffer;\n    /* istanbul ignore if */\n    if (!Buffer) {\n        util._Buffer_from = util._Buffer_allocUnsafe = null;\n        return;\n    }\n    // because node 4.x buffers are incompatible & immutable\n    // see: https://github.com/dcodeIO/protobuf.js/pull/665\n    util._Buffer_from = Buffer.from !== Uint8Array.from && Buffer.from ||\n        /* istanbul ignore next */\n        function Buffer_from(value, encoding) {\n            return new Buffer(value, encoding);\n        };\n    util._Buffer_allocUnsafe = Buffer.allocUnsafe ||\n        /* istanbul ignore next */\n        function Buffer_allocUnsafe(size) {\n            return new Buffer(size);\n        };\n};\n", "\"use strict\";\nmodule.exports = verifier;\n\nvar Enum      = require(15),\n    util      = require(37);\n\nfunction invalid(field, expected) {\n    return field.name + \": \" + expected + (field.repeated && expected !== \"array\" ? \"[]\" : field.map && expected !== \"object\" ? \"{k:\"+field.keyType+\"}\" : \"\") + \" expected\";\n}\n\n/**\n * Generates a partial value verifier.\n * @param {Codegen} gen Codegen instance\n * @param {Field} field Reflected field\n * @param {number} fieldIndex Field index\n * @param {string} ref Variable reference\n * @returns {Codegen} Codegen instance\n * @ignore\n */\nfunction genVerifyValue(gen, field, fieldIndex, ref) {\n    /* eslint-disable no-unexpected-multiline */\n    if (field.resolvedType) {\n        if (field.resolvedType instanceof Enum) { gen\n            (\"switch(%s){\", ref)\n                (\"default:\")\n                    (\"return%j\", invalid(field, \"enum value\"));\n            for (var keys = Object.keys(field.resolvedType.values), j = 0; j < keys.length; ++j) gen\n                (\"case %i:\", field.resolvedType.values[keys[j]]);\n            gen\n                    (\"break\")\n            (\"}\");\n        } else {\n            gen\n            (\"{\")\n                (\"var e=types[%i].verify(%s);\", fieldIndex, ref)\n                (\"if(e)\")\n                    (\"return%j+e\", field.name + \".\")\n            (\"}\");\n        }\n    } else {\n        switch (field.type) {\n            case \"int32\":\n            case \"uint32\":\n            case \"sint32\":\n            case \"fixed32\":\n            case \"sfixed32\": gen\n                (\"if(!util.isInteger(%s))\", ref)\n                    (\"return%j\", invalid(field, \"integer\"));\n                break;\n            case \"int64\":\n            case \"uint64\":\n            case \"sint64\":\n            case \"fixed64\":\n            case \"sfixed64\": gen\n                (\"if(!util.isInteger(%s)&&!(%s&&util.isInteger(%s.low)&&util.isInteger(%s.high)))\", ref, ref, ref, ref)\n                    (\"return%j\", invalid(field, \"integer|Long\"));\n                break;\n            case \"float\":\n            case \"double\": gen\n                (\"if(typeof %s!==\\\"number\\\")\", ref)\n                    (\"return%j\", invalid(field, \"number\"));\n                break;\n            case \"bool\": gen\n                (\"if(typeof %s!==\\\"boolean\\\")\", ref)\n                    (\"return%j\", invalid(field, \"boolean\"));\n                break;\n            case \"string\": gen\n                (\"if(!util.isString(%s))\", ref)\n                    (\"return%j\", invalid(field, \"string\"));\n                break;\n            case \"bytes\": gen\n                (\"if(!(%s&&typeof %s.length===\\\"number\\\"||util.isString(%s)))\", ref, ref, ref)\n                    (\"return%j\", invalid(field, \"buffer\"));\n                break;\n        }\n    }\n    return gen;\n    /* eslint-enable no-unexpected-multiline */\n}\n\n/**\n * Generates a partial key verifier.\n * @param {Codegen} gen Codegen instance\n * @param {Field} field Reflected field\n * @param {string} ref Variable reference\n * @returns {Codegen} Codegen instance\n * @ignore\n */\nfunction genVerifyKey(gen, field, ref) {\n    /* eslint-disable no-unexpected-multiline */\n    switch (field.keyType) {\n        case \"int32\":\n        case \"uint32\":\n        case \"sint32\":\n        case \"fixed32\":\n        case \"sfixed32\": gen\n            (\"if(!util.key32Re.test(%s))\", ref)\n                (\"return%j\", invalid(field, \"integer key\"));\n            break;\n        case \"int64\":\n        case \"uint64\":\n        case \"sint64\":\n        case \"fixed64\":\n        case \"sfixed64\": gen\n            (\"if(!util.key64Re.test(%s))\", ref) // see comment above: x is ok, d is not\n                (\"return%j\", invalid(field, \"integer|Long key\"));\n            break;\n        case \"bool\": gen\n            (\"if(!util.key2Re.test(%s))\", ref)\n                (\"return%j\", invalid(field, \"boolean key\"));\n            break;\n    }\n    return gen;\n    /* eslint-enable no-unexpected-multiline */\n}\n\n/**\n * Generates a verifier specific to the specified message type.\n * @param {Type} mtype Message type\n * @returns {Codegen} Codegen instance\n */\nfunction verifier(mtype) {\n    /* eslint-disable no-unexpected-multiline */\n\n    var gen = util.codegen([\"m\"], mtype.name + \"$verify\")\n    (\"if(typeof m!==\\\"object\\\"||m===null)\")\n        (\"return%j\", \"object expected\");\n    var oneofs = mtype.oneofsArray,\n        seenFirstField = {};\n    if (oneofs.length) gen\n    (\"var p={}\");\n\n    for (var i = 0; i < /* initializes */ mtype.fieldsArray.length; ++i) {\n        var field = mtype._fieldsArray[i].resolve(),\n            ref   = \"m\" + util.safeProp(field.name);\n\n        if (field.optional) gen\n        (\"if(%s!=null&&m.hasOwnProperty(%j)){\", ref, field.name); // !== undefined && !== null\n\n        // map fields\n        if (field.map) { gen\n            (\"if(!util.isObject(%s))\", ref)\n                (\"return%j\", invalid(field, \"object\"))\n            (\"var k=Object.keys(%s)\", ref)\n            (\"for(var i=0;i<k.length;++i){\");\n                genVerifyKey(gen, field, \"k[i]\");\n                genVerifyValue(gen, field, i, ref + \"[k[i]]\")\n            (\"}\");\n\n        // repeated fields\n        } else if (field.repeated) { gen\n            (\"if(!Array.isArray(%s))\", ref)\n                (\"return%j\", invalid(field, \"array\"))\n            (\"for(var i=0;i<%s.length;++i){\", ref);\n                genVerifyValue(gen, field, i, ref + \"[i]\")\n            (\"}\");\n\n        // required or present fields\n        } else {\n            if (field.partOf) {\n                var oneofProp = util.safeProp(field.partOf.name);\n                if (seenFirstField[field.partOf.name] === 1) gen\n            (\"if(p%s===1)\", oneofProp)\n                (\"return%j\", field.partOf.name + \": multiple values\");\n                seenFirstField[field.partOf.name] = 1;\n                gen\n            (\"p%s=1\", oneofProp);\n            }\n            genVerifyValue(gen, field, i, ref);\n        }\n        if (field.optional) gen\n        (\"}\");\n    }\n    return gen\n    (\"return null\");\n    /* eslint-enable no-unexpected-multiline */\n}", "\"use strict\";\n\n/**\n * Wrappers for common types.\n * @type {Object.<string,IWrapper>}\n * @const\n */\nvar wrappers = exports;\n\nvar Message = require(21);\n\n/**\n * From object converter part of an {@link IWrapper}.\n * @typedef WrapperFromObjectConverter\n * @type {function}\n * @param {Object.<string,*>} object Plain object\n * @returns {Message<{}>} Message instance\n * @this Type\n */\n\n/**\n * To object converter part of an {@link IWrapper}.\n * @typedef WrapperToObjectConverter\n * @type {function}\n * @param {Message<{}>} message Message instance\n * @param {IConversionOptions} [options] Conversion options\n * @returns {Object.<string,*>} Plain object\n * @this Type\n */\n\n/**\n * Common type wrapper part of {@link wrappers}.\n * @interface IWrapper\n * @property {WrapperFromObjectConverter} [fromObject] From object converter\n * @property {WrapperToObjectConverter} [toObject] To object converter\n */\n\n// Custom wrapper for Any\nwrappers[\".google.protobuf.Any\"] = {\n\n    fromObject: function(object) {\n\n        // unwrap value type if mapped\n        if (object && object[\"@type\"]) {\n             // Only use fully qualified type name after the last '/'\n            var name = object[\"@type\"].substring(object[\"@type\"].lastIndexOf(\"/\") + 1);\n            var type = this.lookup(name);\n            /* istanbul ignore else */\n            if (type) {\n                // type_url does not accept leading \".\"\n                var type_url = object[\"@type\"].charAt(0) === \".\" ?\n                    object[\"@type\"].slice(1) : object[\"@type\"];\n                // type_url prefix is optional, but path seperator is required\n                if (type_url.indexOf(\"/\") === -1) {\n                    type_url = \"/\" + type_url;\n                }\n                return this.create({\n                    type_url: type_url,\n                    value: type.encode(type.fromObject(object)).finish()\n                });\n            }\n        }\n\n        return this.fromObject(object);\n    },\n\n    toObject: function(message, options) {\n\n        // Default prefix\n        var googleApi = \"type.googleapis.com/\";\n        var prefix = \"\";\n        var name = \"\";\n\n        // decode value if requested and unmapped\n        if (options && options.json && message.type_url && message.value) {\n            // Only use fully qualified type name after the last '/'\n            name = message.type_url.substring(message.type_url.lastIndexOf(\"/\") + 1);\n            // Separate the prefix used\n            prefix = message.type_url.substring(0, message.type_url.lastIndexOf(\"/\") + 1);\n            var type = this.lookup(name);\n            /* istanbul ignore else */\n            if (type)\n                message = type.decode(message.value);\n        }\n\n        // wrap value if unmapped\n        if (!(message instanceof this.ctor) && message instanceof Message) {\n            var object = message.$type.toObject(message, options);\n            var messageName = message.$type.fullName[0] === \".\" ?\n                message.$type.fullName.slice(1) : message.$type.fullName;\n            // Default to type.googleapis.com prefix if no prefix is used\n            if (prefix === \"\") {\n                prefix = googleApi;\n            }\n            name = prefix + messageName;\n            object[\"@type\"] = name;\n            return object;\n        }\n\n        return this.toObject(message, options);\n    }\n};\n", "\"use strict\";\nmodule.exports = Writer;\n\nvar util      = require(39);\n\nvar BufferWriter; // cyclic\n\nvar LongBits  = util.LongBits,\n    base64    = util.base64,\n    utf8      = util.utf8;\n\n/**\n * Constructs a new writer operation instance.\n * @classdesc Scheduled writer operation.\n * @constructor\n * @param {function(*, Uint8Array, number)} fn Function to call\n * @param {number} len Value byte length\n * @param {*} val Value to write\n * @ignore\n */\nfunction Op(fn, len, val) {\n\n    /**\n     * Function to call.\n     * @type {function(Uint8Array, number, *)}\n     */\n    this.fn = fn;\n\n    /**\n     * Value byte length.\n     * @type {number}\n     */\n    this.len = len;\n\n    /**\n     * Next operation.\n     * @type {Writer.Op|undefined}\n     */\n    this.next = undefined;\n\n    /**\n     * Value to write.\n     * @type {*}\n     */\n    this.val = val; // type varies\n}\n\n/* istanbul ignore next */\nfunction noop() {} // eslint-disable-line no-empty-function\n\n/**\n * Constructs a new writer state instance.\n * @classdesc Copied writer state.\n * @memberof Writer\n * @constructor\n * @param {Writer} writer Writer to copy state from\n * @ignore\n */\nfunction State(writer) {\n\n    /**\n     * Current head.\n     * @type {Writer.Op}\n     */\n    this.head = writer.head;\n\n    /**\n     * Current tail.\n     * @type {Writer.Op}\n     */\n    this.tail = writer.tail;\n\n    /**\n     * Current buffer length.\n     * @type {number}\n     */\n    this.len = writer.len;\n\n    /**\n     * Next state.\n     * @type {State|null}\n     */\n    this.next = writer.states;\n}\n\n/**\n * Constructs a new writer instance.\n * @classdesc Wire format writer using `Uint8Array` if available, otherwise `Array`.\n * @constructor\n */\nfunction Writer() {\n\n    /**\n     * Current length.\n     * @type {number}\n     */\n    this.len = 0;\n\n    /**\n     * Operations head.\n     * @type {Object}\n     */\n    this.head = new Op(noop, 0, 0);\n\n    /**\n     * Operations tail\n     * @type {Object}\n     */\n    this.tail = this.head;\n\n    /**\n     * Linked forked states.\n     * @type {Object|null}\n     */\n    this.states = null;\n\n    // When a value is written, the writer calculates its byte length and puts it into a linked\n    // list of operations to perform when finish() is called. This both allows us to allocate\n    // buffers of the exact required size and reduces the amount of work we have to do compared\n    // to first calculating over objects and then encoding over objects. In our case, the encoding\n    // part is just a linked list walk calling operations with already prepared values.\n}\n\nvar create = function create() {\n    return util.Buffer\n        ? function create_buffer_setup() {\n            return (Writer.create = function create_buffer() {\n                return new BufferWriter();\n            })();\n        }\n        /* istanbul ignore next */\n        : function create_array() {\n            return new Writer();\n        };\n};\n\n/**\n * Creates a new writer.\n * @function\n * @returns {BufferWriter|Writer} A {@link BufferWriter} when Buffers are supported, otherwise a {@link Writer}\n */\nWriter.create = create();\n\n/**\n * Allocates a buffer of the specified size.\n * @param {number} size Buffer size\n * @returns {Uint8Array} Buffer\n */\nWriter.alloc = function alloc(size) {\n    return new util.Array(size);\n};\n\n// Use Uint8Array buffer pool in the browser, just like node does with buffers\n/* istanbul ignore else */\nif (util.Array !== Array)\n    Writer.alloc = util.pool(Writer.alloc, util.Array.prototype.subarray);\n\n/**\n * Pushes a new operation to the queue.\n * @param {function(Uint8Array, number, *)} fn Function to call\n * @param {number} len Value byte length\n * @param {number} val Value to write\n * @returns {Writer} `this`\n * @private\n */\nWriter.prototype._push = function push(fn, len, val) {\n    this.tail = this.tail.next = new Op(fn, len, val);\n    this.len += len;\n    return this;\n};\n\nfunction writeByte(val, buf, pos) {\n    buf[pos] = val & 255;\n}\n\nfunction writeVarint32(val, buf, pos) {\n    while (val > 127) {\n        buf[pos++] = val & 127 | 128;\n        val >>>= 7;\n    }\n    buf[pos] = val;\n}\n\n/**\n * Constructs a new varint writer operation instance.\n * @classdesc Scheduled varint writer operation.\n * @extends Op\n * @constructor\n * @param {number} len Value byte length\n * @param {number} val Value to write\n * @ignore\n */\nfunction VarintOp(len, val) {\n    this.len = len;\n    this.next = undefined;\n    this.val = val;\n}\n\nVarintOp.prototype = Object.create(Op.prototype);\nVarintOp.prototype.fn = writeVarint32;\n\n/**\n * Writes an unsigned 32 bit value as a varint.\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.uint32 = function write_uint32(value) {\n    // here, the call to this.push has been inlined and a varint specific Op subclass is used.\n    // uint32 is by far the most frequently used operation and benefits significantly from this.\n    this.len += (this.tail = this.tail.next = new VarintOp(\n        (value = value >>> 0)\n                < 128       ? 1\n        : value < 16384     ? 2\n        : value < 2097152   ? 3\n        : value < 268435456 ? 4\n        :                     5,\n    value)).len;\n    return this;\n};\n\n/**\n * Writes a signed 32 bit value as a varint.\n * @function\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.int32 = function write_int32(value) {\n    return value < 0\n        ? this._push(writeVarint64, 10, LongBits.fromNumber(value)) // 10 bytes per spec\n        : this.uint32(value);\n};\n\n/**\n * Writes a 32 bit value as a varint, zig-zag encoded.\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.sint32 = function write_sint32(value) {\n    return this.uint32((value << 1 ^ value >> 31) >>> 0);\n};\n\nfunction writeVarint64(val, buf, pos) {\n    while (val.hi) {\n        buf[pos++] = val.lo & 127 | 128;\n        val.lo = (val.lo >>> 7 | val.hi << 25) >>> 0;\n        val.hi >>>= 7;\n    }\n    while (val.lo > 127) {\n        buf[pos++] = val.lo & 127 | 128;\n        val.lo = val.lo >>> 7;\n    }\n    buf[pos++] = val.lo;\n}\n\n/**\n * Writes an unsigned 64 bit value as a varint.\n * @param {Long|number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.uint64 = function write_uint64(value) {\n    var bits = LongBits.from(value);\n    return this._push(writeVarint64, bits.length(), bits);\n};\n\n/**\n * Writes a signed 64 bit value as a varint.\n * @function\n * @param {Long|number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.int64 = Writer.prototype.uint64;\n\n/**\n * Writes a signed 64 bit value as a varint, zig-zag encoded.\n * @param {Long|number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.sint64 = function write_sint64(value) {\n    var bits = LongBits.from(value).zzEncode();\n    return this._push(writeVarint64, bits.length(), bits);\n};\n\n/**\n * Writes a boolish value as a varint.\n * @param {boolean} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.bool = function write_bool(value) {\n    return this._push(writeByte, 1, value ? 1 : 0);\n};\n\nfunction writeFixed32(val, buf, pos) {\n    buf[pos    ] =  val         & 255;\n    buf[pos + 1] =  val >>> 8   & 255;\n    buf[pos + 2] =  val >>> 16  & 255;\n    buf[pos + 3] =  val >>> 24;\n}\n\n/**\n * Writes an unsigned 32 bit value as fixed 32 bits.\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.fixed32 = function write_fixed32(value) {\n    return this._push(writeFixed32, 4, value >>> 0);\n};\n\n/**\n * Writes a signed 32 bit value as fixed 32 bits.\n * @function\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.sfixed32 = Writer.prototype.fixed32;\n\n/**\n * Writes an unsigned 64 bit value as fixed 64 bits.\n * @param {Long|number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.fixed64 = function write_fixed64(value) {\n    var bits = LongBits.from(value);\n    return this._push(writeFixed32, 4, bits.lo)._push(writeFixed32, 4, bits.hi);\n};\n\n/**\n * Writes a signed 64 bit value as fixed 64 bits.\n * @function\n * @param {Long|number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.sfixed64 = Writer.prototype.fixed64;\n\n/**\n * Writes a float (32 bit).\n * @function\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.float = function write_float(value) {\n    return this._push(util.float.writeFloatLE, 4, value);\n};\n\n/**\n * Writes a double (64 bit float).\n * @function\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.double = function write_double(value) {\n    return this._push(util.float.writeDoubleLE, 8, value);\n};\n\nvar writeBytes = util.Array.prototype.set\n    ? function writeBytes_set(val, buf, pos) {\n        buf.set(val, pos); // also works for plain array values\n    }\n    /* istanbul ignore next */\n    : function writeBytes_for(val, buf, pos) {\n        for (var i = 0; i < val.length; ++i)\n            buf[pos + i] = val[i];\n    };\n\n/**\n * Writes a sequence of bytes.\n * @param {Uint8Array|string} value Buffer or base64 encoded string to write\n * @returns {Writer} `this`\n */\nWriter.prototype.bytes = function write_bytes(value) {\n    var len = value.length >>> 0;\n    if (!len)\n        return this._push(writeByte, 1, 0);\n    if (util.isString(value)) {\n        var buf = Writer.alloc(len = base64.length(value));\n        base64.decode(value, buf, 0);\n        value = buf;\n    }\n    return this.uint32(len)._push(writeBytes, len, value);\n};\n\n/**\n * Writes a string.\n * @param {string} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.string = function write_string(value) {\n    var len = utf8.length(value);\n    return len\n        ? this.uint32(len)._push(utf8.write, len, value)\n        : this._push(writeByte, 1, 0);\n};\n\n/**\n * Forks this writer's state by pushing it to a stack.\n * Calling {@link Writer#reset|reset} or {@link Writer#ldelim|ldelim} resets the writer to the previous state.\n * @returns {Writer} `this`\n */\nWriter.prototype.fork = function fork() {\n    this.states = new State(this);\n    this.head = this.tail = new Op(noop, 0, 0);\n    this.len = 0;\n    return this;\n};\n\n/**\n * Resets this instance to the last state.\n * @returns {Writer} `this`\n */\nWriter.prototype.reset = function reset() {\n    if (this.states) {\n        this.head   = this.states.head;\n        this.tail   = this.states.tail;\n        this.len    = this.states.len;\n        this.states = this.states.next;\n    } else {\n        this.head = this.tail = new Op(noop, 0, 0);\n        this.len  = 0;\n    }\n    return this;\n};\n\n/**\n * Resets to the last state and appends the fork state's current write length as a varint followed by its operations.\n * @returns {Writer} `this`\n */\nWriter.prototype.ldelim = function ldelim() {\n    var head = this.head,\n        tail = this.tail,\n        len  = this.len;\n    this.reset().uint32(len);\n    if (len) {\n        this.tail.next = head.next; // skip noop\n        this.tail = tail;\n        this.len += len;\n    }\n    return this;\n};\n\n/**\n * Finishes the write operation.\n * @returns {Uint8Array} Finished buffer\n */\nWriter.prototype.finish = function finish() {\n    var head = this.head.next, // skip noop\n        buf  = this.constructor.alloc(this.len),\n        pos  = 0;\n    while (head) {\n        head.fn(head.val, buf, pos);\n        pos += head.len;\n        head = head.next;\n    }\n    // this.head = this.tail = null;\n    return buf;\n};\n\nWriter._configure = function(BufferWriter_) {\n    BufferWriter = BufferWriter_;\n    Writer.create = create();\n    BufferWriter._configure();\n};\n", "\"use strict\";\nmodule.exports = <PERSON><PERSON>erWriter;\n\n// extends Writer\nvar Writer = require(42);\n(BufferWriter.prototype = Object.create(Writer.prototype)).constructor = BufferWriter;\n\nvar util = require(39);\n\n/**\n * Constructs a new buffer writer instance.\n * @classdesc Wire format writer using node buffers.\n * @extends Writer\n * @constructor\n */\nfunction BufferWriter() {\n    Writer.call(this);\n}\n\nBufferWriter._configure = function () {\n    /**\n     * Allocates a buffer of the specified size.\n     * @function\n     * @param {number} size Buffer size\n     * @returns {Buffer} Buffer\n     */\n    BufferWriter.alloc = util._Buffer_allocUnsafe;\n\n    BufferWriter.writeBytesBuffer = util.Buffer && util.Buffer.prototype instanceof Uint8Array && util.Buffer.prototype.set.name === \"set\"\n        ? function writeBytesBuffer_set(val, buf, pos) {\n          buf.set(val, pos); // faster than copy (requires node >= 4 where Buffers extend Uint8Array and set is properly inherited)\n          // also works for plain array values\n        }\n        /* istanbul ignore next */\n        : function writeBytesBuffer_copy(val, buf, pos) {\n          if (val.copy) // Buffer values\n            val.copy(buf, pos, 0, val.length);\n          else for (var i = 0; i < val.length;) // plain array values\n            buf[pos++] = val[i++];\n        };\n};\n\n\n/**\n * @override\n */\nBufferWriter.prototype.bytes = function write_bytes_buffer(value) {\n    if (util.isString(value))\n        value = util._Buffer_from(value, \"base64\");\n    var len = value.length >>> 0;\n    this.uint32(len);\n    if (len)\n        this._push(BufferWriter.writeBytesBuffer, len, value);\n    return this;\n};\n\nfunction writeStringBuffer(val, buf, pos) {\n    if (val.length < 40) // plain js is faster for short strings (probably due to redundant assertions)\n        util.utf8.write(val, buf, pos);\n    else if (buf.utf8Write)\n        buf.utf8Write(val, pos);\n    else\n        buf.write(val, pos);\n}\n\n/**\n * @override\n */\nBufferWriter.prototype.string = function write_string_buffer(value) {\n    var len = util.Buffer.byteLength(value);\n    this.uint32(len);\n    if (len)\n        this._push(writeStringBuffer, len, value);\n    return this;\n};\n\n\n/**\n * Finishes the write operation.\n * @name BufferWriter#finish\n * @function\n * @returns {Buffer} Finished buffer\n */\n\nBufferWriter._configure();\n"], "sourceRoot": "."}