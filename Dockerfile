# PharmAccounts Monorepo Dockerfile
# This builds both frontend and backend into a single deployable container

FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
WORKDIR /app

# Copy package files
COPY package*.json ./
COPY backend/package*.json ./backend/
COPY frontend/package*.json ./frontend/

# Install dependencies
RUN npm ci --only=production && npm cache clean --force

# Build the frontend
FROM base AS frontend-builder
WORKDIR /app

# Copy frontend source
COPY frontend/ ./frontend/
COPY package*.json ./

# Install frontend dependencies and build
RUN cd frontend && npm ci
RUN cd frontend && npm run build

# Build the final image
FROM base AS runner
WORKDIR /app

# Create non-root user
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 pharmaccounts

# Copy backend source
COPY backend/ ./backend/

# Copy built frontend assets to backend static directory
COPY --from=frontend-builder /app/frontend/.next ./backend/static/.next
COPY --from=frontend-builder /app/frontend/public ./backend/static/public

# Copy production dependencies
COPY --from=deps /app/node_modules ./node_modules
COPY --from=deps /app/backend/node_modules ./backend/node_modules

# Set environment variables
ENV NODE_ENV=production
ENV PORT=3000

# Change ownership to non-root user
RUN chown -R pharmaccounts:nodejs /app
USER pharmaccounts

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:3000/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# Start the application
WORKDIR /app/backend
CMD ["npm", "start"]