/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  eslint: {
    ignoreDuringBuilds: true,
  },

  // Output configuration for production builds
  output: process.env.NODE_ENV === 'production' ? 'standalone' : undefined,

  // Disable image optimization for static export compatibility
  images: {
    unoptimized: true,
  },

  // Asset prefix for production (if needed)
  assetPrefix: process.env.NODE_ENV === 'production' ? '' : '',

  // Trailing slash configuration
  trailingSlash: false,

  // Add rewrites to proxy API requests to the backend during development
  async rewrites() {
    // Only apply rewrites in development
    if (process.env.NODE_ENV === 'development') {
      return [
        {
          source: '/api/:path*',
          destination: 'http://localhost:3000/api/:path*', // Proxy to Backend
        },
      ];
    }
    return [];
  },

  // Headers for better caching in production
  async headers() {
    return [
      {
        source: '/_next/static/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
      {
        source: '/static/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=86400',
          },
        ],
      },
    ];
  },
};

module.exports = nextConfig;