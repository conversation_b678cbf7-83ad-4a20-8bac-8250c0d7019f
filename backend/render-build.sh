#!/usr/bin/env bash
# PharmAccounts Monorepo Build Script for Render.com
# This script builds both frontend and backend for production deployment

# exit on error
set -o errexit

echo "🚀 Starting PharmAccounts Monorepo Build Process..."

# Go to project root
cd /opt/render/project/src

# Install root dependencies
echo "📦 Installing root dependencies..."
npm install

# Build frontend and copy to backend
echo "🏗️ Building frontend and backend..."
node scripts/build.js

echo "✅ Build completed successfully!"
echo "📁 Frontend assets copied to backend/static/"
echo "🚀 Ready for deployment!"

# Note: Puppeteer installation is now handled in the unified build script
