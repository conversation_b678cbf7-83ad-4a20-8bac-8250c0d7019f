(()=>{var e={};e.id=4520,e.ids=[4520],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},39914:(e,t,r)=>{Promise.resolve().then(r.bind(r,94934))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69488:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var n=r(60687),s=r(43210),a=r(16189),i=r(29523),o=r(89667),l=r(62185);function d(){let[e,t]=(0,s.useState)(""),[r,d]=(0,s.useState)(""),[c,u]=(0,s.useState)(!1),[p,m]=(0,s.useState)(null),[f,x]=(0,s.useState)(null),[h,g]=(0,s.useState)(!1);(0,a.useRouter)();let v=async t=>{t.preventDefault(),m(null),u(!0);try{let t=await l.ZQ.login(e,r);t.success?window.location.href="/orders":m(t.error||"Login failed")}catch(e){m(e instanceof Error?e.message:"Login failed")}finally{u(!1)}};return(0,n.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,n.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,n.jsx)("div",{children:(0,n.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Sign in to your account"})}),(0,n.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:v,children:[(0,n.jsxs)("div",{className:"rounded-md shadow-sm -space-y-px",children:[(0,n.jsx)("div",{children:(0,n.jsx)(o.p,{type:"email",value:e,onChange:e=>t(e.target.value),required:!0,className:"mb-2",placeholder:"Email address"})}),(0,n.jsx)("div",{children:(0,n.jsx)(o.p,{type:"password",value:r,onChange:e=>d(e.target.value),required:!0,placeholder:"Password"})}),(0,n.jsx)("div",{className:"flex items-center justify-between mt-2",children:(0,n.jsx)("button",{type:"button",className:"text-sm text-blue-600 hover:underline focus:outline-none",onClick:async()=>{x(null),g(!0);try{let t=await l.ZQ.resetPassword(e);t.success?x("Password reset email sent. Please check your inbox."):x(t.error||"Failed to send password reset email.")}catch(e){x("Failed to send password reset email.")}finally{g(!1)}},disabled:!e||h,"aria-disabled":!e||h,children:h?"Sending reset email...":"Forgot my password?"})}),f&&(0,n.jsx)("div",{className:"text-sm text-gray-700 mt-2 text-center",children:f})]}),p&&(0,n.jsx)("div",{className:"text-red-500 text-sm text-center",children:p}),(0,n.jsx)("div",{children:(0,n.jsx)(i.$,{type:"submit",className:"w-full",disabled:c,children:c?"Signing in...":"Sign in"})})]})]})})}},76926:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return l}});let n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=s(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var o=a?Object.getOwnPropertyDescriptor(e,i):null;o&&(o.get||o.set)?Object.defineProperty(n,i,o):n[i]=e[i]}return n.default=e,r&&r.set(e,n),n}(r(61120));function s(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(s=function(e){return e?r:t})(e)}let a={current:null},i="function"==typeof n.cache?n.cache:e=>e,o=console.warn;function l(e){return function(...t){o(e(...t))}}i(e=>{try{o(a.current)}finally{a.current=null}})},79551:e=>{"use strict";e.exports=require("url")},79666:(e,t,r)=>{Promise.resolve().then(r.bind(r,69488))},82666:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var n=r(65239),s=r(48088),a=r(88170),i=r.n(a),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,94934)),"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/login/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/login/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},89667:(e,t,r)=>{"use strict";r.d(t,{p:()=>a});var n=r(60687);r(43210);var s=r(4780);function a({className:e,type:t,...r}){return(0,n.jsx)("input",{type:t,"data-slot":"input",className:(0,s.cn)("border-input file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},94934:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/login/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/login/page.tsx","default")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[4447,4825,1658,7924,8422],()=>r(82666));module.exports=n})();