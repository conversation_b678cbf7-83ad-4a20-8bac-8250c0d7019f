(()=>{var e={};e.id=7758,e.ids=[7758],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96120:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>u,serverHooks:()=>d,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>l});var s={};r.r(s),r.d(s,{POST:()=>c});var o=r(96559),n=r(48088),a=r(37719),i=r(32190);async function c(e){try{let t,r,{email:s,password:o}=await e.json(),n="http://localhost:3000";console.log("Attempting to connect to backend at:",n);try{t=await fetch(`${n}/api/auth/login`,{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify({email:s,password:o})}),console.log("Backend response status:",t.status),r=await t.json(),console.log("Backend response data:",JSON.stringify(r))}catch(t){console.error("Fetch error details:",t);let e=t instanceof Error?t.message:"Unknown error";throw Error(`Failed to connect to backend: ${e}`)}if(r.success&&r.token){let e=i.NextResponse.json(r,{status:t.status});return e.cookies.set("auth-token",r.token,{path:"/",maxAge:86400,httpOnly:!0,sameSite:"lax"}),e}return i.NextResponse.json(r,{status:t.status})}catch(e){return console.error("Login error:",e),i.NextResponse.json({success:!1,error:"Authentication failed"},{status:500})}}let u=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/auth/login/route",pathname:"/api/auth/login",filename:"route",bundlePath:"app/api/auth/login/route"},resolvedPagePath:"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/api/auth/login/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:p,workUnitAsyncStorage:l,serverHooks:d}=u;function h(){return(0,a.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:l})}},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580],()=>r(96120));module.exports=s})();