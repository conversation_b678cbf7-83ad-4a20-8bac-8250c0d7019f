(()=>{var e={};e.id=2444,e.ids=[2444],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70905:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>g,routeModule:()=>p,serverHooks:()=>l,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>h});var r={};s.r(r),s.d(r,{GET:()=>u,PUT:()=>c});var a=s(96559),o=s(48088),n=s(37719),i=s(32190);async function u(e){try{let t=e.cookies.get("auth-token")?.value;if(!t)return i.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});let s=await fetch("http://localhost:3000/api/auth/settings",{method:"GET",headers:{Accept:"application/json",Authorization:`Bearer ${t}`}}),r=await s.json();return i.NextResponse.json(r,{status:s.status})}catch(e){return console.error("Get settings error:",e),i.NextResponse.json({success:!1,error:"Failed to fetch settings"},{status:500})}}async function c(e){try{let t=e.cookies.get("auth-token")?.value;if(!t)return i.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});let s=await e.json(),r=await fetch("http://localhost:3000/api/auth/settings",{method:"PUT",headers:{"Content-Type":"application/json",Accept:"application/json",Authorization:`Bearer ${t}`},body:JSON.stringify(s)}),a=await r.json();return i.NextResponse.json(a,{status:r.status})}catch(e){return console.error("Update settings error:",e),i.NextResponse.json({success:!1,error:"Failed to update settings"},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/auth/settings/route",pathname:"/api/auth/settings",filename:"route",bundlePath:"app/api/auth/settings/route"},resolvedPagePath:"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/api/auth/settings/route.ts",nextConfigOutput:"standalone",userland:r}),{workAsyncStorage:d,workUnitAsyncStorage:h,serverHooks:l}=p;function g(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:h})}},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,580],()=>s(70905));module.exports=r})();