(()=>{var e={};e.id=6478,e.ids=[6478],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},36115:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>i,serverHooks:()=>l,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>c});var s={};r.r(s),r.d(s,{POST:()=>u});var a=r(96559),o=r(48088),n=r(37719),p=r(32190);async function u(e){let t=await e.json();try{let e=await fetch("http://localhost:3000/api/auth/reset-password",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)}),r=await e.json();return p.NextResponse.json(r,{status:e.status})}catch(e){return p.NextResponse.json({success:!1,error:"Failed to proxy request"},{status:500})}}let i=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/auth/reset-password/route",pathname:"/api/auth/reset-password",filename:"route",bundlePath:"app/api/auth/reset-password/route"},resolvedPagePath:"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/api/auth/reset-password/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:c,serverHooks:l}=i;function h(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:c})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580],()=>r(36115));module.exports=s})();