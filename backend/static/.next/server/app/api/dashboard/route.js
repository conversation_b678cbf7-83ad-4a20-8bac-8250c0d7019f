(()=>{var e={};e.id=4618,e.ids=[4618],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},22491:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>h,routeModule:()=>p,serverHooks:()=>m,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>u});var r={};s.r(r),s.d(r,{GET:()=>d});var i=s(96559),o=s(48088),a=s(37719),n=s(32190),c=s(22971);async function d(e){try{return n.NextResponse.json(c.Kq)}catch(e){return console.error("Error fetching dashboard stats:",e),n.NextResponse.json({error:"Failed to fetch dashboard stats"},{status:500})}}let p=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/dashboard/route",pathname:"/api/dashboard",filename:"route",bundlePath:"app/api/dashboard/route"},resolvedPagePath:"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/api/dashboard/route.ts",nextConfigOutput:"standalone",userland:r}),{workAsyncStorage:l,workUnitAsyncStorage:u,serverHooks:m}=p;function h(){return(0,a.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:u})}},22971:(e,t,s)=>{"use strict";s.d(t,{Kq:()=>i,nT:()=>r});let r=[{id:"step-1",title:"Retrieve Orders",description:"Retrieve orders from Drug Comparison",status:"completed",link:"/orders",helpText:"This step retrieves all orders from Drug Comparison. You can filter orders by date and supplier."},{id:"step-2",title:"Generate Checklist",description:"Create checklist items from orders",status:"in-progress",link:"/checklist/generate",helpText:"Select orders to generate checklist items. You can filter orders by date and supplier before generating."},{id:"step-3",title:"Process Checklist",description:"Mark items as received, incorrect, or missing",status:"not-started",link:"/checklist",helpText:"Process each item in the checklist by marking it as received, incorrect, or missing. You can also add notes for each item."},{id:"step-4",title:"Process Invoices",description:"Upload and process invoices",status:"not-started",link:"/invoices",helpText:"Upload invoice PDFs or process invoices from emails. The system will extract invoice data automatically."},{id:"step-5",title:"Reconcile Invoices",description:"Compare invoices with orders",status:"not-started",link:"/reconciliation",helpText:"Compare invoices with orders to identify discrepancies. You can resolve discrepancies by adding notes and marking them as resolved."},{id:"step-6",title:"Process Statements",description:"Upload and process statements",status:"not-started",link:"/statements",helpText:"Upload statement PDFs or process statements from emails. The system will extract statement data automatically."},{id:"step-7",title:"Reconcile Statements",description:"Compare statements with invoices",status:"not-started",link:"/statements/reconcile",helpText:"Compare statements with invoices to identify discrepancies. You can resolve discrepancies by adding notes and marking them as resolved."},{id:"step-8",title:"Create Credit Requests",description:"Request credit for missing or incorrect items",status:"not-started",link:"/credit-requests/create",helpText:"Create credit requests for missing or incorrect items. You can select items from the checklist and add details for each request."},{id:"step-9",title:"Generate Reports",description:"Generate reports for reconciliation",status:"not-started",link:"/reports",helpText:"Generate reports for reconciliation. You can select the report type, date range, and other parameters."}],i={orders:{total:45,pending:12,processed:33},checklist:{total:156,pending:42,received:98,incorrect:8,missing:8},invoices:{total:28,reconciled:22,unreconciled:6},statements:{total:3,reconciled:1,unreconciled:2},creditRequests:{total:12,pending:3,sent:5,approved:3,rejected:1}}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,580],()=>s(22491));module.exports=r})();