(()=>{var e={};e.id=5936,e.ids=[5936],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},62087:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>g,routeModule:()=>p,serverHooks:()=>h,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var s={};t.r(s),t.d(s,{POST:()=>c});var a=t(96559),o=t(48088),n=t(37719),i=t(32190),u=t(44999);async function c(e){try{let r=await (0,u.b3)(),t=await r.get("Authorization");if(!t)return i.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});let s=await e.json(),a=await fetch("http://localhost:3000/api/checklist/generate",{method:"POST",headers:{Authorization:t,"Content-Type":"application/json"},body:JSON.stringify(s)}),o=await a.json();if(!a.ok)return i.NextResponse.json({error:o.error||{message:"Failed to generate checklist from order"}},{status:a.status});return i.NextResponse.json(o)}catch(e){return console.error("Error generating checklist from order:",e),i.NextResponse.json({error:{message:"Internal server error"}},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/checklist/generate/route",pathname:"/api/checklist/generate",filename:"route",bundlePath:"app/api/checklist/generate/route"},resolvedPagePath:"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/api/checklist/generate/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:h}=p;function g(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,4999],()=>t(62087));module.exports=s})();