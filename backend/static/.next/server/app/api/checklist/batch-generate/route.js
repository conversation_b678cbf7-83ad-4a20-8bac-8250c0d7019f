(()=>{var e={};e.id=7153,e.ids=[7153],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},23416:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>p,serverHooks:()=>l,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>h});var s={};r.r(s),r.d(s,{POST:()=>u});var a=r(96559),n=r(48088),o=r(37719),i=r(32190),c=r(44999);async function u(e){try{let t=await (0,c.b3)(),r=await t.get("Authorization");if(!r)return i.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});let s=await e.json(),a=await fetch("http://localhost:3000/api/checklist/batch-generate",{method:"POST",headers:{Authorization:r,"Content-Type":"application/json"},body:JSON.stringify(s)}),n=await a.json();if(!a.ok)return i.NextResponse.json({error:n.error||{message:"Failed to batch generate checklist items"}},{status:a.status});return i.NextResponse.json(n)}catch(e){return console.error("Error batch generating checklist items:",e),i.NextResponse.json({error:{message:"Internal server error"}},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/checklist/batch-generate/route",pathname:"/api/checklist/batch-generate",filename:"route",bundlePath:"app/api/checklist/batch-generate/route"},resolvedPagePath:"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/api/checklist/batch-generate/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:h,serverHooks:l}=p;function g(){return(0,o.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:h})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,4999],()=>r(23416));module.exports=s})();