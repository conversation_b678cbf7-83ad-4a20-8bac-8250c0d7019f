(()=>{var e={};e.id=9011,e.ids=[9011],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},26751:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>d,serverHooks:()=>g,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{GET:()=>p,POST:()=>l});var a=r(96559),o=r(48088),n=r(37719),i=r(32190),c=r(44999);let u="http://localhost:3000";async function p(e){try{let t=e.cookies.get("auth-token")?.value,r=await (0,c.b3)(),s=await r.get("Authorization"),a=t?`Bearer ${t}`:s;if(!a)return i.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});let o=e.nextUrl.searchParams,n=o.get("orderId"),p=o.get("status"),l=o.get("supplier"),d=o.get("startDate"),h=o.get("endDate"),m=new URLSearchParams;n&&m.append("orderId",n),p&&m.append("status",p),l&&m.append("supplier",l),d&&m.append("startDate",d),h&&m.append("endDate",h);let g=await fetch(`${u}/api/checklist/items${m.toString()?`?${m.toString()}`:""}`,{headers:{Authorization:a,"Content-Type":"application/json"}}),x=await g.json();if(!g.ok)return i.NextResponse.json({error:x.error||{message:"Failed to fetch checklist items"}},{status:g.status});return i.NextResponse.json(x)}catch(e){return console.error("Error fetching checklist items:",e),i.NextResponse.json({error:{message:"Internal server error"}},{status:500})}}async function l(e){try{let t=e.cookies.get("auth-token")?.value,r=await (0,c.b3)(),s=await r.get("Authorization"),a=t?`Bearer ${t}`:s;if(!a)return i.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});let o=await e.json(),n=await fetch(`${u}/api/checklist/items`,{method:"POST",headers:{Authorization:a,"Content-Type":"application/json"},body:JSON.stringify(o)}),p=await n.json();if(!n.ok)return i.NextResponse.json({error:p.error||{message:"Failed to create checklist items"}},{status:n.status});return i.NextResponse.json(p)}catch(e){return console.error("Error creating checklist items:",e),i.NextResponse.json({error:{message:"Internal server error"}},{status:500})}}let d=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/checklist/items/route",pathname:"/api/checklist/items",filename:"route",bundlePath:"app/api/checklist/items/route"},resolvedPagePath:"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/api/checklist/items/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:h,workUnitAsyncStorage:m,serverHooks:g}=d;function x(){return(0,n.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:m})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,4999],()=>r(26751));module.exports=s})();