(()=>{var e={};e.id=223,e.ids=[223],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66051:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>m,routeModule:()=>p,serverHooks:()=>h,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var s={};r.r(s),r.d(s,{PATCH:()=>c});var a=r(96559),i=r(48088),o=r(37719),n=r(32190),u=r(44999);async function c(e,{params:t}){try{let r=await (0,u.b3)(),s=await r.get("Authorization");if(!s)return n.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});let a=(await t).id,i=await e.json(),o=await fetch(`http://localhost:3000/api/checklist/items/${a}`,{method:"PATCH",headers:{Authorization:s,"Content-Type":"application/json"},body:JSON.stringify(i)}),c=await o.json();if(!o.ok)return n.NextResponse.json({error:c.error||{message:"Failed to update checklist item"}},{status:o.status});return n.NextResponse.json(c)}catch(e){return console.error("Error updating checklist item:",e),n.NextResponse.json({error:{message:"Internal server error"}},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/checklist/items/[id]/route",pathname:"/api/checklist/items/[id]",filename:"route",bundlePath:"app/api/checklist/items/[id]/route"},resolvedPagePath:"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/api/checklist/items/[id]/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:h}=p;function m(){return(0,o.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},78335:()=>{},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,4999],()=>r(66051));module.exports=s})();