(()=>{var e={};e.id=1481,e.ids=[1481],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},40885:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>k,routeModule:()=>p,serverHooks:()=>h,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>d});var s={};r.r(s),r.d(s,{POST:()=>c});var a=r(96559),o=r(48088),n=r(37719),i=r(32190),u=r(44999);async function c(e){try{let t=await (0,u.b3)(),r=await t.get("Authorization");if(!r)return i.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});let s=await e.json(),a=await fetch("http://localhost:3000/api/checklist/bulk",{method:"POST",headers:{Authorization:r,"Content-Type":"application/json"},body:JSON.stringify(s)}),o=await a.json();if(!a.ok)return i.NextResponse.json({error:o.error||{message:"Failed to bulk update checklist items"}},{status:a.status});return i.NextResponse.json(o)}catch(e){return console.error("Error bulk updating checklist items:",e),i.NextResponse.json({error:{message:"Internal server error"}},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/checklist/bulk/route",pathname:"/api/checklist/bulk",filename:"route",bundlePath:"app/api/checklist/bulk/route"},resolvedPagePath:"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/api/checklist/bulk/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:d,serverHooks:h}=p;function k(){return(0,n.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:d})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,4999],()=>r(40885));module.exports=s})();