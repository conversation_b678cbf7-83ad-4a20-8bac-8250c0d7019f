(()=>{var e={};e.id=6039,e.ids=[6039],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31128:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>c,serverHooks:()=>m,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var s={};r.r(s),r.d(s,{GET:()=>p});var a=r(96559),n=r(48088),o=r(37719),i=r(32190),u=r(44999);async function p(e){try{let t=await (0,u.b3)(),r=await t.get("Authorization");if(!r)return i.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});let s=e.nextUrl.searchParams,a=s.get("orderId"),n=s.get("supplier"),o=s.get("startDate"),p=s.get("endDate"),c=new URLSearchParams;a&&c.append("orderId",a),n&&c.append("supplier",n),o&&c.append("startDate",o),p&&c.append("endDate",p);let d=await fetch(`http://localhost:3000/api/checklist/summary${c.toString()?`?${c.toString()}`:""}`,{headers:{Authorization:r,"Content-Type":"application/json"}}),l=await d.json();if(!d.ok)return i.NextResponse.json({error:l.error||{message:"Failed to fetch checklist summary"}},{status:d.status});return i.NextResponse.json(l)}catch(e){return console.error("Error fetching checklist summary:",e),i.NextResponse.json({error:{message:"Internal server error"}},{status:500})}}let c=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/checklist/summary/route",pathname:"/api/checklist/summary",filename:"route",bundlePath:"app/api/checklist/summary/route"},resolvedPagePath:"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/api/checklist/summary/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:m}=c;function h(){return(0,o.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,4999],()=>r(31128));module.exports=s})();