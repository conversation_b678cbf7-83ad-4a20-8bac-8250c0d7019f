(()=>{var e={};e.id=9513,e.ids=[9513],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},22971:(e,t,s)=>{"use strict";s.d(t,{Kq:()=>i,nT:()=>r});let r=[{id:"step-1",title:"Retrieve Orders",description:"Retrieve orders from Drug Comparison",status:"completed",link:"/orders",helpText:"This step retrieves all orders from Drug Comparison. You can filter orders by date and supplier."},{id:"step-2",title:"Generate Checklist",description:"Create checklist items from orders",status:"in-progress",link:"/checklist/generate",helpText:"Select orders to generate checklist items. You can filter orders by date and supplier before generating."},{id:"step-3",title:"Process Checklist",description:"Mark items as received, incorrect, or missing",status:"not-started",link:"/checklist",helpText:"Process each item in the checklist by marking it as received, incorrect, or missing. You can also add notes for each item."},{id:"step-4",title:"Process Invoices",description:"Upload and process invoices",status:"not-started",link:"/invoices",helpText:"Upload invoice PDFs or process invoices from emails. The system will extract invoice data automatically."},{id:"step-5",title:"Reconcile Invoices",description:"Compare invoices with orders",status:"not-started",link:"/reconciliation",helpText:"Compare invoices with orders to identify discrepancies. You can resolve discrepancies by adding notes and marking them as resolved."},{id:"step-6",title:"Process Statements",description:"Upload and process statements",status:"not-started",link:"/statements",helpText:"Upload statement PDFs or process statements from emails. The system will extract statement data automatically."},{id:"step-7",title:"Reconcile Statements",description:"Compare statements with invoices",status:"not-started",link:"/statements/reconcile",helpText:"Compare statements with invoices to identify discrepancies. You can resolve discrepancies by adding notes and marking them as resolved."},{id:"step-8",title:"Create Credit Requests",description:"Request credit for missing or incorrect items",status:"not-started",link:"/credit-requests/create",helpText:"Create credit requests for missing or incorrect items. You can select items from the checklist and add details for each request."},{id:"step-9",title:"Generate Reports",description:"Generate reports for reconciliation",status:"not-started",link:"/reports",helpText:"Generate reports for reconciliation. You can select the report type, date range, and other parameters."}],i={orders:{total:45,pending:12,processed:33},checklist:{total:156,pending:42,received:98,incorrect:8,missing:8},invoices:{total:28,reconciled:22,unreconciled:6},statements:{total:3,reconciled:1,unreconciled:2},creditRequests:{total:12,pending:3,sent:5,approved:3,rejected:1}}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34557:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>h,routeModule:()=>p,serverHooks:()=>m,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>u});var r={};s.r(r),s.d(r,{POST:()=>d});var i=s(96559),o=s(48088),n=s(37719),a=s(32190);let c=[...s(22971).nT];async function d(e){let t=e.nextUrl.pathname.match(/\/steps\/([^\/]+)\/skip/),s=t?t[1]:null;try{console.log("POST request path id:",s);let e=c.findIndex(e=>e.id===s);if(-1===e)return a.NextResponse.json({error:"Workflow step not found"},{status:404});return c[e]={...c[e],status:"skipped"},e<c.length-1&&(c[e+1]={...c[e+1],status:"in-progress"}),a.NextResponse.json({success:!0,message:`Workflow step ${s} skipped successfully`,nextStep:e<c.length-1?c[e+1]:null})}catch(e){return console.error("Error skipping workflow step:",e),a.NextResponse.json({error:"Failed to skip workflow step"},{status:500})}}let p=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/workflow/steps/[id]/skip/route",pathname:"/api/workflow/steps/[id]/skip",filename:"route",bundlePath:"app/api/workflow/steps/[id]/skip/route"},resolvedPagePath:"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/api/workflow/steps/[id]/skip/route.ts",nextConfigOutput:"standalone",userland:r}),{workAsyncStorage:l,workUnitAsyncStorage:u,serverHooks:m}=p;function h(){return(0,n.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:u})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,580],()=>s(34557));module.exports=r})();