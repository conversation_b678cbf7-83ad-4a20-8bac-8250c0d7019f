(()=>{var e={};e.id=7991,e.ids=[7991],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},39634:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>p,serverHooks:()=>l,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>d});var s={};t.r(s),t.d(s,{GET:()=>u});var o=t(96559),a=t(48088),n=t(37719),i=t(32190);async function u(e){let r=e.cookies.get("auth-token"),t=r?.value;if(!t)return i.NextResponse.json({error:"Not authenticated"},{status:401});let s=await fetch("http://localhost:3000/api/scrape/orders",{headers:{Authorization:t.startsWith("Bearer ")?t:`Bearer ${t}`,Accept:"application/json"},cache:"no-store"}),o="";if(!s.ok){try{o=await s.text()}catch{o="Unknown backend error"}return i.NextResponse.json({error:"Failed to fetch orders",backend:o,status:s.status},{status:s.status})}let a=await s.json();return i.NextResponse.json(a)}let p=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/overview-orders/route",pathname:"/api/overview-orders",filename:"route",bundlePath:"app/api/overview-orders/route"},resolvedPagePath:"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/api/overview-orders/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:c,workUnitAsyncStorage:d,serverHooks:l}=p;function h(){return(0,n.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:d})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580],()=>t(39634));module.exports=s})();