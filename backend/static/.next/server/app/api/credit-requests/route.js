(()=>{var e={};e.id=2652,e.ids=[2652],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8577:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>p,serverHooks:()=>g,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{GET:()=>u,POST:()=>c});var i=r(96559),a=r(48088),n=r(37719),o=r(32190);let d=[{id:"CR001",orderId:"ORD123",invoiceId:"INV456",supplier:"AAH",status:"sent",requestDate:new Date("2025-03-20"),items:[{productName:"Paracetamol 500mg Tablets",pipCode:"PIP123456",quantity:2,reason:"missing",details:"Items missing from delivery",originalUnitPrice:2.99,originalTotal:5.98}],emailHistory:[{id:"EM001",sentDate:new Date("2025-03-20T10:00:00"),to:["<EMAIL>"],subject:"Credit Request - Order ORD123",content:"Please issue credit note for missing items...",status:"sent"}],notes:"First request sent"},{id:"CR002",orderId:"ORD124",invoiceId:"INV457",supplier:"Phoenix",status:"approved",requestDate:new Date("2025-03-19"),responseDate:new Date("2025-03-21"),creditNoteNumber:"CN789",items:[{productName:"Ibuprofen 200mg Tablets",pipCode:"PIP789012",quantity:1,reason:"damaged",details:"Box crushed during delivery",originalUnitPrice:3.99,originalTotal:3.99,images:["https://example.com/damage1.jpg"]}],emailHistory:[{id:"EM002",sentDate:new Date("2025-03-19T14:30:00"),to:["<EMAIL>"],subject:"Credit Request - Order ORD124",content:"Please issue credit note for damaged items...",status:"sent",response:{date:new Date("2025-03-21T09:15:00"),content:"Credit note CN789 issued for damaged items"}}],notes:"Credit note received"},{id:"CR003",orderId:"ORD125",invoiceId:"INV458",supplier:"Alliance",status:"pending",requestDate:new Date("2025-03-24"),items:[{productName:"Amoxicillin 250mg Capsules",pipCode:"PIP345678",quantity:3,reason:"incorrect",details:"Received 500mg instead of 250mg",originalUnitPrice:5.99,originalTotal:17.97}],emailHistory:[],notes:"Awaiting initial email to be sent"}];async function u(){try{return o.NextResponse.json(d)}catch(e){return console.error("Error fetching credit requests:",e),o.NextResponse.json({error:"Failed to fetch credit requests"},{status:500})}}async function c(e){try{let t={...await e.json(),id:`CR${String(d.length+1).padStart(3,"0")}`,status:"pending",requestDate:new Date,emailHistory:[]};return d.push(t),o.NextResponse.json(t)}catch(e){return console.error("Error creating credit request:",e),o.NextResponse.json({error:"Failed to create credit request"},{status:500})}}let p=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/credit-requests/route",pathname:"/api/credit-requests",filename:"route",bundlePath:"app/api/credit-requests/route"},resolvedPagePath:"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/api/credit-requests/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:m,serverHooks:g}=p;function x(){return(0,n.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:m})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580],()=>r(8577));module.exports=s})();