(()=>{var e={};e.id=1766,e.ids=[1766],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},75894:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>d,serverHooks:()=>v,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>h});var s={};r.r(s),r.d(s,{GET:()=>c,POST:()=>l});var o=r(96559),a=r(48088),n=r(37719),i=r(32190),u=r(44999);let p="http://localhost:3000";async function c(){try{let e=(await (0,u.UL)()).get("auth-token"),t={Accept:"application/json"};e&&(t.Authorization=`Bearer ${e.value}`);let r=await fetch(`${p}/api/invoices`,{headers:t,cache:"no-store"});if(!r.ok){let e="Failed to fetch invoices";try{let t=await r.json();e=t.error?.message||t.error||e}catch{e=r.statusText||e}return i.NextResponse.json({error:e},{status:r.status})}let s=await r.json();return i.NextResponse.json(s)}catch(e){return console.error("Error in /api/invoices:",e),i.NextResponse.json({error:"Internal Server Error"},{status:500})}}async function l(e){try{let t,r=(await (0,u.UL)()).get("auth-token"),s={};r&&(s.Authorization=`Bearer ${r.value}`);let o=await e.formData(),a=new FormData;for(let[e,t]of o.entries())a.append(e,t);let n=await fetch(`${p}/api/invoices/process`,{method:"POST",headers:s,body:a});if(t=(n.headers.get("content-type")||"").includes("application/json")?await n.json():await n.text(),!n.ok)return i.NextResponse.json({error:"string"==typeof t?t:t?.error||"Upload failed"},{status:n.status});return i.NextResponse.json(t,{status:n.status})}catch(e){return console.error("Error proxying invoice upload:",e),i.NextResponse.json({error:"Failed to upload invoice"},{status:500})}}let d=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/invoices/route",pathname:"/api/invoices",filename:"route",bundlePath:"app/api/invoices/route"},resolvedPagePath:"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/api/invoices/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:h,serverHooks:v}=d;function f(){return(0,n.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:h})}},78335:()=>{},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,4999],()=>r(75894));module.exports=s})();