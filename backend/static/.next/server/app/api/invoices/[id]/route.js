(()=>{var e={};e.id=2684,e.ids=[2684],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},57938:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>l,routeModule:()=>p,serverHooks:()=>h,workAsyncStorage:()=>u,workUnitAsyncStorage:()=>d});var s={};r.r(s),r.d(s,{GET:()=>c});var a=r(96559),o=r(48088),i=r(37719),n=r(32190);async function c(e,{params:t}){console.log("Next.js API route - incoming headers:",Object.fromEntries(e.headers.entries()));let{id:r}=await t,s=await fetch(`http://localhost:3000/api/invoices/${r}`,{method:"GET",headers:{Accept:"application/json",...e.headers.get("authorization")?{authorization:e.headers.get("authorization")}:{},...e.headers.get("cookie")?{cookie:e.headers.get("cookie")}:{}},credentials:"include"}),a=await s.arrayBuffer(),o=s.headers.get("content-type")||"application/json";return new n.NextResponse(a,{status:s.status,headers:{"content-type":o}})}let p=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/invoices/[id]/route",pathname:"/api/invoices/[id]",filename:"route",bundlePath:"app/api/invoices/[id]/route"},resolvedPagePath:"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/api/invoices/[id]/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:u,workUnitAsyncStorage:d,serverHooks:h}=p;function l(){return(0,i.patchFetch)({workAsyncStorage:u,workUnitAsyncStorage:d})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580],()=>r(57938));module.exports=s})();