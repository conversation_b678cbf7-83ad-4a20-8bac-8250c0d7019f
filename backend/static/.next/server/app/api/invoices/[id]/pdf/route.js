(()=>{var e={};e.id=7519,e.ids=[7519],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70611:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>c,serverHooks:()=>l,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>d});var o={};r.r(o),r.d(o,{GET:()=>u});var s=r(96559),n=r(48088),a=r(37719),i=r(32190);async function u(e,{params:t}){let{id:r}=await t;console.log(`[PDF Route] Fetching PDF for invoice ${r}`),console.log("[PDF Route] Headers:",Object.fromEntries(e.headers.entries()));try{let t=e.headers.get("cookie")?.match(/auth-token=([^;]+)/)?.[1];if(!t)return console.error("[PDF Route] No auth token found in cookies"),new i.NextResponse("Unauthorized - No auth token",{status:401});console.log("[PDF Route] Fetching with auth token:",t.substring(0,20)+"...");let o=await fetch(`http://localhost:3000/api/invoices/${r}/pdf`,{method:"GET",headers:{Accept:"application/pdf",Authorization:`Bearer ${t}`,Cookie:`auth-token=${t}`},credentials:"include"});if(console.log("[PDF Route] Backend response status:",o.status),console.log("[PDF Route] Backend response headers:",Object.fromEntries(o.headers.entries())),!o.ok)return console.error("[PDF Route] Backend error:",await o.text()),new i.NextResponse(`Failed to fetch PDF: ${o.statusText}`,{status:o.status});let s=await o.arrayBuffer(),n=o.headers.get("content-type")||"application/pdf",a=o.headers.get("content-disposition");return console.log("[PDF Route] Received PDF, size:",s.byteLength,"bytes"),console.log("[PDF Route] Content-Type:",n),new i.NextResponse(s,{status:200,headers:{"content-type":n,...a?{"content-disposition":a}:{}}})}catch(e){return console.error("[PDF Route] Error:",e),new i.NextResponse(`Internal Server Error: ${e?.message||"Unknown error"}`,{status:500})}}let c=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/invoices/[id]/pdf/route",pathname:"/api/invoices/[id]/pdf",filename:"route",bundlePath:"app/api/invoices/[id]/pdf/route"},resolvedPagePath:"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/api/invoices/[id]/pdf/route.ts",nextConfigOutput:"standalone",userland:o}),{workAsyncStorage:p,workUnitAsyncStorage:d,serverHooks:l}=c;function h(){return(0,a.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:d})}},78335:()=>{},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[4447,580],()=>r(70611));module.exports=o})();