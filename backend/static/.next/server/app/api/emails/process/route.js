(()=>{var e={};e.id=355,e.ids=[355],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29808:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>x,routeModule:()=>c,serverHooks:()=>m,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>d});var s={};t.r(s),t.d(s,{POST:()=>u});var o=t(96559),a=t(48088),n=t(37719),i=t(32190),p=t(44999);async function u(){try{let e=(await (0,p.UL)()).get("auth-token"),r={Accept:"application/json","Content-Type":"application/json"};e&&(r.Authorization=`Bearer ${e.value}`);let t=await fetch("http://localhost:3000/api/emails/process",{method:"POST",headers:r,cache:"no-store"});if(!t.ok){let e="Failed to process emails";try{let r=await t.json();e=r.error?.message||r.error||e}catch{e=t.statusText||e}return i.NextResponse.json({error:e},{status:t.status})}let s=await t.json();return i.NextResponse.json(s)}catch(e){return console.error("Error in /api/emails/process:",e),i.NextResponse.json({error:"Internal Server Error"},{status:500})}}let c=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/emails/process/route",pathname:"/api/emails/process",filename:"route",bundlePath:"app/api/emails/process/route"},resolvedPagePath:"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/api/emails/process/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:d,serverHooks:m}=c;function x(){return(0,n.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:d})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,4999],()=>t(29808));module.exports=s})();