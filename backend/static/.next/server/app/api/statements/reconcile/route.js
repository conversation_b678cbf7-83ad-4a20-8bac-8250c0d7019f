(()=>{var e={};e.id=8733,e.ids=[8733],e.modules={278:(e,t,n)=>{"use strict";function a(e,t){return e instanceof Date?new e.constructor(t):new Date(t)}n.d(t,{w:()=>a})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},20592:(e,t,n)=>{"use strict";n.d(t,{e:()=>i});var a=n(90672),r=n(278);function i(e,t){var n=-t;let i=(0,a.a)(e);return isNaN(n)?(0,r.w)(e,NaN):(n&&i.setDate(i.getDate()+n),i)}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},79889:(e,t,n)=>{"use strict";n.r(t),n.d(t,{patchFetch:()=>V,routeModule:()=>I,serverHooks:()=>j,workAsyncStorage:()=>T,workUnitAsyncStorage:()=>N});var a={};n.r(a),n.d(a,{POST:()=>x});var r=n(96559),i=n(48088),s=n(37719),o=n(32190),c=n(20592);let u=new Date,d=new Date(u.getFullYear(),u.getMonth(),1),l=new Date(u.getFullYear(),u.getMonth()+1,0),m=new Date(d.getTime()+Math.random()*(l.getTime()-d.getTime())),p=new Date(d.getTime()+Math.random()*(l.getTime()-d.getTime())),g=new Date(d.getTime()+Math.random()*(l.getTime()-d.getTime())),v=new Date(d.getTime()+Math.random()*(l.getTime()-d.getTime())),f=new Date(u.getFullYear(),u.getMonth()-1,1),h=new Date(u.getFullYear(),u.getMonth(),0),w=new Date(f.getTime()+Math.random()*(h.getTime()-f.getTime())),A=new Date(f.getTime()+Math.random()*(h.getTime()-f.getTime())),D=[{id:"st-001",supplier:"AAH Pharmaceuticals",period:{start:d,end:l},invoices:[{invoiceId:"INV-AAH-001",date:m,amount:1250.75,status:"matched"},{invoiceId:"INV-AAH-002",date:p,amount:875.5,status:"unmatched",discrepancies:[{field:"Total Amount",statementValue:875.5,invoiceValue:825.5,difference:50}]},{invoiceId:"INV-AAH-003",date:g,amount:320.25,status:"partial",discrepancies:[{field:"Item Count",statementValue:15,invoiceValue:14,difference:1}]}],totalAmount:2446.5,reconciled:!1,createdAt:new Date,updatedAt:new Date},{id:"st-002",supplier:"Phoenix Healthcare",period:{start:d,end:l},invoices:[{invoiceId:"INV-PHX-001",date:m,amount:950.25,status:"matched"},{invoiceId:"INV-PHX-002",date:v,amount:1125.75,status:"matched"}],totalAmount:2076,reconciled:!0,createdAt:new Date,updatedAt:new Date},{id:"st-003",supplier:"Alliance Healthcare",period:{start:f,end:h},invoices:[{invoiceId:"INV-ALH-001",date:w,amount:1875.5,status:"matched"},{invoiceId:"INV-ALH-002",date:A,amount:725.25,status:"unmatched",discrepancies:[{field:"Total Amount",statementValue:725.25,invoiceValue:775.25,difference:-50},{field:"VAT",statementValue:120.88,invoiceValue:129.21,difference:-8.33}]}],totalAmount:2600.75,reconciled:!1,createdAt:(0,c.e)(new Date,30),updatedAt:(0,c.e)(new Date,30)}];async function x(e){try{let{statementId:t,invoiceId:n,resolution:a}=await e.json();if(!t||!n)return o.NextResponse.json({error:"Statement ID and Invoice ID are required"},{status:400});let r=D.findIndex(e=>e.id===t);if(-1===r)return o.NextResponse.json({error:"Statement not found"},{status:404});let i=D[r],s=i.invoices.findIndex(e=>e.invoiceId===n);if(-1===s)return o.NextResponse.json({error:"Invoice not found in statement"},{status:404});let c=[...i.invoices];c[s]={...c[s],status:"matched",discrepancies:void 0};let u=c.every(e=>"matched"===e.status);return D[r]={...i,invoices:c,reconciled:u,updatedAt:new Date},o.NextResponse.json({success:!0,message:`Invoice ${n} reconciled successfully`,statementReconciled:u})}catch(e){return console.error("Error reconciling invoice:",e),o.NextResponse.json({error:"Failed to reconcile invoice"},{status:500})}}let I=new r.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/statements/reconcile/route",pathname:"/api/statements/reconcile",filename:"route",bundlePath:"app/api/statements/reconcile/route"},resolvedPagePath:"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/api/statements/reconcile/route.ts",nextConfigOutput:"standalone",userland:a}),{workAsyncStorage:T,workUnitAsyncStorage:N,serverHooks:j}=I;function V(){return(0,s.patchFetch)({workAsyncStorage:T,workUnitAsyncStorage:N})}},90672:(e,t,n)=>{"use strict";function a(e){let t=Object.prototype.toString.call(e);return e instanceof Date||"object"==typeof e&&"[object Date]"===t?new e.constructor(+e):new Date("number"==typeof e||"[object Number]"===t||"string"==typeof e||"[object String]"===t?e:NaN)}n.d(t,{a:()=>a})},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),a=t.X(0,[4447,580],()=>n(79889));module.exports=a})();