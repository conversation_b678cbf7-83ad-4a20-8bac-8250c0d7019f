(()=>{var e={};e.id=7730,e.ids=[7730],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{},99852:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>c,serverHooks:()=>m,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var s={};r.r(s),r.d(s,{GET:()=>p});var a=r(96559),n=r(48088),o=r(37719),i=r(32190),u=r(44999);async function p(e,{params:t}){let r;try{r=(await t).id;let e=(await (0,u.UL)()).get("auth-token"),s={Accept:"application/json"};e&&(s.Authorization=`Bearer ${e.value}`);let a=await fetch(`http://localhost:3000/api/statements/${r}`,{headers:s,cache:"no-store"});if(!a.ok){let e="Failed to fetch statement";try{let t=await a.json();e=t.error?.message||t.error||e}catch{e=a.statusText||e}return i.NextResponse.json({error:e},{status:a.status})}let n=await a.json();return i.NextResponse.json(n)}catch(e){return console.error(`Error in /api/statements/${r||"unknown"}:`,e),i.NextResponse.json({error:"Internal Server Error"},{status:500})}}let c=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/statements/[id]/route",pathname:"/api/statements/[id]",filename:"route",bundlePath:"app/api/statements/[id]/route"},resolvedPagePath:"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/api/statements/[id]/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:m}=c;function x(){return(0,o.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,4999],()=>r(99852));module.exports=s})();