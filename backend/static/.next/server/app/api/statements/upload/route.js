(()=>{var e={};e.id=8e3,e.ids=[8e3],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},54726:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>d,serverHooks:()=>m,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>c});var s={};r.r(s),r.d(s,{POST:()=>p});var a=r(96559),o=r(48088),n=r(37719),u=r(32190),i=r(44999);async function p(e){try{let t=(await (0,i.UL)()).get("auth-token"),r=await e.formData(),s={};t&&(s.Authorization=`Bearer ${t.value}`);let a=await fetch("http://localhost:3000/api/statements/upload",{method:"POST",headers:s,body:r});if(!a.ok){let e="Failed to upload statement";try{let t=await a.json();e=t.error?.message||t.error||e}catch{e=a.statusText||e}return u.NextResponse.json({error:e},{status:a.status})}let o=await a.json();return u.NextResponse.json(o)}catch(e){return console.error("Error in /api/statements/upload:",e),u.NextResponse.json({error:"Internal Server Error"},{status:500})}}let d=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/statements/upload/route",pathname:"/api/statements/upload",filename:"route",bundlePath:"app/api/statements/upload/route"},resolvedPagePath:"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/api/statements/upload/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:c,serverHooks:m}=d;function x(){return(0,n.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:c})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,4999],()=>r(54726));module.exports=s})();