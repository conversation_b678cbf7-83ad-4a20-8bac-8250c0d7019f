(()=>{var e={};e.id=9096,e.ids=[9096],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},40931:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>c,serverHooks:()=>m,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var s={};r.r(s),r.d(s,{GET:()=>u});var a=r(96559),n=r(48088),o=r(37719),i=r(32190),p=r(44999);async function u(e){try{let t=(await (0,p.UL)()).get("auth-token"),{searchParams:r}=new URL(e.url),s=r.get("supplier"),a=r.get("startDate"),n=r.get("endDate"),o="";s&&(o+=`supplier=${encodeURIComponent(s)}&`),a&&(o+=`startDate=${encodeURIComponent(a)}&`),n&&(o+=`endDate=${encodeURIComponent(n)}&`);let u={Accept:"application/json"};t&&(u.Authorization=`Bearer ${t.value}`);let c=await fetch(`http://localhost:3000/api/statements${o?`?${o}`:""}`,{headers:u,cache:"no-store"});if(!c.ok){let e="Failed to fetch statements";try{let t=await c.json();e=t.error?.message||t.error||e}catch{e=c.statusText||e}return i.NextResponse.json({error:e},{status:c.status})}let d=await c.json();return i.NextResponse.json(d.statements||[])}catch(e){return console.error("Error in /api/statements:",e),i.NextResponse.json({error:"Internal Server Error"},{status:500})}}let c=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/statements/route",pathname:"/api/statements",filename:"route",bundlePath:"app/api/statements/route"},resolvedPagePath:"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/api/statements/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:m}=c;function x(){return(0,o.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,4999],()=>r(40931));module.exports=s})();