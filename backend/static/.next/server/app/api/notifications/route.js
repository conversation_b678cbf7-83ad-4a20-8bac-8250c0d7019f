"use strict";(()=>{var e={};e.id=2170,e.ids=[2170],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},49450:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>f,routeModule:()=>c,serverHooks:()=>x,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var o={};t.r(o),t.d(o,{GET:()=>u});var n=t(96559),s=t(48088),a=t(37719),i=t(32190);let p=[...t(66293).pG];async function u(e){try{return i.NextResponse.json(p)}catch(e){return console.error("Error fetching notifications:",e),i.NextResponse.json({error:"Failed to fetch notifications"},{status:500})}}let c=new n.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/notifications/route",pathname:"/api/notifications",filename:"route",bundlePath:"app/api/notifications/route"},resolvedPagePath:"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/api/notifications/route.ts",nextConfigOutput:"standalone",userland:o}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:x}=c;function f(){return(0,a.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[4447,580,5545],()=>t(49450));module.exports=o})();