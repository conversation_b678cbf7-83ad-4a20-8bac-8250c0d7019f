(()=>{var e={};e.id=9789,e.ids=[9789],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},58247:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>x,routeModule:()=>c,serverHooks:()=>h,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var s={};t.r(s),t.d(s,{GET:()=>p});var o=t(96559),a=t(48088),n=t(37719),i=t(32190),u=t(44999);async function p(e){try{let r=e.cookies.get("auth-token")?.value,t=await (0,u.b3)(),s=await t.get("Authorization"),o=r?`Bearer ${r}`:s;if(!o)return i.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});let a=await fetch("http://localhost:3000/api/scrape/orders",{headers:{Authorization:o,Accept:"application/json"}});if(!a.ok)throw Error("Failed to retrieve orders");let n=await a.json();return i.NextResponse.json(n)}catch(e){return console.error("Error retrieving orders:",e),i.NextResponse.json({success:!1,message:"Failed to retrieve orders"},{status:500})}}let c=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/orders/route",pathname:"/api/orders",filename:"route",bundlePath:"app/api/orders/route"},resolvedPagePath:"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/api/orders/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:h}=c;function x(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,4999],()=>t(58247));module.exports=s})();