(()=>{var e={};e.id=4200,e.ids=[4200],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},39788:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>v,routeModule:()=>c,serverHooks:()=>x,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>d});var s={};t.r(s),t.d(s,{POST:()=>u});var o=t(96559),a=t(48088),n=t(37719),i=t(32190),p=t(44999);async function u(e){try{let{startDate:r,endDate:t}=await e.json();if(console.log("API route called with dates:",{startDate:r,endDate:t}),!r||!t)return i.NextResponse.json({error:"Missing date parameters"},{status:400});let s=/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}$/;if(!s.test(r)||!s.test(t))return i.NextResponse.json({error:"Invalid date format. Expected YYYY-MM-DD HH:mm"},{status:400});let o=(await (0,p.UL)()).get("auth-token"),a={Accept:"application/json","Content-Type":"application/json"};o&&(a.Authorization=`Bearer ${o.value}`);let n="http://localhost:3000/api/scrape/retrieve";console.log("Making request to:",n);let u=await fetch(n,{method:"POST",headers:a,body:JSON.stringify({startDate:r,endDate:t}),cache:"no-store"});if(console.log("Response status:",u.status),!u.ok){let e="Failed to retrieve orders";try{if((u.headers.get("content-type")||"").includes("application/json")){let r=await u.json();e=r.error?.message||r.error||e}else e=await u.text()||e}catch{e=u.statusText||e}return i.NextResponse.json({error:e},{status:u.status})}let c=await u.text();console.log("Response text:",c);try{let e=c?JSON.parse(c):null;if(!e)throw Error("Empty response");return i.NextResponse.json(e)}catch(e){return console.error("Failed to parse response:",e),i.NextResponse.json({error:"Invalid response from server"},{status:500})}}catch(e){return console.error("Error in /api/orders/retrieve:",e),i.NextResponse.json({error:"Internal Server Error"},{status:500})}}let c=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/orders/retrieve/route",pathname:"/api/orders/retrieve",filename:"route",bundlePath:"app/api/orders/retrieve/route"},resolvedPagePath:"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/api/orders/retrieve/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:d,serverHooks:x}=c;function v(){return(0,n.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:d})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,4999],()=>t(39788));module.exports=s})();