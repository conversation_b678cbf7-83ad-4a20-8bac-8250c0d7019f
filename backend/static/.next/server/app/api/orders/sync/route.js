(()=>{var e={};e.id=6213,e.ids=[6213],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12103:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>c,serverHooks:()=>x,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var s={};t.r(s),t.d(s,{GET:()=>p});var o=t(96559),n=t(48088),a=t(37719),i=t(32190),u=t(44999);async function p(e){try{let e=await (0,u.b3)(),r=await e.get("Authorization");if(!r)return i.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});let t=await fetch("http://localhost:3000/api/orders/sync",{headers:{Authorization:r,"Content-Type":"application/json"}}),s=await t.json();if(!t.ok)return i.NextResponse.json({error:s.error||{message:"Failed to sync orders with Drug Comparison"}},{status:t.status});return i.NextResponse.json(s)}catch(e){return console.error("Error syncing orders:",e),i.NextResponse.json({error:{message:"Internal server error"}},{status:500})}}let c=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/orders/sync/route",pathname:"/api/orders/sync",filename:"route",bundlePath:"app/api/orders/sync/route"},resolvedPagePath:"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/api/orders/sync/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:x}=c;function h(){return(0,a.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,4999],()=>t(12103));module.exports=s})();