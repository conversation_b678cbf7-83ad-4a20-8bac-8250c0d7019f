(()=>{var e={};e.id=7413,e.ids=[7413],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},45284:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>g,routeModule:()=>l,serverHooks:()=>m,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{GET:()=>d,PATCH:()=>c});var a=t(96559),o=t(48088),n=t(37719),i=t(32190),u=t(44999);let p="http://localhost:3000";async function d(e,{params:r}){try{let e=await (0,u.b3)(),t=await e.get("Authorization");if(!t)return i.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});let s=(await r).id,a=await fetch(`${p}/api/orders/${s}`,{headers:{Authorization:t,"Content-Type":"application/json"}}),o=await a.json();if(!a.ok)return i.NextResponse.json({error:o.error||{message:"Failed to fetch order details"}},{status:a.status});return i.NextResponse.json(o)}catch(e){return console.error("Error fetching order details:",e),i.NextResponse.json({error:{message:"Internal server error"}},{status:500})}}async function c(e,{params:r}){try{let t=await (0,u.b3)(),s=await t.get("Authorization");if(!s)return i.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});let a=(await r).id,o=await e.json(),n=await fetch(`${p}/api/orders/${a}`,{method:"PATCH",headers:{Authorization:s,"Content-Type":"application/json"},body:JSON.stringify(o)}),d=await n.json();if(!n.ok)return i.NextResponse.json({error:d.error||{message:"Failed to update order status"}},{status:n.status});return i.NextResponse.json(d)}catch(e){return console.error("Error updating order status:",e),i.NextResponse.json({error:{message:"Internal server error"}},{status:500})}}let l=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/orders/[id]/route",pathname:"/api/orders/[id]",filename:"route",bundlePath:"app/api/orders/[id]/route"},resolvedPagePath:"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/api/orders/[id]/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:h,workUnitAsyncStorage:x,serverHooks:m}=l;function g(){return(0,n.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:x})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,4999],()=>t(45284));module.exports=s})();