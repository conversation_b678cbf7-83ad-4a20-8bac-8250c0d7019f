{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [{"source": "/_next/static/:path*", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}], "regex": "^/_next/static(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}, {"source": "/static/:path*", "headers": [{"key": "Cache-Control", "value": "public, max-age=86400"}], "regex": "^/static(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}], "dynamicRoutes": [{"page": "/api/checklist/items/[id]", "regex": "^/api/checklist/items/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/checklist/items/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/invoices/[id]", "regex": "^/api/invoices/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/invoices/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/invoices/[id]/pdf", "regex": "^/api/invoices/([^/]+?)/pdf(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/invoices/(?<nxtPid>[^/]+?)/pdf(?:/)?$"}, {"page": "/api/orders/[id]", "regex": "^/api/orders/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/orders/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/statements/[id]", "regex": "^/api/statements/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/statements/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/workflow/steps/[id]/complete", "regex": "^/api/workflow/steps/([^/]+?)/complete(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/workflow/steps/(?<nxtPid>[^/]+?)/complete(?:/)?$"}, {"page": "/api/workflow/steps/[id]/skip", "regex": "^/api/workflow/steps/([^/]+?)/skip(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/workflow/steps/(?<nxtPid>[^/]+?)/skip(?:/)?$"}, {"page": "/invoices/[invoiceId]", "regex": "^/invoices/([^/]+?)(?:/)?$", "routeKeys": {"nxtPinvoiceId": "nxtPinvoiceId"}, "namedRegex": "^/invoices/(?<nxtPinvoiceId>[^/]+?)(?:/)?$"}, {"page": "/statements/[statementId]", "regex": "^/statements/([^/]+?)(?:/)?$", "routeKeys": {"nxtPstatementId": "nxtPstatementId"}, "namedRegex": "^/statements/(?<nxtPstatementId>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/analytics", "regex": "^/analytics(?:/)?$", "routeKeys": {}, "namedRegex": "^/analytics(?:/)?$"}, {"page": "/cashflow", "regex": "^/cashflow(?:/)?$", "routeKeys": {}, "namedRegex": "^/cashflow(?:/)?$"}, {"page": "/checklist", "regex": "^/checklist(?:/)?$", "routeKeys": {}, "namedRegex": "^/checklist(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/invoices", "regex": "^/invoices(?:/)?$", "routeKeys": {}, "namedRegex": "^/invoices(?:/)?$"}, {"page": "/login", "regex": "^/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/login(?:/)?$"}, {"page": "/orders", "regex": "^/orders(?:/)?$", "routeKeys": {}, "namedRegex": "^/orders(?:/)?$"}, {"page": "/overview", "regex": "^/overview(?:/)?$", "routeKeys": {}, "namedRegex": "^/overview(?:/)?$"}, {"page": "/reconciliation", "regex": "^/reconciliation(?:/)?$", "routeKeys": {}, "namedRegex": "^/reconciliation(?:/)?$"}, {"page": "/settings", "regex": "^/settings(?:/)?$", "routeKeys": {}, "namedRegex": "^/settings(?:/)?$"}, {"page": "/statements", "regex": "^/statements(?:/)?$", "routeKeys": {}, "namedRegex": "^/statements(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}