"use strict";(()=>{var e={};e.id=2608,e.ids=[2608],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},18032:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>u,serverHooks:()=>m,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>d});var a={};r.r(a),r.d(a,{GET:()=>l});var s=r(96559),i=r(48088),o=r(37719),n=r(32190);let p=[...r(66293).IE];async function l(e){try{let t=e.nextUrl.searchParams,r=t.get("type"),a=t.get("userId"),s=t.get("startDate"),i=t.get("endDate"),o=[...p];if(r&&"all"!==r&&(o=o.filter(e=>e.type===r)),a&&"all"!==a&&(o=o.filter(e=>e.user.id===a)),s){let e=new Date(s);o=o.filter(t=>t.timestamp>=e)}if(i){let e=new Date(i);o=o.filter(t=>t.timestamp<=e)}return o.sort((e,t)=>t.timestamp.getTime()-e.timestamp.getTime()),n.NextResponse.json(o)}catch(e){return console.error("Error fetching activity log:",e),n.NextResponse.json({error:"Failed to fetch activity log"},{status:500})}}let u=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/activity-log/route",pathname:"/api/activity-log",filename:"route",bundlePath:"app/api/activity-log/route"},resolvedPagePath:"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/api/activity-log/route.ts",nextConfigOutput:"standalone",userland:a}),{workAsyncStorage:c,workUnitAsyncStorage:d,serverHooks:m}=u;function g(){return(0,o.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:d})}},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,580,5545],()=>r(18032));module.exports=a})();