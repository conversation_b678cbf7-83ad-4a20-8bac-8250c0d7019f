(()=>{var e={};e.id=1050,e.ids=[1050],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10814:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/(admin)/layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/(admin)/layout.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16023:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(18962).A)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23689:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(18962).A)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},26373:(e,t,s)=>{"use strict";s.d(t,{V:()=>d});var r=s(60687);s(43210);var a=s(47033),n=s(14952),i=s(6800),l=s(4780),o=s(29523);function d({className:e,classNames:t,showOutsideDays:s=!0,...d}){return(0,r.jsx)(i.hv,{showOutsideDays:s,className:(0,l.cn)("p-3",e),classNames:{months:"flex flex-col sm:flex-row gap-2",month:"flex flex-col gap-4",caption:"flex justify-center pt-1 relative items-center w-full",caption_label:"text-sm font-medium",nav:"flex items-center gap-1",nav_button:(0,l.cn)((0,o.r)({variant:"outline"}),"size-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-y-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",day:(0,l.cn)((0,o.r)({variant:"ghost"}),"h-9 w-9 p-0 font-normal aria-selected:opacity-100"),day_range_end:"day-range-end",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground opacity-50 aria-selected:bg-accent/50 aria-selected:text-muted-foreground aria-selected:opacity-30",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...t},components:{IconLeft:({...e})=>(0,r.jsx)(a.A,{className:"h-4 w-4"}),IconRight:({...e})=>(0,r.jsx)(n.A,{className:"h-4 w-4"})},...d})}d.displayName="Calendar"},28822:(e,t,s)=>{Promise.resolve().then(s.bind(s,10814))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35253:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u});var r=s(60687),a=s(85814),n=s.n(a),i=s(29523),l=s(62185),o=s(16189),d=s(97051),c=s(40083);function m(){let e=(0,o.usePathname)();return(0,r.jsx)("nav",{className:"bg-white border-b border-border/40 sticky top-0 z-50",children:(0,r.jsx)("div",{className:"max-w-[1400px] mx-auto px-8",children:(0,r.jsxs)("div",{className:"flex justify-between h-16",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0 flex items-center",children:(0,r.jsx)(n(),{href:"/",className:"text-lg font-semibold text-primary",children:"Admin Portal"})}),(0,r.jsx)("div",{className:"hidden sm:ml-10 sm:flex sm:space-x-8",children:[{href:"/overview",label:"Overview"},{href:"/invoices",label:"Invoices"},{href:"/statements",label:"Statements"},{href:"/orders",label:"Orders"},{href:"/checklist",label:"Checklist"},{href:"/reconciliation",label:"Reconciliation"},{href:"/cashflow",label:"Cash Flow"},{href:"/analytics",label:"Analytics"},{href:"/settings",label:"Settings"}].map(({href:t,label:s})=>(0,r.jsx)(n(),{href:t,className:`inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium transition-colors ${e===t?"border-primary text-primary":"border-transparent text-muted-foreground hover:text-foreground hover:border-border"}`,children:s},t))})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(i.$,{variant:"ghost",size:"icon",className:"text-muted-foreground hover:text-foreground",children:(0,r.jsx)(d.A,{className:"h-5 w-5"})}),(0,r.jsxs)(i.$,{variant:"ghost",size:"sm",onClick:()=>{l.ZQ.logout(),window.location.href="/login"},className:"text-sm font-medium text-muted-foreground hover:text-foreground",children:[(0,r.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Sign out"]})]})]})})})}function u({children:e}){return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)(m,{}),(0,r.jsx)("main",{className:"p-8",children:e})]})}},40034:(e,t,s)=>{Promise.resolve().then(s.bind(s,93690))},40988:(e,t,s)=>{"use strict";s.d(t,{AM:()=>i,Wv:()=>l,hl:()=>o});var r=s(60687);s(43210);var a=s(40599),n=s(4780);function i({...e}){return(0,r.jsx)(a.bL,{"data-slot":"popover",...e})}function l({...e}){return(0,r.jsx)(a.l9,{"data-slot":"popover-trigger",...e})}function o({className:e,align:t="center",sideOffset:s=4,...i}){return(0,r.jsx)(a.ZL,{children:(0,r.jsx)(a.UC,{"data-slot":"popover-content",align:t,sideOffset:s,className:(0,n.cn)("bg-white","rounded-md","shadow-md","border border-gray-200","p-4","z-50","bg-popover","text-popover-foreground","w-72","outline-hidden","data-[state=open]:animate-in","data-[state=closed]:animate-out","data-[state=closed]:fade-out-0","data-[state=open]:fade-in-0","data-[state=closed]:zoom-out-95","data-[state=open]:zoom-in-95","data-[side=bottom]:slide-in-from-top-2","data-[side=left]:slide-in-from-right-2","data-[side=right]:slide-in-from-left-2","data-[side=top]:slide-in-from-bottom-2",e),...i})})}},41550:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(18962).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},50944:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>P});var r=s(60687),a=s(43210),n=s(16189),i=s(54220),l=s(40228),o=s(29523),d=s(26373),c=s(40988),m=s(73437);let u=(e,t,s)=>e!==t?null:(0,r.jsx)(i.A,{className:"ml-1 h-4 w-4 text-gray-900",style:{transform:"asc"===s?"rotate(180deg)":"none"}}),p=e=>e.toLocaleString("en-GB",{minimumFractionDigits:2,maximumFractionDigits:2});function x({statements:e}){let t=(0,n.useRouter)(),[s,i]=(0,a.useState)(void 0),[x,h]=(0,a.useState)(void 0),[f,g]=(0,a.useState)("statementDate"),[y,j]=(0,a.useState)("desc"),[v,b]=(0,a.useState)(1),[w]=(0,a.useState)(10),N=e=>{if(e instanceof Date)return isNaN(e.getTime())?new Date(0):e;if(!e)return new Date(0);if("string"==typeof e){let t=e.trim().split("/");if(3===t.length){let e=t[0],s=t[1],r=t[2],a=parseInt(e,10),n=parseInt(s,10),i=parseInt(r,10);if(!isNaN(a)&&!isNaN(n)&&!isNaN(i)&&i>1e3&&i<3e3&&n>=1&&n<=12&&a>=1&&a<=31){let e=new Date(i,n-1,a);if(!isNaN(e.getTime())&&e.getFullYear()===i&&e.getMonth()===n-1&&e.getDate()===a)return e}}}return new Date(0)},A=(0,a.useMemo)(()=>e=>{if(!s&&!x)return!0;if(!e)return!1;let t=N(e);if(t.getTime()===new Date(0).getTime()&&null!=e&&""!==String(e).trim()||t.getTime()===new Date(0).getTime()&&(s||x))return!1;if(s){let e=new Date(s);if(e.setHours(0,0,0,0),t<e)return!1}if(x){let e=new Date(x);if(e.setHours(23,59,59,999),t>e)return!1}return!0},[s,x]),P=(0,a.useMemo)(()=>e.filter(e=>A(e.statementDate)),[e,A]),D=(0,a.useMemo)(()=>[...P].sort((e,t)=>{let s=0;switch(f){case"statementDate":s=N(e.statementDate).getTime()-N(t.statementDate).getTime();break;case"dueDate":s=N(e.DueDate).getTime()-N(t.DueDate).getTime();break;case"invoiceCount":s=(e.invoices?.length||0)-(t.invoices?.length||0);break;case"total":s=(e.totalAmount||0)-(t.totalAmount||0)}return"desc"===y?-s:s}),[P,f,y]),S=(0,a.useMemo)(()=>{let e=(v-1)*w;return D.slice(e,e+w)},[D,v,w]),k=Math.ceil(D.length/w),C=(0,a.useMemo)(()=>P.reduce((e,t)=>e+(t.totalAmount||0),0),[P]),_=e=>{f===e?j("asc"===y?"desc":"asc"):(g(e),j("desc"))};return(0,r.jsxs)("div",{className:"w-full max-w-[1400px] mx-auto px-4",children:[(0,r.jsx)("div",{className:"flex justify-between items-center mb-6",children:(0,r.jsxs)("h2",{className:"text-2xl font-semibold text-gray-900",children:["Statements",(0,r.jsxs)("span",{className:"ml-4 text-lg font-normal text-gray-600",children:["(Total: \xa3",p(C),")"]})]})}),(0,r.jsx)("div",{className:"flex gap-4 mb-6 items-center",children:(0,r.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"From:"}),(0,r.jsxs)(c.AM,{children:[(0,r.jsx)(c.Wv,{asChild:!0,className:"border border-gray-200",children:(0,r.jsxs)(o.$,{variant:"outline",children:[(0,r.jsx)(l.A,{className:"mr-2 h-4 w-4"}),s?(0,m.GP)(s,"PPP"):"Pick a date"]})}),(0,r.jsx)(c.hl,{className:"p-2 bg-white rounded-lg shadow-lg border border-gray-200 z-50",align:"start",children:(0,r.jsx)(d.V,{mode:"single",selected:s,onSelect:i,initialFocus:!0})})]}),(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"To:"}),(0,r.jsxs)(c.AM,{children:[(0,r.jsx)(c.Wv,{asChild:!0,className:"border border-gray-200",children:(0,r.jsxs)(o.$,{variant:"outline",children:[(0,r.jsx)(l.A,{className:"mr-2 h-4 w-4"}),x?(0,m.GP)(x,"PPP"):"Pick a date"]})}),(0,r.jsx)(c.hl,{className:"p-2 bg-white rounded-lg shadow-lg border border-gray-200 z-50",align:"start",children:(0,r.jsx)(d.V,{mode:"single",selected:x,onSelect:h,initialFocus:!0})})]})]})}),(0,r.jsx)("div",{className:"relative overflow-x-auto bg-white shadow-sm rounded-xl border border-gray-100",children:(0,r.jsxs)("table",{className:"w-full text-sm text-left",children:[(0,r.jsx)("thead",{className:"bg-gray-50 border-b border-gray-100",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{scope:"col",className:"px-6 py-4 text-xs font-semibold text-gray-600",children:"Customer"}),(0,r.jsx)("th",{scope:"col",className:"group cursor-pointer px-6 py-4 text-xs font-semibold text-gray-600",onClick:()=>_("statementDate"),children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{children:"Statement Date"}),u(f,"statementDate",y)]})}),(0,r.jsx)("th",{scope:"col",className:"group cursor-pointer px-6 py-4 text-xs font-semibold text-gray-600",onClick:()=>_("dueDate"),children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{children:"Due Date"}),u(f,"dueDate",y)]})}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-4 text-xs font-semibold text-gray-600",children:"Statement ID"}),(0,r.jsx)("th",{scope:"col",className:"group cursor-pointer px-6 py-4 text-xs font-semibold text-gray-600 text-right",onClick:()=>_("invoiceCount"),children:(0,r.jsxs)("div",{className:"flex items-center justify-end",children:[(0,r.jsx)("span",{children:"Invoices"}),u(f,"invoiceCount",y)]})}),(0,r.jsx)("th",{scope:"col",className:"group cursor-pointer px-6 py-4 text-xs font-semibold text-gray-600 text-right",onClick:()=>_("total"),children:(0,r.jsxs)("div",{className:"flex items-center justify-end",children:[(0,r.jsx)("span",{children:"Total"}),u(f,"total",y)]})})]})}),(0,r.jsx)("tbody",{children:0===D.length?(0,r.jsx)("tr",{children:(0,r.jsx)("td",{colSpan:6,className:"px-6 py-8 text-center text-gray-500",children:"No statements found matching your filters"})}):S.map(e=>(0,r.jsxs)("tr",{onClick:()=>t.push(`/statements/${e.id||e.StatementId}`),className:"bg-white border-b hover:bg-gray-50 transition-colors cursor-pointer",children:[(0,r.jsxs)("td",{className:"px-6 py-4",children:[(0,r.jsx)("div",{className:"text-sm text-gray-900",children:e.CustomerName}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.CustomerAddress})]}),(0,r.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:e.statementDate?(0,m.GP)(N(e.statementDate),"dd/MM/yyyy"):"N/A"}),(0,r.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:e.DueDate?(0,m.GP)(N(e.DueDate),"dd/MM/yyyy"):"N/A"}),(0,r.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:e.StatementId}),(0,r.jsx)("td",{className:"px-6 py-4 text-sm text-gray-900 text-right",children:e.invoices?.length||0}),(0,r.jsxs)("td",{className:"px-6 py-4 text-sm text-gray-900 text-right font-medium",children:["\xa3",p(e.totalAmount||0)]})]},e.id||e.StatementId||`statement-${Math.random()}`))})]})}),D.length>0&&(0,r.jsx)("div",{className:"flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6 mt-4 rounded-lg",children:(0,r.jsxs)("div",{className:"flex flex-1 items-center justify-between",children:[(0,r.jsx)("div",{children:(0,r.jsxs)("p",{className:"text-sm text-gray-700",children:["Showing ",(0,r.jsx)("span",{className:"font-medium",children:(v-1)*w+1})," to"," ",(0,r.jsx)("span",{className:"font-medium",children:Math.min(v*w,D.length)})," of"," ",(0,r.jsx)("span",{className:"font-medium",children:D.length})," results"]})}),(0,r.jsxs)("div",{className:"flex gap-1",children:[(0,r.jsx)(o.$,{variant:"outline",size:"sm",onClick:()=>b(1),disabled:1===v,className:"min-w-[70px] text-center",children:"First"}),(0,r.jsx)(o.$,{variant:"outline",size:"sm",onClick:()=>b(e=>Math.max(1,e-1)),disabled:1===v,className:"min-w-[70px] text-center",children:"Previous"}),(0,r.jsx)("div",{className:"flex items-center gap-1 px-2",children:(0,r.jsxs)("span",{className:"text-sm text-gray-700 text-center min-w-[100px]",children:["Page ",v," of ",k]})}),(0,r.jsx)(o.$,{variant:"outline",size:"sm",onClick:()=>b(e=>Math.min(k,e+1)),disabled:v===k,className:"min-w-[70px] text-center",children:"Next"}),(0,r.jsx)(o.$,{variant:"outline",size:"sm",onClick:()=>b(k),disabled:v===k,className:"min-w-[70px] text-center",children:"Last"})]})]})})]})}var h=s(16023),f=s(63851),g=s(23689);s(62185);let y=async()=>{try{let e=await fetch("/api/statements",{method:"GET",headers:{Accept:"application/json"}});if(!e.ok){let t=await e.json();throw Error(t.error||"Failed to fetch statements")}return(await e.json()).map(e=>({...e,id:e.id||e.StatementId,StatementId:e.id||e.StatementId,period:{start:e.period?.start?new Date(e.period.start):null,end:e.period?.end?new Date(e.period.end):null},createdAt:e.createdAt?new Date(e.createdAt):null,updatedAt:e.updatedAt?new Date(e.updatedAt):null}))}catch(e){throw console.error("Error fetching statements:",e),e}},j=async(e,t,s)=>{try{let r=new FormData;r.append("file",e),r.append("supplier",t.supplier),r.append("periodStart",t.periodStart),r.append("periodEnd",t.periodEnd);let a=new XMLHttpRequest,n=new Promise((e,t)=>{a.upload.addEventListener("progress",e=>{if(e.lengthComputable&&s){let t=Math.round(e.loaded/e.total*100);s(t)}}),a.addEventListener("load",async()=>{if(a.status>=200&&a.status<300){let s=JSON.parse(a.responseText);s.success&&s.statement?e({...s.statement,id:s.statement.id||s.statement.StatementId,StatementId:s.statement.id||s.statement.StatementId,period:{start:s.statement.period?.start?new Date(s.statement.period.start):null,end:s.statement.period?.end?new Date(s.statement.period.end):null},createdAt:s.statement.createdAt?new Date(s.statement.createdAt):null,updatedAt:s.statement.updatedAt?new Date(s.statement.updatedAt):null}):t(Error(s.error||"Upload failed"))}else t(Error(`Upload failed with status ${a.status}`))}),a.addEventListener("error",()=>{t(Error("Network error during upload"))})});return a.open("POST","/api/statements/upload"),a.send(r),n}catch(e){throw console.error("Error uploading statement:",e),e}},v=async()=>{try{let e=await fetch("/api/statements/process-email",{method:"POST",headers:{Accept:"application/json"}});if(!e.ok){let t=await e.json();throw Error(t.error||"Failed to process statements from email")}let t=await e.json();return{success:t.success,message:t.message||"Statements processed successfully"}}catch(e){throw console.error("Error processing statements from email:",e),e}};function b({onUploadComplete:e}){let[t,s]=(0,a.useState)(!1),[n,i]=(0,a.useState)(!1),[l,d]=(0,a.useState)(0),[c,m]=(0,a.useState)(null),[u,p]=(0,a.useState)(null),x=async t=>{let r=t.target.files?.[0];if(r){s(!0),i(!1),d(0),m(null),p(null);try{let t=new Date,a=new Date(t.getFullYear(),t.getMonth(),1),n=new Date(t.getFullYear(),t.getMonth()+1,0),l=r.name.split(".")[0].split("_")[0]||"Unknown";await j(r,{supplier:l,periodStart:a.toISOString(),periodEnd:n.toISOString()},e=>{d(e),100===e&&(s(!1),i(!0))}),i(!1),p(`Statement "${r.name}" processed successfully`),e()}catch(e){console.error("Error uploading statement:",e),m(e.message||"Failed to upload statement")}finally{s(!1),i(!1),t.target.value=""}}};return(0,r.jsxs)("div",{className:"relative space-y-2",children:[(0,r.jsx)("input",{type:"file",id:"statement-upload",accept:".pdf,.csv,.xlsx",className:"hidden",onChange:x,disabled:t||n}),(0,r.jsx)("label",{htmlFor:"statement-upload",children:(0,r.jsxs)(o.$,{variant:"secondary",className:`cursor-pointer ${t||n?"opacity-50":""}`,disabled:t||n,onClick:()=>document.getElementById("statement-upload")?.click(),type:"button",children:[(0,r.jsx)(h.A,{className:"w-4 h-4 mr-2"}),t?`Uploading... ${l}%`:n?"Processing...":"Upload Statement"]})}),c&&(0,r.jsxs)("div",{className:"flex items-center text-sm text-red-500 mt-2",children:[(0,r.jsx)(f.A,{className:"w-4 h-4 mr-1"}),(0,r.jsx)("span",{children:c})]}),u&&(0,r.jsxs)("div",{className:"flex items-center text-sm text-green-500 mt-2",children:[(0,r.jsx)(g.A,{className:"w-4 h-4 mr-1"}),(0,r.jsx)("span",{children:u})]})]})}var w=s(41550),N=s(96882);function A({onRetrieveComplete:e}){let[t,s]=(0,a.useState)(!1),[n,i]=(0,a.useState)(null),[l,d]=(0,a.useState)(null),c=async()=>{s(!0),i(null),d(null);try{let t=await v();console.log("Email processing response:",t),d(t),t.success&&e()}catch(s){console.error("Error processing email for statements:",s);let e=s?.response?.data?.error||s.message||"Failed to process emails",t=s?.response?.data?.details?.suggestion||"";i(t?`${e}. ${t}`:e)}finally{s(!1)}};return(0,r.jsxs)("div",{className:"relative space-y-2",children:[(0,r.jsxs)(o.$,{variant:"secondary",onClick:c,disabled:t,className:t?"opacity-50":"",children:[(0,r.jsx)(w.A,{className:"w-4 h-4 mr-2"}),t?"Processing...":"Fetch Statements from Email"]}),n&&(0,r.jsxs)("div",{className:"flex items-center text-sm text-red-500 mt-2",children:[(0,r.jsx)(f.A,{className:"w-4 h-4 mr-1"}),(0,r.jsx)("span",{children:n})]}),!n&&l&&(0,r.jsxs)("div",{className:`flex items-center text-sm mt-2 ${l.success?"text-green-500":"text-blue-500"}`,children:[l.success?(0,r.jsx)(g.A,{className:"w-4 h-4 mr-1"}):(0,r.jsx)(N.A,{className:"w-4 h-4 mr-1"}),(0,r.jsx)("span",{children:l.message})]})]})}function P(){let[e,t]=(0,a.useState)([]),[s,n]=(0,a.useState)(!0),[i,l]=(0,a.useState)(null),[o,d]=(0,a.useState)(null),c=async()=>{if(!s||!(e.length>0)){try{n(!0);let e=await y();l(null),t(e),d(new Date)}catch(e){return console.error("Error fetching statements:",e),l(e instanceof Error?e.message:"Failed to fetch statements"),!1}finally{n(!1)}return!0}},m=async()=>{try{n(!0),await c()}catch(e){console.error("Error refreshing statements after upload:",e)}finally{n(!1)}};return i?(0,r.jsx)("div",{className:"py-8 max-w-[1400px] mx-auto px-4",children:(0,r.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[(0,r.jsx)("h2",{className:"text-red-800 text-lg font-semibold mb-2",children:"Error Loading Statements"}),(0,r.jsx)("p",{className:"text-red-600 mb-4",children:i}),(0,r.jsx)("button",{onClick:c,className:"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors",children:"Try Again"})]})}):(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"mb-6 flex justify-between items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Statements"}),(0,r.jsx)("div",{className:"text-sm text-gray-500 mt-1",children:o&&(0,r.jsxs)("span",{children:["Last updated: ",o.toLocaleTimeString()]})})]}),(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)(b,{onUploadComplete:m}),(0,r.jsx)(A,{onRetrieveComplete:m})]})]}),s&&0===e.length?(0,r.jsxs)("div",{className:"py-8 flex items-center justify-center text-gray-500",children:[(0,r.jsxs)("svg",{className:"animate-spin h-5 w-5 mr-3",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4",fill:"none"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Loading statements..."]}):(0,r.jsx)(x,{statements:e})]})}},58186:(e,t,s)=>{Promise.resolve().then(s.bind(s,50944))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63851:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(18962).A)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},75678:(e,t,s)=>{Promise.resolve().then(s.bind(s,35253))},76926:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return o}});let r=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var s=a(t);if(s&&s.has(e))return s.get(e);var r={__proto__:null},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var l=n?Object.getOwnPropertyDescriptor(e,i):null;l&&(l.get||l.set)?Object.defineProperty(r,i,l):r[i]=e[i]}return r.default=e,s&&s.set(e,r),r}(s(61120));function a(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,s=new WeakMap;return(a=function(e){return e?s:t})(e)}let n={current:null},i="function"==typeof r.cache?r.cache:e=>e,l=console.warn;function o(e){return function(...t){l(e(...t))}}i(e=>{try{l(n.current)}finally{n.current=null}})},79551:e=>{"use strict";e.exports=require("url")},88118:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var r=s(65239),a=s(48088),n=s(88170),i=s.n(n),l=s(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let d={children:["",{children:["(admin)",{children:["statements",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,93690)),"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/(admin)/statements/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,10814)),"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/(admin)/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/(admin)/statements/page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(admin)/statements/page",pathname:"/statements",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},93690:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/(admin)/statements/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/(admin)/statements/page.tsx","default")},96882:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(18962).A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,4825,1658,7924,1622,9559,8422],()=>s(88118));module.exports=r})();