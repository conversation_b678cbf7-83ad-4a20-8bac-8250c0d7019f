(()=>{var e={};e.id=6096,e.ids=[6096],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10814:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/(admin)/layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/(admin)/layout.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11860:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(18962).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},15079:(e,t,s)=>{"use strict";s.d(t,{bq:()=>u,eb:()=>x,gC:()=>m,l6:()=>d,yv:()=>c});var r=s(60687);s(43210);var a=s(75294),n=s(78272),l=s(13964),i=s(3589),o=s(4780);function d({...e}){return(0,r.jsx)(a.bL,{"data-slot":"select",...e})}function c({...e}){return(0,r.jsx)(a.WT,{"data-slot":"select-value",...e})}function u({className:e,children:t,...s}){return(0,r.jsxs)(a.l9,{"data-slot":"select-trigger",className:(0,o.cn)("border-input bg-background text-foreground data-[placeholder]:text-muted-foreground flex h-10 w-full items-center justify-between rounded-md border px-3 py-2 text-sm shadow-sm transition-colors outline-none focus:border-primary focus:ring-2 focus:ring-primary/15 hover:border-input/80 disabled:cursor-not-allowed disabled:opacity-50","[&_svg]:size-4 [&_svg]:text-muted-foreground [&_svg]:transition-transform [&_svg]:duration-200","data-[state=open]:[&_svg]:rotate-180",e),...s,children:[t,(0,r.jsx)(a.In,{asChild:!0,children:(0,r.jsx)(n.A,{})})]})}function m({className:e,children:t,position:s="popper",...n}){return(0,r.jsx)(a.ZL,{children:(0,r.jsxs)(a.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-background text-foreground rounded-md shadow-md border border-border z-50","data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","relative max-h-96 min-w-[8rem] overflow-hidden p-1","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...n,children:[(0,r.jsx)(h,{}),(0,r.jsx)(a.LM,{className:(0,o.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,r.jsx)(p,{})]})})}function x({className:e,children:t,...s}){return(0,r.jsxs)(a.q7,{"data-slot":"select-item",className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none transition-colors","focus:bg-accent/10 focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...s,children:[(0,r.jsx)("span",{className:"absolute right-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(a.VF,{children:(0,r.jsx)(l.A,{className:"h-4 w-4 text-primary"})})}),(0,r.jsx)(a.p4,{children:t})]})}function h({className:e,...t}){return(0,r.jsx)(a.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1 text-muted-foreground",e),...t,children:(0,r.jsx)(i.A,{className:"h-4 w-4"})})}function p({className:e,...t}){return(0,r.jsx)(a.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1 text-muted-foreground",e),...t,children:(0,r.jsx)(n.A,{className:"h-4 w-4"})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25376:(e,t,s)=>{Promise.resolve().then(s.bind(s,49753))},26373:(e,t,s)=>{"use strict";s.d(t,{V:()=>d});var r=s(60687);s(43210);var a=s(47033),n=s(14952),l=s(6800),i=s(4780),o=s(29523);function d({className:e,classNames:t,showOutsideDays:s=!0,...d}){return(0,r.jsx)(l.hv,{showOutsideDays:s,className:(0,i.cn)("p-3",e),classNames:{months:"flex flex-col sm:flex-row gap-2",month:"flex flex-col gap-4",caption:"flex justify-center pt-1 relative items-center w-full",caption_label:"text-sm font-medium",nav:"flex items-center gap-1",nav_button:(0,i.cn)((0,o.r)({variant:"outline"}),"size-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-y-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",day:(0,i.cn)((0,o.r)({variant:"ghost"}),"h-9 w-9 p-0 font-normal aria-selected:opacity-100"),day_range_end:"day-range-end",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground opacity-50 aria-selected:bg-accent/50 aria-selected:text-muted-foreground aria-selected:opacity-30",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...t},components:{IconLeft:({...e})=>(0,r.jsx)(a.A,{className:"h-4 w-4"}),IconRight:({...e})=>(0,r.jsx)(n.A,{className:"h-4 w-4"})},...d})}d.displayName="Calendar"},28822:(e,t,s)=>{Promise.resolve().then(s.bind(s,10814))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35253:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m});var r=s(60687),a=s(85814),n=s.n(a),l=s(29523),i=s(62185),o=s(16189),d=s(97051),c=s(40083);function u(){let e=(0,o.usePathname)();return(0,r.jsx)("nav",{className:"bg-white border-b border-border/40 sticky top-0 z-50",children:(0,r.jsx)("div",{className:"max-w-[1400px] mx-auto px-8",children:(0,r.jsxs)("div",{className:"flex justify-between h-16",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0 flex items-center",children:(0,r.jsx)(n(),{href:"/",className:"text-lg font-semibold text-primary",children:"Admin Portal"})}),(0,r.jsx)("div",{className:"hidden sm:ml-10 sm:flex sm:space-x-8",children:[{href:"/overview",label:"Overview"},{href:"/invoices",label:"Invoices"},{href:"/statements",label:"Statements"},{href:"/orders",label:"Orders"},{href:"/checklist",label:"Checklist"},{href:"/reconciliation",label:"Reconciliation"},{href:"/cashflow",label:"Cash Flow"},{href:"/analytics",label:"Analytics"},{href:"/settings",label:"Settings"}].map(({href:t,label:s})=>(0,r.jsx)(n(),{href:t,className:`inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium transition-colors ${e===t?"border-primary text-primary":"border-transparent text-muted-foreground hover:text-foreground hover:border-border"}`,children:s},t))})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(l.$,{variant:"ghost",size:"icon",className:"text-muted-foreground hover:text-foreground",children:(0,r.jsx)(d.A,{className:"h-5 w-5"})}),(0,r.jsxs)(l.$,{variant:"ghost",size:"sm",onClick:()=>{i.ZQ.logout(),window.location.href="/login"},className:"text-sm font-medium text-muted-foreground hover:text-foreground",children:[(0,r.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Sign out"]})]})]})})})}function m({children:e}){return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)(u,{}),(0,r.jsx)("main",{className:"p-8",children:e})]})}},40988:(e,t,s)=>{"use strict";s.d(t,{AM:()=>l,Wv:()=>i,hl:()=>o});var r=s(60687);s(43210);var a=s(40599),n=s(4780);function l({...e}){return(0,r.jsx)(a.bL,{"data-slot":"popover",...e})}function i({...e}){return(0,r.jsx)(a.l9,{"data-slot":"popover-trigger",...e})}function o({className:e,align:t="center",sideOffset:s=4,...l}){return(0,r.jsx)(a.ZL,{children:(0,r.jsx)(a.UC,{"data-slot":"popover-content",align:t,sideOffset:s,className:(0,n.cn)("bg-white","rounded-md","shadow-md","border border-gray-200","p-4","z-50","bg-popover","text-popover-foreground","w-72","outline-hidden","data-[state=open]:animate-in","data-[state=closed]:animate-out","data-[state=closed]:fade-out-0","data-[state=open]:fade-in-0","data-[state=closed]:zoom-out-95","data-[state=open]:zoom-in-95","data-[side=bottom]:slide-in-from-top-2","data-[side=left]:slide-in-from-right-2","data-[side=right]:slide-in-from-left-2","data-[side=top]:slide-in-from-bottom-2",e),...l})})}},49753:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>v});var r=s(60687),a=s(43210),n=s(54220),l=s(40228),i=s(29523),o=s(26373);function d({isOpen:e,onClose:t,onConfirm:s,title:a,message:n,confirmText:l="Confirm",cancelText:o="Cancel"}){return e?(0,r.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[(0,r.jsx)("div",{className:"fixed inset-0 bg-black/50",onClick:t}),(0,r.jsxs)("div",{className:"relative bg-white rounded-lg p-6 w-full max-w-md",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:a}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-6",children:n}),(0,r.jsxs)("div",{className:"flex justify-end gap-2",children:[(0,r.jsx)(i.$,{variant:"outline",onClick:t,children:o}),(0,r.jsx)(i.$,{onClick:()=>{s(),t()},children:l})]})]})]}):null}var c=s(11860);function u({message:e,type:t="info",duration:s=5e3,onClose:n,onUndo:l}){let[o,d]=(0,a.useState)(!0),u=()=>{d(!1),n()};return o?(0,r.jsxs)("div",{className:`
        fixed bottom-4 right-4 flex items-center gap-2 p-4 rounded-lg shadow-lg
        ${"success"===t?"bg-green-100 text-green-800":""}
        ${"error"===t?"bg-red-100 text-red-800":""}
        ${"info"===t?"bg-blue-100 text-blue-800":""}
      `,children:[(0,r.jsx)("p",{className:"text-sm",children:e}),l&&(0,r.jsx)(i.$,{variant:"outline",size:"sm",onClick:()=>{l(),u()},className:"ml-2",children:"Undo"}),(0,r.jsx)("button",{onClick:u,className:"ml-2 p-1 hover:bg-black/5 rounded",children:(0,r.jsx)(c.A,{className:"h-4 w-4"})})]}):null}function m(){let[e,t]=(0,a.useState)(null),s=()=>{t(null)},n=e?(0,r.jsx)(u,{message:e.message,type:e.type,onClose:s,onUndo:e.onUndo}):null;return{showToast:(e,s="info",r)=>{t({message:e,type:s,onUndo:r})},hideToast:s,ToastComponent:n}}var x=s(40988),h=s(15079),p=s(73437),g=s(62185);let f=(e,t,s)=>e!==t?null:(0,r.jsx)(n.A,{className:"ml-1 h-4 w-4 text-gray-900",style:{transform:"asc"===s?"rotate(180deg)":"none"}});function y({items:e}){let{showToast:t,ToastComponent:s}=m(),[n,c]=(0,a.useState)([]),[u,y]=(0,a.useState)(new Set),[j,b]=(0,a.useState)("all"),[v,w]=(0,a.useState)(void 0),[N,k]=(0,a.useState)(void 0),[C,S]=(0,a.useState)("dateChecked"),[P,A]=(0,a.useState)("desc"),[z,$]=(0,a.useState)(1),[_]=(0,a.useState)(10),[O,D]=(0,a.useState)(""),M=e=>{C===e?A("asc"===P?"desc":"asc"):(S(e),A("desc")),$(1)},I=(0,a.useMemo)(()=>e.filter(e=>{let t="all"===j||e.status===j,s=(()=>{if(!v||!N||!e.dateChecked)return!0;let t=new Date(e.dateChecked),s=new Date(v),r=new Date(N);return s.setHours(0,0,0,0),r.setHours(23,59,59,999),t>=s&&t<=r})(),r=!O||e.productName.toLowerCase().includes(O.toLowerCase())||e.pipCode.toLowerCase().includes(O.toLowerCase());return t&&s&&r}),[e,j,v,N,O]),F=(0,a.useMemo)(()=>[...I].sort((e,t)=>{let s=0;switch(C){case"dateChecked":let r=e.dateChecked?new Date(e.dateChecked):null,a=t.dateChecked?new Date(t.dateChecked):null;s=(r?.getTime()??0)-(a?.getTime()??0);break;case"expectedQuantity":s=e.expectedQuantity-t.expectedQuantity;break;case"receivedQuantity":s=(e.receivedQuantity||0)-(t.receivedQuantity||0)}return"desc"===P?-s:s}),[I,C,P]),U=Math.ceil(F.length/_),Q=(0,a.useMemo)(()=>{let e=(z-1)*_;return F.slice(e,e+_)},[F,z,_]),[T,G]=(0,a.useState)({isOpen:!1,itemId:"",newStatus:"pending"}),[q,L]=(0,a.useState)({isOpen:!1,newStatus:"pending"}),R=async(e,t)=>{G({isOpen:!0,itemId:e,newStatus:t})},E=(e,t)=>{c(s=>[...s,{itemId:e,previousStatus:t}])},V=async()=>{try{let s=T.itemId,r=e.find(e=>e.id===s);if(!r)return;E(s,r.status),await g.x0.updateItem(s,{status:T.newStatus}),t(`Item status updated to ${T.newStatus}`,"success",async()=>{let e=n[n.length-1];e&&(await g.x0.updateItem(s,{status:e.previousStatus}),c(e=>e.slice(0,-1)),t("Status change undone","info"))})}catch(e){console.error("Failed to update status:",e),t("Failed to update status","error")}},W=async e=>{L({isOpen:!0,newStatus:e})},B=async()=>{try{let s=Array.from(u),r=s.map(t=>{let s=e.find(e=>e.id===t);return s&&E(t,s.status),{id:t,data:{status:q.newStatus}}});await g.x0.bulkUpdate(r),y(new Set),t(`Updated ${s.length} items to ${q.newStatus}`,"success",async()=>{let e=n.slice(-s.length).map(e=>({id:e.itemId,data:{status:e.previousStatus}}));await g.x0.bulkUpdate(e),c(e=>e.slice(0,-s.length)),t("Bulk status change undone","info")})}catch(e){console.error("Failed to update items:",e),t("Failed to update items","error")}};return(0,r.jsxs)("div",{className:"w-full max-w-[1400px] mx-auto px-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Checklist Items"}),u.size>0&&(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)(i.$,{variant:"outline",size:"sm",onClick:()=>W("received"),children:"Mark Selected as Received"}),(0,r.jsx)(i.$,{variant:"outline",size:"sm",onClick:()=>y(new Set),children:"Clear Selection"})]})]}),(0,r.jsxs)("div",{className:"flex gap-4 mb-6 items-center",children:[(0,r.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"From:"}),(0,r.jsxs)(x.AM,{children:[(0,r.jsx)(x.Wv,{asChild:!0,children:(0,r.jsxs)(i.$,{variant:"outline",children:[(0,r.jsx)(l.A,{className:"mr-2 h-4 w-4"}),v?(0,p.GP)(v,"PPP"):"Pick a date"]})}),(0,r.jsx)(x.hl,{className:"p-2 bg-white rounded-lg shadow-lg border border-gray-200 z-50",children:(0,r.jsx)(o.V,{mode:"single",selected:v,onSelect:w,initialFocus:!0})})]}),(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"To:"}),(0,r.jsxs)(x.AM,{children:[(0,r.jsx)(x.Wv,{asChild:!0,children:(0,r.jsxs)(i.$,{variant:"outline",children:[(0,r.jsx)(l.A,{className:"mr-2 h-4 w-4"}),N?(0,p.GP)(N,"PPP"):"Pick a date"]})}),(0,r.jsx)(x.hl,{className:"p-2 bg-white rounded-lg shadow-lg border border-gray-200 z-50",children:(0,r.jsx)(o.V,{mode:"single",selected:N,onSelect:k,initialFocus:!0})})]})]}),(0,r.jsxs)(h.l6,{value:j,onValueChange:e=>b(e),children:[(0,r.jsx)(h.bq,{className:"w-[180px] border-gray-200",children:(0,r.jsx)(h.yv,{placeholder:"All Status"})}),(0,r.jsxs)(h.gC,{children:[(0,r.jsx)(h.eb,{value:"all",children:"All Status"}),(0,r.jsx)(h.eb,{value:"pending",children:"Pending"}),(0,r.jsx)(h.eb,{value:"received",children:"Received"}),(0,r.jsx)(h.eb,{value:"incorrect",children:"Incorrect"}),(0,r.jsx)(h.eb,{value:"missing",children:"Missing"})]})]}),(0,r.jsx)("input",{type:"text",placeholder:"Search items...",className:"px-3 py-2 border border-gray-200 rounded-md",value:O,onChange:e=>D(e.target.value)})]}),(0,r.jsx)("div",{className:"relative overflow-x-auto bg-white shadow-sm rounded-xl border border-gray-100",children:(0,r.jsxs)("table",{className:"w-full text-sm text-left",children:[(0,r.jsx)("thead",{className:"bg-gray-50 border-b border-gray-100",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{scope:"col",className:"px-6 py-4",children:(0,r.jsx)("input",{type:"checkbox",checked:u.size===Q.length,onChange:e=>{e.target.checked?y(new Set(Q.map(e=>e.id))):y(new Set)},className:"rounded border-gray-300"})}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-4 text-xs font-semibold text-gray-600",children:"Status"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-4 text-xs font-semibold text-gray-600",children:"Product"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-4 text-xs font-semibold text-gray-600",children:"PIP Code"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-4 text-xs font-semibold text-gray-600 text-right cursor-pointer",onClick:()=>M("expectedQuantity"),children:(0,r.jsxs)("div",{className:"flex items-center justify-end",children:[(0,r.jsx)("span",{children:"Expected Qty"}),f(C,"expectedQuantity",P)]})}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-4 text-xs font-semibold text-gray-600 text-right cursor-pointer",onClick:()=>M("receivedQuantity"),children:(0,r.jsxs)("div",{className:"flex items-center justify-end",children:[(0,r.jsx)("span",{children:"Received Qty"}),f(C,"receivedQuantity",P)]})}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-4 text-xs font-semibold text-gray-600",children:"Notes"})]})}),(0,r.jsx)("tbody",{children:0===Q.length?(0,r.jsx)("tr",{children:(0,r.jsx)("td",{colSpan:7,className:"px-6 py-8 text-center text-gray-500",children:"No items found matching your filters"})}):Q.map(e=>(0,r.jsxs)("tr",{className:"bg-white border-b hover:bg-gray-50 transition-colors",children:[(0,r.jsx)("td",{className:"px-6 py-4",children:(0,r.jsx)("input",{type:"checkbox",checked:u.has(e.id),onChange:t=>{let s=new Set(u);t.target.checked?s.add(e.id):s.delete(e.id),y(s)},className:"rounded border-gray-300"})}),(0,r.jsx)("td",{className:"px-6 py-4",children:(0,r.jsxs)(h.l6,{value:e.status,onValueChange:t=>R(e.id,t),children:[(0,r.jsx)(h.bq,{className:"w-[120px]",children:(0,r.jsx)(h.yv,{})}),(0,r.jsxs)(h.gC,{children:[(0,r.jsx)(h.eb,{value:"pending",children:"Pending"}),(0,r.jsx)(h.eb,{value:"received",children:"Received"}),(0,r.jsx)(h.eb,{value:"incorrect",children:"Incorrect"}),(0,r.jsx)(h.eb,{value:"missing",children:"Missing"})]})]})}),(0,r.jsx)("td",{className:"px-6 py-4 text-gray-900",children:e.productName}),(0,r.jsx)("td",{className:"px-6 py-4",children:(0,r.jsx)("div",{className:"text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded inline-block",children:e.pipCode})}),(0,r.jsx)("td",{className:"px-6 py-4 text-right text-gray-600",children:e.expectedQuantity}),(0,r.jsx)("td",{className:"px-6 py-4 text-right text-gray-600",children:e.receivedQuantity||"-"}),(0,r.jsx)("td",{className:"px-6 py-4 text-gray-600",children:e.notes||"-"})]},e.id))})]})}),F.length>0&&(0,r.jsx)("div",{className:"flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6 mt-4 rounded-lg",children:(0,r.jsxs)("div",{className:"flex flex-1 items-center justify-between",children:[(0,r.jsx)("div",{children:(0,r.jsxs)("p",{className:"text-sm text-gray-700",children:["Showing ",(0,r.jsx)("span",{className:"font-medium",children:(z-1)*_+1})," to"," ",(0,r.jsx)("span",{className:"font-medium",children:Math.min(z*_,F.length)})," of"," ",(0,r.jsx)("span",{className:"font-medium",children:F.length})," results"]})}),(0,r.jsxs)("div",{className:"flex gap-1",children:[(0,r.jsx)(i.$,{variant:"outline",size:"sm",onClick:()=>$(1),disabled:1===z,className:"min-w-[70px] text-center",children:"First"}),(0,r.jsx)(i.$,{variant:"outline",size:"sm",onClick:()=>$(e=>Math.max(1,e-1)),disabled:1===z,className:"min-w-[70px] text-center",children:"Previous"}),(0,r.jsx)("div",{className:"flex items-center gap-1 px-2",children:(0,r.jsxs)("span",{className:"text-sm text-gray-700 text-center min-w-[100px]",children:["Page ",z," of ",U]})}),(0,r.jsx)(i.$,{variant:"outline",size:"sm",onClick:()=>$(e=>Math.min(U,e+1)),disabled:z===U,className:"min-w-[70px] text-center",children:"Next"}),(0,r.jsx)(i.$,{variant:"outline",size:"sm",onClick:()=>$(U),disabled:z===U,className:"min-w-[70px] text-center",children:"Last"})]})]})}),(0,r.jsx)(d,{isOpen:T.isOpen,onClose:()=>G({...T,isOpen:!1}),onConfirm:V,title:"Update Item Status",message:`Are you sure you want to mark this item as ${T.newStatus}?`,confirmText:"Update Status"}),(0,r.jsx)(d,{isOpen:q.isOpen,onClose:()=>L({...q,isOpen:!1}),onConfirm:B,title:"Update Multiple Items",message:`Are you sure you want to mark ${u.size} items as ${q.newStatus}?`,confirmText:"Update All"}),s]})}function j({orders:e,onGenerate:t,userId:s}){let{showToast:n,ToastComponent:d}=m(),[c,u]=(0,a.useState)("allSuppliers"),[g,f]=(0,a.useState)(void 0),[y,j]=(0,a.useState)(void 0),[b,v]=(0,a.useState)(!1),[w,N]=(0,a.useState)(new Set),k=(0,a.useMemo)(()=>{console.log("All orders for supplier extraction:",e);let t=Array.from(new Set(e.map(e=>e.supplier??""))).sort();return console.log("Unique suppliers found:",t),t},[e]),C=(0,a.useMemo)(()=>{let e=k.map(e=>e&&""!==e.trim()?e.trim():"N/A");return console.log("Cleaned suppliers:",e),e},[k]),S=(0,a.useMemo)(()=>{console.log("Filtering with:",{supplierFilter:c,startDate:g?.toISOString(),endDate:y?.toISOString()});let t=function(e,t,s,r){let a,n;console.log("Filtering orders with:",{supplier:t,startDate:s?.toISOString(),endDate:r?.toISOString(),totalOrders:e.length}),s&&((a=new Date(s)).setHours(0,0,0,0),console.log("Start date for filtering:",a)),r&&((n=new Date(r)).setHours(23,59,59,999),console.log("End date for filtering:",n));let l=e.filter(e=>{if(t&&(!e.supplier||e.supplier.toLowerCase().trim()!==t.toLowerCase().trim()))return!1;if(a||n){if(!e.dateTime)return!1;let t=null;if(e.dateTime.match(/^\d{2}-\d{2}-\d{4}/)){let[s,r,a]=e.dateTime.split(" ")[0].split("-").map(Number);t=new Date(a,r-1,s)}else t=new Date(e.dateTime);if(!t||isNaN(t.getTime()))return console.log("Invalid date format:",e.dateTime),!1;if(console.log("Order date parsed:",{original:e.dateTime,parsed:t}),a&&t<a||n&&t>n)return!1}return!0});return console.log("Filtered orders:",{filteredCount:l.length}),l}(e,"allSuppliers"!==c?c:void 0,g,y);return console.log("Filtered orders count:",t.length),t},[e,c,g,y]),P=()=>{w.size===S.length?N(new Set):N(new Set(S.map(e=>`${e.orderNo}:${e.pipCode}`)))},A=async()=>{if(0===w.size)return void n("Please select at least one order","error");v(!0);try{var e;let r=(e=S.filter(e=>w.has(`${e.orderNo}:${e.pipCode}`)),e.map(e=>({id:`cl-${e.orderNo}-${e.pipCode}-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,orderId:e.orderNo,productName:e.description,pipCode:e.pipCode,expectedQuantity:e.approvedQty,receivedQuantity:null,status:"pending",notes:"",dateChecked:null,checkedBy:s})));await t(r),n(`Successfully generated ${r.length} checklist items`,"success"),N(new Set)}catch(e){console.error("Failed to generate checklist items:",e),n("Failed to generate checklist items","error")}finally{v(!1)}};return(0,r.jsxs)("div",{className:"w-full max-w-[1400px] mx-auto px-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold text-gray-900",children:"Generate Checklist"}),(0,r.jsx)(i.$,{onClick:A,disabled:b||0===w.size,className:"flex items-center gap-2",children:b?"Generating...":`Generate Checklist (${w.size})`})]}),(0,r.jsxs)("div",{className:"flex gap-4 mb-6 items-center",children:[(0,r.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"From:"}),(0,r.jsxs)(x.AM,{children:[(0,r.jsx)(x.Wv,{asChild:!0,className:"border border-gray-200",children:(0,r.jsxs)(i.$,{variant:"outline",children:[(0,r.jsx)(l.A,{className:"mr-2 h-4 w-4"}),g?(0,p.GP)(g,"PPP"):"Pick a date"]})}),(0,r.jsx)(x.hl,{className:"p-2 bg-white rounded-lg shadow-lg border border-gray-200 z-50",align:"start",children:(0,r.jsx)(o.V,{mode:"single",selected:g,onSelect:e=>{console.log("Start date selected:",e?.toISOString()),f(e)},initialFocus:!0})})]}),(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"To:"}),(0,r.jsxs)(x.AM,{children:[(0,r.jsx)(x.Wv,{asChild:!0,className:"border border-gray-200",children:(0,r.jsxs)(i.$,{variant:"outline",children:[(0,r.jsx)(l.A,{className:"mr-2 h-4 w-4"}),y?(0,p.GP)(y,"PPP"):"Pick a date"]})}),(0,r.jsx)(x.hl,{className:"p-2 bg-white rounded-lg shadow-lg border border-gray-200 z-50",align:"start",children:(0,r.jsx)(o.V,{mode:"single",selected:y,onSelect:e=>{console.log("End date selected:",e?.toISOString()),j(e)},initialFocus:!0})})]})]}),(0,r.jsxs)(h.l6,{value:c,onValueChange:e=>{console.log("Supplier filter changed to:",e),u(e)},children:[(0,r.jsx)(h.bq,{className:"w-[180px] border-gray-200",children:(0,r.jsx)(h.yv,{placeholder:"All Suppliers"})}),(0,r.jsxs)(h.gC,{className:"p-2 bg-white rounded-lg shadow-lg border border-gray-200 z-50",children:[(0,r.jsx)(h.eb,{value:"allSuppliers",children:"All Suppliers"}),C.filter(e=>e&&"N/A"!==e).map(e=>(console.log("Rendering supplier option:",e),(0,r.jsx)(h.eb,{value:e,children:e},e)))]})]}),(0,r.jsx)(i.$,{variant:"outline",size:"sm",onClick:P,className:"ml-auto",children:w.size===S.length?"Deselect All":"Select All"})]}),(0,r.jsx)("div",{className:"relative overflow-x-auto bg-white shadow-sm rounded-xl border border-gray-100",children:(0,r.jsxs)("table",{className:"w-full text-sm text-left",children:[(0,r.jsx)("thead",{className:"bg-gray-50 border-b border-gray-100",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{scope:"col",className:"px-6 py-4",children:(0,r.jsx)("input",{type:"checkbox",checked:S.length>0&&w.size===S.length,onChange:P,className:"rounded border-gray-300"})}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-4 text-xs font-semibold text-gray-600",children:"Supplier"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-4 text-xs font-semibold text-gray-600",children:"Date/Time"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-4 text-xs font-semibold text-gray-600",children:"Details"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-4 text-xs font-semibold text-gray-600 text-right",children:"Quantity"})]})}),(0,r.jsx)("tbody",{children:0===S.length?(0,r.jsx)("tr",{children:(0,r.jsx)("td",{colSpan:5,className:"px-6 py-8 text-center text-gray-500",children:"No orders found matching your filters"})}):S.map((e,t)=>{let s=`${e.orderNo}:${e.pipCode}`,a=`${s}-${t}`,n=w.has(s);return console.log("Rendering order row:",{orderKey:s,uniqueKey:a,order:e}),(0,r.jsxs)("tr",{className:`border-b hover:bg-gray-50 transition-colors ${n?"bg-blue-50":"bg-white"}`,children:[(0,r.jsx)("td",{className:"px-6 py-4",children:(0,r.jsx)("input",{type:"checkbox",checked:n,onChange:()=>{let e=new Set(w);n?e.delete(s):e.add(s),N(e)},className:"rounded border-gray-300"})}),(0,r.jsx)("td",{className:"px-6 py-4 text-gray-600",children:e.supplier||"N/A"}),(0,r.jsx)("td",{className:"px-6 py-4 text-gray-600",children:e.dateTime||""}),(0,r.jsx)("td",{className:"px-6 py-4",children:(0,r.jsxs)("div",{className:"space-y-1.5",children:[(0,r.jsx)("div",{className:"font-medium text-gray-900",children:e.description||"N/A"}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:e.orderNo||""}),(0,r.jsx)("div",{className:"text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded inline-block",children:e.pipCode||""})]})}),(0,r.jsx)("td",{className:"px-6 py-4 text-right text-gray-600",children:(0,r.jsx)("span",{className:"font-medium",children:e.approvedQty})})]},a)})})]})}),d]})}async function b(e){(0,g.B3)();let t=await fetch("/api/checklist/items",{method:"POST",headers:(0,g.ru)({"Content-Type":"application/json"}),body:JSON.stringify({items:e})});if(!t.ok){let e=await t.json();throw Error(e.error?.message||"Failed to create checklist items")}return(await t.json()).items||[]}function v(){let[e,t]=(0,a.useState)("list"),[s,n]=(0,a.useState)([]),[l,o]=(0,a.useState)([]),[d,c]=(0,a.useState)(!0),[u,m]=(0,a.useState)(null),x=async e=>{m(null);try{let s=await b(e);n(e=>[...s,...e]),t("list")}catch(e){throw console.error("Failed to generate checklist:",e),m("Failed to generate checklist. Please make sure you are logged in."),e}};return d?(0,r.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"})}):u?(0,r.jsxs)("div",{className:"flex flex-col justify-center items-center h-64",children:[(0,r.jsx)("div",{className:"text-red-500 mb-4",children:u}),(0,r.jsx)(i.$,{onClick:()=>window.location.href="/login",children:"Go to Login"})]}):"list"===e?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"mb-6 flex justify-end",children:(0,r.jsx)(i.$,{onClick:()=>t("generate"),children:"Generate New Checklist"})}),(0,r.jsx)(y,{items:s})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"mb-6 flex justify-end",children:(0,r.jsx)(i.$,{onClick:()=>t("list"),children:"View Checklist Items"})}),(0,r.jsx)(j,{orders:l,onGenerate:x,userId:"user-1"})]})}},58886:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var r=s(65239),a=s(48088),n=s(88170),l=s.n(n),i=s(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);s.d(t,o);let d={children:["",{children:["(admin)",{children:["checklist",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,85578)),"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/(admin)/checklist/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,10814)),"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/(admin)/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/(admin)/checklist/page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(admin)/checklist/page",pathname:"/checklist",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},75678:(e,t,s)=>{Promise.resolve().then(s.bind(s,35253))},76926:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return o}});let r=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var s=a(t);if(s&&s.has(e))return s.get(e);var r={__proto__:null},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var l in e)if("default"!==l&&Object.prototype.hasOwnProperty.call(e,l)){var i=n?Object.getOwnPropertyDescriptor(e,l):null;i&&(i.get||i.set)?Object.defineProperty(r,l,i):r[l]=e[l]}return r.default=e,s&&s.set(e,r),r}(s(61120));function a(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,s=new WeakMap;return(a=function(e){return e?s:t})(e)}let n={current:null},l="function"==typeof r.cache?r.cache:e=>e,i=console.warn;function o(e){return function(...t){i(e(...t))}}l(e=>{try{i(n.current)}finally{n.current=null}})},79551:e=>{"use strict";e.exports=require("url")},85578:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/(admin)/checklist/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/(admin)/checklist/page.tsx","default")},96e3:(e,t,s)=>{Promise.resolve().then(s.bind(s,85578))}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,4825,1658,7924,1622,9559,7400,7922,8422],()=>s(58886));module.exports=r})();