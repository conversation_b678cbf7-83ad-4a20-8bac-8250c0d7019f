(()=>{var e={};e.id=1107,e.ids=[1107],e.modules={163:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return n}});let n=r(71042).unstable_rethrow;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4671:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var n=r(37413),a=r(17897);let s="http://localhost:3000";(()=>{if("undefined"==typeof document)return;let e=document.cookie.split(";").find(e=>e.trim().startsWith("auth-token="));return e&&e.split("=")[1].trim()})();let o=async(e,t)=>{try{if(!s)throw Error("API URL is not configured");let r=await fetch(`${s}/api/statements/${e}`,{method:"GET",headers:{Accept:"application/json",Authorization:`Bearer ${t}`}});if(!r.ok){let e=await r.json();throw Error(e.error||"Failed to fetch statement")}let n=await r.json();return{...n.statement,id:n.statement.id||n.statement.StatementId,StatementId:n.statement.id||n.statement.StatementId,period:{start:n.statement.period?.start?new Date(n.statement.period.start):null,end:n.statement.period?.end?new Date(n.statement.period.end):null},createdAt:n.statement.createdAt?new Date(n.statement.createdAt):null,updatedAt:n.statement.updatedAt?new Date(n.statement.updatedAt):null,statementDate:n.statement.statementDate?new Date(n.statement.statementDate):null}}catch(e){throw console.error("Error fetching statement:",e),e}};var i=r(39916);r(61120);var l=r(44999);let d=e=>{try{let t=e.period?.start||null,r=e.period?.end||null,n=e.createdAt||null,a=e.updatedAt||null,s=e.statementDate||null;console.log("Frontend: parseStatementData - raw data.period:",e.period),console.log("Frontend: parseStatementData - raw data.createdAt:",e.createdAt,"raw data.updatedAt:",e.updatedAt),console.log("Frontend: parseStatementData - raw data.statementDate (should be Date object or null):",e.statementDate);let o=(e.invoices||[]).map((e,t)=>{console.log(`Frontend: parseStatementData - invoice item ${t} - raw inv.Date:`,e.Date,`(type: ${typeof e.Date})`);let r=e.Date?new Date(e.Date):null;return console.log(`Frontend: parseStatementData - invoice item ${t} - parsed itemDate:`,r),{...e,invoiceId:e.invoiceId||e.InvoiceNumber,date:r,amount:"number"==typeof e.Amount?e.Amount:e.Amount?parseFloat(e.Amount):null,type:e.Type||e.type||"invoice"}}),i={...e,period:{start:t,end:r},invoices:o,createdAt:n,updatedAt:a,statementDate:s};return console.log("Frontend: parseStatementData - final parsed period:",i.period),console.log("Frontend: parseStatementData - final parsed createdAt:",i.createdAt,"final parsed updatedAt:",i.updatedAt),console.log("Frontend: parseStatementData - final parsed statementDate:",i.statementDate),i}catch(e){return console.error("Error parsing statement data:",e),null}};async function c({params:e}){let{statementId:t}=e;t||(0,i.notFound)();let r=null,s=null;try{let e=await (0,l.UL)(),n=e.get("auth-token")?.value;if(!n)throw Error("Authentication token is missing. Please log in.");let a=await o(t,n);if(a||(0,i.notFound)(),!(r=d(a)))throw Error("Failed to parse statement data, potentially invalid date format.")}catch(e){console.error(`Error fetching statement ${t}:`,e),(e.message?.includes("404")||e.message?.includes("not found"))&&(0,i.notFound)(),s=`Failed to load statement: ${e.message||"Unknown error"}`}return s?(0,n.jsx)("div",{className:"p-6 text-red-600 bg-red-100 border border-red-300 rounded-md",children:s}):r?(0,n.jsxs)("div",{className:"container mx-auto p-4 md:p-6 lg:p-8",children:[(0,n.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Statement Details"}),(0,n.jsx)(a.StatementDetail,{statement:r})]}):(0,n.jsx)("div",{className:"p-6 text-orange-600 bg-orange-100 border border-orange-300 rounded-md",children:"Statement data could not be loaded or parsed correctly."})}},10814:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/(admin)/layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/(admin)/layout.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11275:(e,t,r)=>{"use strict";r.d(t,{StatementDetail:()=>u});var n=r(60687),a=r(43210),s=r(54220);let o=e=>"number"!=typeof e?"N/A":e.toLocaleString("en-GB",{minimumFractionDigits:2,maximumFractionDigits:2}),i=e=>{if(!e)return"N/A";try{let t="string"==typeof e||"number"==typeof e?new Date(e):e;if(t instanceof Date&&!isNaN(t.getTime()))return t.toLocaleDateString("en-GB",{day:"2-digit",month:"2-digit",year:"numeric"});return console.warn("formatDateSafely: Input resulted in an Invalid Date:",e,"Parsed as:",t),"Invalid Date"}catch(t){return console.error("formatDateSafely: Error formatting date:",e,t.message),"Date Error"}},l=e=>{if(e instanceof Date)return isNaN(e.getTime())?new Date(0):e;if(!e)return new Date(0);if("string"==typeof e){let t=e.trim().split("/");if(3===t.length){let e=t[0],r=t[1],n=t[2],a=parseInt(e,10),s=parseInt(r,10),o=parseInt(n,10);if(!isNaN(a)&&!isNaN(s)&&!isNaN(o)&&o>1e3&&o<3e3&&s>=1&&s<=12&&a>=1&&a<=31){let e=new Date(o,s-1,a);if(!isNaN(e.getTime())&&e.getFullYear()===o&&e.getMonth()===s-1&&e.getDate()===a)return e}}}return new Date(0)},d=(e,t,r)=>e!==t?null:(0,n.jsx)(s.A,{className:"ml-1 h-4 w-4 text-gray-500",style:{transform:"asc"===r?"rotate(180deg)":"none"}}),c=({invoices:e})=>e&&0!==e.length?(0,n.jsx)(n.Fragment,{children:e.map((e,t)=>(0,n.jsxs)("tr",{className:"bg-white border-b hover:bg-gray-50 transition-colors",children:[(0,n.jsx)("td",{className:"px-6 py-4 text-sm text-gray-900",children:e.invoiceId||"N/A"}),(0,n.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:i(e.date)}),(0,n.jsx)("td",{className:"px-6 py-4 text-sm text-gray-500",children:e.type?e.type.charAt(0).toUpperCase()+e.type.slice(1):"N/A"}),(0,n.jsxs)("td",{className:"px-6 py-4 text-sm text-gray-900 text-right font-medium",children:["\xa3",o(e.amount)]})]},`${e.invoiceId||"item"}-${t}`))}):(0,n.jsx)("tr",{children:(0,n.jsx)("td",{colSpan:4,className:"px-6 py-4 text-center text-gray-500",children:"No invoices listed on this statement."})});function u({statement:e}){let[t,r]=(0,a.useState)("date"),[s,u]=(0,a.useState)("desc");if(!e)return(0,n.jsx)("div",{className:"p-4 text-red-500",children:"Error: Statement data is missing."});let m=e=>{t===e?u("asc"===s?"desc":"asc"):(r(e),u("desc"))},p=(0,a.useMemo)(()=>e.invoices?[...e.invoices].sort((e,r)=>{let n=0;switch(t){case"invoiceId":n=(e.invoiceId||"").localeCompare(r.invoiceId||"");break;case"date":n=l(e.date).getTime()-l(r.date).getTime();break;case"type":n=(e.type||"").localeCompare(r.type||"");break;case"amount":n=(e.amount||0)-(r.amount||0)}return"desc"===s?-n:n}):[],[e.invoices,t,s]);return(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{className:"flex justify-between items-start bg-white p-6 rounded-xl shadow-sm border border-gray-100",children:[(0,n.jsxs)("div",{className:"space-y-1",children:[(0,n.jsx)("h2",{className:"text-xl font-semibold",children:e.supplier||"N/A"}),(0,n.jsxs)("p",{className:"text-sm text-gray-600",children:["Statement Date: ",i(e.statementDate)]}),e.id&&(0,n.jsx)("a",{href:`/api/statements/${e.id}/pdf`,target:"_blank",rel:"noopener noreferrer",className:"inline-block mt-2 px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors",children:"View Statement PDF"})]}),(0,n.jsxs)("div",{className:"text-right space-y-2",children:[(0,n.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,n.jsxs)("div",{children:["Created: ",i(e.createdAt)]}),(0,n.jsxs)("div",{children:["Updated: ",i(e.updatedAt)]})]}),(0,n.jsxs)("div",{className:"text-lg font-bold mt-4",children:["Total Due: \xa3",o(e.totalAmount)]})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"font-semibold mb-4",children:"Included Invoices/Items"}),(0,n.jsx)("div",{className:"relative overflow-x-auto bg-white shadow-sm rounded-xl border border-gray-100",children:(0,n.jsxs)("table",{className:"w-full text-sm text-left",children:[(0,n.jsx)("thead",{className:"bg-gray-50 border-b border-gray-100",children:(0,n.jsxs)("tr",{children:[(0,n.jsx)("th",{scope:"col",className:"group cursor-pointer px-6 py-4 text-xs font-semibold text-gray-600",onClick:()=>m("invoiceId"),children:(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("span",{children:"Invoice/Item ID"}),d(t,"invoiceId",s)]})}),(0,n.jsx)("th",{scope:"col",className:"group cursor-pointer px-6 py-4 text-xs font-semibold text-gray-600",onClick:()=>m("date"),children:(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("span",{children:"Date"}),d(t,"date",s)]})}),(0,n.jsx)("th",{scope:"col",className:"group cursor-pointer px-6 py-4 text-xs font-semibold text-gray-600",onClick:()=>m("type"),children:(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("span",{children:"Type"})," ",d(t,"type",s)]})}),(0,n.jsx)("th",{scope:"col",className:"group cursor-pointer px-6 py-4 text-xs font-semibold text-gray-600 text-right",onClick:()=>m("amount"),children:(0,n.jsxs)("div",{className:"flex items-center justify-end",children:[(0,n.jsx)("span",{children:"Amount"}),d(t,"amount",s)]})})]})}),(0,n.jsxs)("tbody",{children:[(0,n.jsx)(c,{invoices:p}),(0,n.jsxs)("tr",{className:"bg-gray-50",children:[(0,n.jsx)("td",{colSpan:3,className:"px-6 py-4 text-sm text-gray-900 font-semibold",children:"Total Amount"}),(0,n.jsxs)("td",{className:"px-6 py-4 text-sm text-gray-900 text-right font-semibold",children:["\xa3",o(e.totalAmount)]})]})]})]})})]})]})}},17897:(e,t,r)=>{"use strict";r.d(t,{StatementDetail:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call StatementDetail() from the server but StatementDetail is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/components/statement-detail.tsx","StatementDetail")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28822:(e,t,r)=>{Promise.resolve().then(r.bind(r,10814))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35253:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m});var n=r(60687),a=r(85814),s=r.n(a),o=r(29523),i=r(62185),l=r(16189),d=r(97051),c=r(40083);function u(){let e=(0,l.usePathname)();return(0,n.jsx)("nav",{className:"bg-white border-b border-border/40 sticky top-0 z-50",children:(0,n.jsx)("div",{className:"max-w-[1400px] mx-auto px-8",children:(0,n.jsxs)("div",{className:"flex justify-between h-16",children:[(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("div",{className:"flex-shrink-0 flex items-center",children:(0,n.jsx)(s(),{href:"/",className:"text-lg font-semibold text-primary",children:"Admin Portal"})}),(0,n.jsx)("div",{className:"hidden sm:ml-10 sm:flex sm:space-x-8",children:[{href:"/overview",label:"Overview"},{href:"/invoices",label:"Invoices"},{href:"/statements",label:"Statements"},{href:"/orders",label:"Orders"},{href:"/checklist",label:"Checklist"},{href:"/reconciliation",label:"Reconciliation"},{href:"/cashflow",label:"Cash Flow"},{href:"/analytics",label:"Analytics"},{href:"/settings",label:"Settings"}].map(({href:t,label:r})=>(0,n.jsx)(s(),{href:t,className:`inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium transition-colors ${e===t?"border-primary text-primary":"border-transparent text-muted-foreground hover:text-foreground hover:border-border"}`,children:r},t))})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,n.jsx)(o.$,{variant:"ghost",size:"icon",className:"text-muted-foreground hover:text-foreground",children:(0,n.jsx)(d.A,{className:"h-5 w-5"})}),(0,n.jsxs)(o.$,{variant:"ghost",size:"sm",onClick:()=>{i.ZQ.logout(),window.location.href="/login"},className:"text-sm font-medium text-muted-foreground hover:text-foreground",children:[(0,n.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Sign out"]})]})]})})})}function m({children:e}){return(0,n.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,n.jsx)(u,{}),(0,n.jsx)("main",{className:"p-8",children:e})]})}},39916:(e,t,r)=>{"use strict";var n=r(97576);r.o(n,"notFound")&&r.d(t,{notFound:function(){return n.notFound}}),r.o(n,"redirect")&&r.d(t,{redirect:function(){return n.redirect}})},46202:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var n=r(65239),a=r(48088),s=r(88170),o=r.n(s),i=r(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let d={children:["",{children:["(admin)",{children:["statements",{children:["[statementId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,4671)),"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/(admin)/statements/[statementId]/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,10814)),"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/(admin)/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/(admin)/statements/[statementId]/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new n.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(admin)/statements/[statementId]/page",pathname:"/statements/[statementId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},48976:(e,t,r)=>{"use strict";function n(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"forbidden",{enumerable:!0,get:function(){return n}}),r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},54220:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(18962).A)("ArrowUpDown",[["path",{d:"m21 16-4 4-4-4",key:"f6ql7i"}],["path",{d:"M17 20V4",key:"1ejh1v"}],["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}]])},62765:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"notFound",{enumerable:!0,get:function(){return a}});let n=""+r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function a(){let e=Object.defineProperty(Error(n),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw e.digest=n,e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68817:(e,t,r)=>{Promise.resolve().then(r.bind(r,11275))},70899:(e,t,r)=>{"use strict";function n(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unauthorized",{enumerable:!0,get:function(){return n}}),r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},71042:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return function e(t){if((0,o.isNextRouterError)(t)||(0,s.isBailoutToCSRError)(t)||(0,l.isDynamicServerError)(t)||(0,i.isDynamicPostpone)(t)||(0,a.isPostpone)(t)||(0,n.isHangingPromiseRejectionError)(t))throw t;t instanceof Error&&"cause"in t&&e(t.cause)}}});let n=r(68388),a=r(52637),s=r(51846),o=r(31162),i=r(84971),l=r(98479);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},75678:(e,t,r)=>{Promise.resolve().then(r.bind(r,35253))},79551:e=>{"use strict";e.exports=require("url")},86897:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getRedirectError:function(){return o},getRedirectStatusCodeFromError:function(){return u},getRedirectTypeFromError:function(){return c},getURLFromRedirectError:function(){return d},permanentRedirect:function(){return l},redirect:function(){return i}});let n=r(52836),a=r(49026),s=r(19121).actionAsyncStorage;function o(e,t,r){void 0===r&&(r=n.RedirectStatusCode.TemporaryRedirect);let s=Object.defineProperty(Error(a.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return s.digest=a.REDIRECT_ERROR_CODE+";"+t+";"+e+";"+r+";",s}function i(e,t){var r;throw null!=t||(t=(null==s||null==(r=s.getStore())?void 0:r.isAction)?a.RedirectType.push:a.RedirectType.replace),o(e,t,n.RedirectStatusCode.TemporaryRedirect)}function l(e,t){throw void 0===t&&(t=a.RedirectType.replace),o(e,t,n.RedirectStatusCode.PermanentRedirect)}function d(e){return(0,a.isRedirectError)(e)?e.digest.split(";").slice(2,-2).join(";"):null}function c(e){if(!(0,a.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return e.digest.split(";",2)[1]}function u(e){if(!(0,a.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(e.digest.split(";").at(-2))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},87313:(e,t,r)=>{Promise.resolve().then(r.bind(r,17897))},97576:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return c},RedirectType:function(){return a.RedirectType},forbidden:function(){return o.forbidden},notFound:function(){return s.notFound},permanentRedirect:function(){return n.permanentRedirect},redirect:function(){return n.redirect},unauthorized:function(){return i.unauthorized},unstable_rethrow:function(){return l.unstable_rethrow}});let n=r(86897),a=r(49026),s=r(62765),o=r(48976),i=r(70899),l=r(163);class d extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class c extends URLSearchParams{append(){throw new d}delete(){throw new d}set(){throw new d}sort(){throw new d}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[4447,4999,4825,1658,7924,1622,8422],()=>r(46202));module.exports=n})();