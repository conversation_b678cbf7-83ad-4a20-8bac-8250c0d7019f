(()=>{var e={};e.id=9531,e.ids=[9531],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10814:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/(admin)/layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/(admin)/layout.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13697:(e,r,t)=>{"use strict";function s(e){return e.approvedQty>0}function n(e){if(!e)return null;let r=e.toLowerCase(),t={aah:"/aah.png",alliance:"/alliance.png",phoenix:"/phoenix.jpg",bestway:"/bestway.jpeg",bns:"/bns.jpeg",cavendish:"/cavendish.jpg",sigma:"/sigma.jpg",otc:"/otc.jpg",trident:"/trident.png",wardle:"/wardles.jpeg"},s={bns:["bns","b&s"],wardle:["wardle","donald wardle"]};for(let[e,n]of Object.entries(s))if(n.some(e=>r.includes(e)))return t[e];let n=Object.keys(t).find(e=>!s[e]&&r.includes(e));return n?t[n]:null}t.d(r,{$:()=>s,v:()=>n})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28822:(e,r,t)=>{Promise.resolve().then(t.bind(t,10814))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33387:(e,r,t)=>{Promise.resolve().then(t.bind(t,94501))},33873:e=>{"use strict";e.exports=require("path")},35253:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>m});var s=t(60687),n=t(85814),o=t.n(n),a=t(29523),i=t(62185),l=t(16189),c=t(97051),d=t(40083);function u(){let e=(0,l.usePathname)();return(0,s.jsx)("nav",{className:"bg-white border-b border-border/40 sticky top-0 z-50",children:(0,s.jsx)("div",{className:"max-w-[1400px] mx-auto px-8",children:(0,s.jsxs)("div",{className:"flex justify-between h-16",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"flex-shrink-0 flex items-center",children:(0,s.jsx)(o(),{href:"/",className:"text-lg font-semibold text-primary",children:"Admin Portal"})}),(0,s.jsx)("div",{className:"hidden sm:ml-10 sm:flex sm:space-x-8",children:[{href:"/overview",label:"Overview"},{href:"/invoices",label:"Invoices"},{href:"/statements",label:"Statements"},{href:"/orders",label:"Orders"},{href:"/checklist",label:"Checklist"},{href:"/reconciliation",label:"Reconciliation"},{href:"/cashflow",label:"Cash Flow"},{href:"/analytics",label:"Analytics"},{href:"/settings",label:"Settings"}].map(({href:r,label:t})=>(0,s.jsx)(o(),{href:r,className:`inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium transition-colors ${e===r?"border-primary text-primary":"border-transparent text-muted-foreground hover:text-foreground hover:border-border"}`,children:t},r))})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)(a.$,{variant:"ghost",size:"icon",className:"text-muted-foreground hover:text-foreground",children:(0,s.jsx)(c.A,{className:"h-5 w-5"})}),(0,s.jsxs)(a.$,{variant:"ghost",size:"sm",onClick:()=>{i.ZQ.logout(),window.location.href="/login"},className:"text-sm font-medium text-muted-foreground hover:text-foreground",children:[(0,s.jsx)(d.A,{className:"h-4 w-4 mr-2"}),"Sign out"]})]})]})})})}function m({children:e}){return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,s.jsx)(u,{}),(0,s.jsx)("main",{className:"p-8",children:e})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},75678:(e,r,t)=>{Promise.resolve().then(t.bind(t,35253))},76926:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return l}});let s=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=n(r);if(t&&t.has(e))return t.get(e);var s={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var i=o?Object.getOwnPropertyDescriptor(e,a):null;i&&(i.get||i.set)?Object.defineProperty(s,a,i):s[a]=e[a]}return s.default=e,t&&t.set(e,s),s}(t(61120));function n(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(n=function(e){return e?t:r})(e)}let o={current:null},a="function"==typeof s.cache?s.cache:e=>e,i=console.warn;function l(e){return function(...r){i(e(...r))}}a(e=>{try{i(o.current)}finally{o.current=null}})},79551:e=>{"use strict";e.exports=require("url")},85218:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var s=t(65239),n=t(48088),o=t(88170),a=t.n(o),i=t(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);t.d(r,l);let c={children:["",{children:["(admin)",{children:["overview",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,90203)),"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/(admin)/overview/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,10814)),"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/(admin)/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/(admin)/overview/page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/(admin)/overview/page",pathname:"/overview",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},90203:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/(admin)/overview/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/(admin)/overview/page.tsx","default")},94501:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>c});var s=t(60687),n=t(43210),o=t(30474),a=t(13697);function i({orders:e}){let r={};e.forEach(e=>{let t=e.supplier;t&&(r[t]||(r[t]={approvedOrders:0,totalSpend:0}),r[t].approvedOrders+=e.approvedQty,r[t].totalSpend+=e.approvedQty*e.price)});let t=Object.keys(r),n=Math.min(t.length,10);return(0,s.jsx)("div",{className:"w-full max-w-[1400px] mx-auto",children:(0,s.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:t.slice(0,n).map(e=>(0,s.jsx)(l,{supplier:e,metrics:r[e]},e))})})}function l({supplier:e,metrics:r}){let t=(0,a.v)(e);return(0,s.jsx)("div",{className:"w-full p-6 bg-white rounded-xl border border-gray-100 shadow-sm hover:shadow-md hover:border-gray-200 transition-all",children:(0,s.jsxs)("div",{className:"flex flex-col items-center",children:[(0,s.jsx)("div",{className:"relative w-24 h-24 mb-4",children:t?(0,s.jsx)(o.default,{src:t,alt:e,fill:!0,sizes:"96px",className:"object-contain",quality:85,priority:!0}):(0,s.jsx)("div",{className:"w-full h-full flex items-center justify-center bg-gray-50 rounded-lg text-gray-500 font-medium",children:e})}),(0,s.jsxs)("div",{className:"w-full space-y-4",children:[(0,s.jsxs)("div",{className:"flex flex-col items-center",children:[(0,s.jsx)("span",{className:"text-sm font-medium text-gray-600 mb-1",children:"Approved Orders"}),(0,s.jsx)("span",{className:"text-2xl font-semibold text-gray-900",children:r.approvedOrders})]}),(0,s.jsxs)("div",{className:"flex flex-col items-center",children:[(0,s.jsx)("span",{className:"text-sm font-medium text-gray-600 mb-1",children:"Total Spend"}),(0,s.jsxs)("span",{className:"text-2xl font-semibold text-gray-900",children:["\xa3",r.totalSpend.toFixed(2)]})]})]})]})})}function c(){return(0,s.jsx)(d,{})}function d(){let[e,r]=(0,n.useState)(null),[t,o]=(0,n.useState)(null);return t?(0,s.jsxs)("div",{className:"p-8",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-red-600 mb-4",children:"Failed to load overview"}),(0,s.jsx)("p",{className:"text-gray-700",children:t})]}):e?(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Overview"})}),(0,s.jsx)(i,{orders:e})]}):(0,s.jsx)("div",{className:"p-8",children:"Loading..."})}},96435:(e,r,t)=>{Promise.resolve().then(t.bind(t,90203))}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,4825,1658,7924,1622,474,8422],()=>t(85218));module.exports=s})();