(()=>{var e={};e.id=2535,e.ids=[2535],e.modules={47:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});var n=t(37413);function s(){return(0,n.jsxs)("div",{className:"p-6",children:[(0,n.jsx)("h1",{className:"text-2xl font-semibold",children:"Cashflow"}),(0,n.jsx)("p",{children:"This page is under construction."})]})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10814:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n});let n=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/(admin)/layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/(admin)/layout.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28822:(e,r,t)=>{Promise.resolve().then(t.bind(t,10814))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35253:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>m});var n=t(60687),s=t(85814),o=t.n(s),a=t(29523),i=t(62185),l=t(16189),c=t(97051),d=t(40083);function u(){let e=(0,l.usePathname)();return(0,n.jsx)("nav",{className:"bg-white border-b border-border/40 sticky top-0 z-50",children:(0,n.jsx)("div",{className:"max-w-[1400px] mx-auto px-8",children:(0,n.jsxs)("div",{className:"flex justify-between h-16",children:[(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("div",{className:"flex-shrink-0 flex items-center",children:(0,n.jsx)(o(),{href:"/",className:"text-lg font-semibold text-primary",children:"Admin Portal"})}),(0,n.jsx)("div",{className:"hidden sm:ml-10 sm:flex sm:space-x-8",children:[{href:"/overview",label:"Overview"},{href:"/invoices",label:"Invoices"},{href:"/statements",label:"Statements"},{href:"/orders",label:"Orders"},{href:"/checklist",label:"Checklist"},{href:"/reconciliation",label:"Reconciliation"},{href:"/cashflow",label:"Cash Flow"},{href:"/analytics",label:"Analytics"},{href:"/settings",label:"Settings"}].map(({href:r,label:t})=>(0,n.jsx)(o(),{href:r,className:`inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium transition-colors ${e===r?"border-primary text-primary":"border-transparent text-muted-foreground hover:text-foreground hover:border-border"}`,children:t},r))})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,n.jsx)(a.$,{variant:"ghost",size:"icon",className:"text-muted-foreground hover:text-foreground",children:(0,n.jsx)(c.A,{className:"h-5 w-5"})}),(0,n.jsxs)(a.$,{variant:"ghost",size:"sm",onClick:()=>{i.ZQ.logout(),window.location.href="/login"},className:"text-sm font-medium text-muted-foreground hover:text-foreground",children:[(0,n.jsx)(d.A,{className:"h-4 w-4 mr-2"}),"Sign out"]})]})]})})})}function m({children:e}){return(0,n.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,n.jsx)(u,{}),(0,n.jsx)("main",{className:"p-8",children:e})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73634:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var n=t(65239),s=t(48088),o=t(88170),a=t.n(o),i=t(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);t.d(r,l);let c={children:["",{children:["(admin)",{children:["cashflow",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,47)),"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/(admin)/cashflow/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,10814)),"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/(admin)/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/(admin)/cashflow/page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new n.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/(admin)/cashflow/page",pathname:"/cashflow",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},75678:(e,r,t)=>{Promise.resolve().then(t.bind(t,35253))},76926:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return l}});let n=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=s(r);if(t&&t.has(e))return t.get(e);var n={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var i=o?Object.getOwnPropertyDescriptor(e,a):null;i&&(i.get||i.set)?Object.defineProperty(n,a,i):n[a]=e[a]}return n.default=e,t&&t.set(e,n),n}(t(61120));function s(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(s=function(e){return e?t:r})(e)}let o={current:null},a="function"==typeof n.cache?n.cache:e=>e,i=console.warn;function l(e){return function(...r){i(e(...r))}}a(e=>{try{i(o.current)}finally{o.current=null}})},78335:()=>{},79551:e=>{"use strict";e.exports=require("url")},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[4447,4825,1658,7924,1622,8422],()=>t(73634));module.exports=n})();