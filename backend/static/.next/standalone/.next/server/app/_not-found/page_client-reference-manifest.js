globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/_not-found/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"670":{"*":{"id":"95269","name":"*","chunks":[],"async":false}},"894":{"*":{"id":"86346","name":"*","chunks":[],"async":false}},"1295":{"*":{"id":"97173","name":"*","chunks":[],"async":false}},"1623":{"*":{"id":"63879","name":"*","chunks":[],"async":false}},"1815":{"*":{"id":"11275","name":"*","chunks":[],"async":false}},"2972":{"*":{"id":"49753","name":"*","chunks":[],"async":false}},"3440":{"*":{"id":"35253","name":"*","chunks":[],"async":false}},"3481":{"*":{"id":"50944","name":"*","chunks":[],"async":false}},"4303":{"*":{"id":"70744","name":"*","chunks":[],"async":false}},"4911":{"*":{"id":"28827","name":"*","chunks":[],"async":false}},"4970":{"*":{"id":"27924","name":"*","chunks":[],"async":false}},"5761":{"*":{"id":"19526","name":"*","chunks":[],"async":false}},"6178":{"*":{"id":"82131","name":"*","chunks":[],"async":false}},"6614":{"*":{"id":"35656","name":"*","chunks":[],"async":false}},"6975":{"*":{"id":"40099","name":"*","chunks":[],"async":false}},"7104":{"*":{"id":"94501","name":"*","chunks":[],"async":false}},"7555":{"*":{"id":"38243","name":"*","chunks":[],"async":false}},"9665":{"*":{"id":"62763","name":"*","chunks":[],"async":false}},"9690":{"*":{"id":"69488","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/node_modules/next/dist/client/components/client-page.js":{"id":894,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/node_modules/next/dist/esm/client/components/client-page.js":{"id":894,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/node_modules/next/dist/client/components/client-segment.js":{"id":4970,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/node_modules/next/dist/esm/client/components/client-segment.js":{"id":4970,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/node_modules/next/dist/client/components/error-boundary.js":{"id":6614,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":6614,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"id":6975,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js":{"id":6975,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/node_modules/next/dist/client/components/layout-router.js":{"id":7555,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/node_modules/next/dist/esm/client/components/layout-router.js":{"id":7555,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/node_modules/next/dist/client/components/metadata/async-metadata.js":{"id":4911,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/node_modules/next/dist/esm/client/components/metadata/async-metadata.js":{"id":4911,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"id":9665,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js":{"id":9665,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/node_modules/next/dist/client/components/render-from-template-context.js":{"id":1295,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":1295,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/globals.css":{"id":2728,"name":"*","chunks":["7177","static/chunks/app/layout-aa6e4e82a3b0f79f.js"],"async":false},"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/login/page.tsx":{"id":9690,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/(admin)/layout.tsx":{"id":3440,"name":"*","chunks":["9352","static/chunks/9352-ade9e14a23bab32f.js","554","static/chunks/554-72349a2c917da050.js","4012","static/chunks/app/(admin)/layout-e000fa4aed6fc8fb.js"],"async":false},"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/(admin)/invoices/[invoiceId]/page.tsx":{"id":5761,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/(admin)/invoices/page.tsx":{"id":6178,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/(admin)/checklist/page.tsx":{"id":2972,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/(admin)/overview/page.tsx":{"id":7104,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/(admin)/statements/page.tsx":{"id":3481,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/(admin)/settings/page.tsx":{"id":1623,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/(admin)/reconciliation/page.tsx":{"id":670,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/components/statement-detail.tsx":{"id":1815,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/components/orders-table.tsx":{"id":4303,"name":"*","chunks":[],"async":false}},"entryCSSFiles":{"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/":[],"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/layout":[{"inlined":false,"path":"static/css/e0a56f776c8af4c8.css"}],"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/page":[],"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/(admin)/layout":[],"/Users/<USER>/Documents/Algorythms/Projects/PharmAccounts/frontend/src/app/_not-found/page":[]},"rscModuleMapping":{"670":{"*":{"id":"93989","name":"*","chunks":[],"async":false}},"894":{"*":{"id":"16444","name":"*","chunks":[],"async":false}},"1295":{"*":{"id":"31307","name":"*","chunks":[],"async":false}},"1623":{"*":{"id":"93157","name":"*","chunks":[],"async":false}},"1815":{"*":{"id":"17897","name":"*","chunks":[],"async":false}},"2728":{"*":{"id":"61135","name":"*","chunks":[],"async":false}},"2972":{"*":{"id":"85578","name":"*","chunks":[],"async":false}},"3440":{"*":{"id":"10814","name":"*","chunks":[],"async":false}},"3481":{"*":{"id":"93690","name":"*","chunks":[],"async":false}},"4303":{"*":{"id":"20698","name":"*","chunks":[],"async":false}},"4911":{"*":{"id":"12089","name":"*","chunks":[],"async":false}},"4970":{"*":{"id":"16042","name":"*","chunks":[],"async":false}},"5761":{"*":{"id":"42087","name":"*","chunks":[],"async":false}},"6178":{"*":{"id":"39320","name":"*","chunks":[],"async":false}},"6614":{"*":{"id":"88170","name":"*","chunks":[],"async":false}},"6975":{"*":{"id":"49477","name":"*","chunks":[],"async":false}},"7104":{"*":{"id":"90203","name":"*","chunks":[],"async":false}},"7555":{"*":{"id":"29345","name":"*","chunks":[],"async":false}},"9665":{"*":{"id":"46577","name":"*","chunks":[],"async":false}},"9690":{"*":{"id":"94934","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}