exports.id=5545,exports.ids=[5545],exports.modules={278:(e,t,i)=>{"use strict";function a(e,t){return e instanceof Date?new e.constructor(t):new Date(t)}i.d(t,{w:()=>a})},20592:(e,t,i)=>{"use strict";i.d(t,{e:()=>n});var a=i(90672),r=i(278);function n(e,t){var i=-t;let n=(0,a.a)(e);return isNaN(i)?(0,r.w)(e,NaN):(i&&n.setDate(n.getDate()+i),n)}},66293:(e,t,i)=>{"use strict";i.d(t,{IE:()=>m,pG:()=>d});var a=i(90672),r=i(278);function n(e,t){let i=+(0,a.a)(e);return(0,r.w)(e,i+t)}function s(e,t){return n(e,-(36e5*t))}var o=i(20592);function c(e,t){return n(e,-(6e4*t))}let d=[{id:"notif-001",title:"New Invoice Received",message:"A new invoice from AAH Pharmaceuticals has been received and needs to be processed.",type:"info",timestamp:s(new Date,1),read:!1,link:"/invoices/INV-AAH-004",linkText:"View Invoice"},{id:"notif-002",title:"Order Discrepancy Detected",message:"An order from Phoenix Healthcare has discrepancies that need to be resolved.",type:"warning",timestamp:s(new Date,3),read:!1,link:"/reconciliation/ORD-PHX-002",linkText:"View Discrepancies"},{id:"notif-003",title:"Credit Request Approved",message:"Your credit request CR001 for Alliance Healthcare has been approved.",type:"success",timestamp:s(new Date,5),read:!0,link:"/credit-requests/CR001",linkText:"View Credit Request"},{id:"notif-004",title:"Statement Reconciliation Required",message:"The monthly statement from Bestway Pharmacy has been received and needs to be reconciled.",type:"info",timestamp:(0,o.e)(new Date,1),read:!1,link:"/statements/st-004",linkText:"View Statement"},{id:"notif-005",title:"Credit Request Rejected",message:"Your credit request CR002 for Sigma Pharmaceuticals has been rejected.",type:"error",timestamp:(0,o.e)(new Date,2),read:!0,link:"/credit-requests/CR002",linkText:"View Credit Request"}],m=[{id:"act-001",type:"order",action:"Order Retrieved",description:"Retrieved new orders from Drug Comparison",timestamp:c(new Date,15),user:{id:"user-001",name:"John Smith"},details:{"Orders Retrieved":12,Supplier:"Multiple"}},{id:"act-002",type:"checklist",action:"Checklist Generated",description:"Generated checklist items from orders",timestamp:c(new Date,30),user:{id:"user-001",name:"John Smith"},details:{"Items Generated":45,"From Orders":8}},{id:"act-003",type:"checklist",action:"Items Checked",description:"Marked checklist items as received",timestamp:s(new Date,2),user:{id:"user-002",name:"Jane Doe"},details:{"Items Received":32,"Items Missing":3,"Items Incorrect":2}},{id:"act-004",type:"invoice",action:"Invoice Processed",description:"Processed invoice from AAH Pharmaceuticals",timestamp:s(new Date,3),user:{id:"user-002",name:"Jane Doe"},details:{"Invoice ID":"INV-AAH-003",Amount:"\xa3320.25"}},{id:"act-005",type:"statement",action:"Statement Reconciled",description:"Reconciled statement from Phoenix Healthcare",timestamp:s(new Date,4),user:{id:"user-001",name:"John Smith"},details:{"Statement ID":"st-002",Period:"March 2025",Invoices:2,"Total Amount":"\xa32076.00"}},{id:"act-006",type:"credit-request",action:"Credit Request Created",description:"Created credit request for missing items",timestamp:(0,o.e)(new Date,1),user:{id:"user-002",name:"Jane Doe"},details:{"Request ID":"CR003",Supplier:"AAH Pharmaceuticals",Items:3,"Total Amount":"\xa3156.75"}},{id:"act-007",type:"credit-request",action:"Credit Request Approved",description:"Credit request approved by supplier",timestamp:(0,o.e)(new Date,2),user:{id:"user-001",name:"John Smith"},details:{"Request ID":"CR001",Supplier:"Alliance Healthcare","Total Amount":"\xa3210.50"}},{id:"act-008",type:"user",action:"User Login",description:"User logged in to the system",timestamp:c(new Date,5),user:{id:"user-001",name:"John Smith"}},{id:"act-009",type:"user",action:"Settings Updated",description:"User updated notification settings",timestamp:(0,o.e)(new Date,3),user:{id:"user-002",name:"Jane Doe"},details:{"Email Notifications":"Enabled","Browser Notifications":"Disabled"}},{id:"act-010",type:"invoice",action:"Invoice Uploaded",description:"Manually uploaded invoice PDF",timestamp:(0,o.e)(new Date,4),user:{id:"user-001",name:"John Smith"},details:{"Invoice ID":"INV-ALH-002",Supplier:"Alliance Healthcare",Amount:"\xa3775.25"}}]},78335:()=>{},90672:(e,t,i)=>{"use strict";function a(e){let t=Object.prototype.toString.call(e);return e instanceof Date||"object"==typeof e&&"[object Date]"===t?new e.constructor(+e):new Date("number"==typeof e||"[object Number]"===t||"string"==typeof e||"[object String]"===t?e:NaN)}i.d(t,{a:()=>a})},96487:()=>{}};