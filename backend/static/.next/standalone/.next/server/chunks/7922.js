"use strict";exports.id=7922,exports.ids=[7922],exports.modules={3589:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(18962).A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},13964:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(18962).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},75294:(e,t,r)=>{r.d(t,{UC:()=>eE,In:()=>eI,q7:()=>eM,VF:()=>eA,p4:()=>eL,ZL:()=>eP,bL:()=>ek,wn:()=>eB,PP:()=>eH,l9:()=>eR,WT:()=>eN,LM:()=>eD});var n=r(43210),l=r(51215);function o(e,[t,r]){return Math.min(r,Math.max(t,e))}var a=r(70569),i=r(9510),s=r(98599),d=r(11273),u=r(43),c=r(31355),p=r(1359),f=r(32547),h=r(96963),v=r(38674),m=r(25028),w=r(14163),g=r(8730),x=r(13495),y=r(65551),S=r(66156),b=r(60687),C=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});n.forwardRef((e,t)=>(0,b.jsx)(w.sG.span,{...e,ref:t,style:{...C,...e.style}})).displayName="VisuallyHidden";var j=r(63376),T=r(8970),k=[" ","Enter","ArrowUp","ArrowDown"],R=[" ","Enter"],N="Select",[I,P,E]=(0,i.N)(N),[D,M]=(0,d.A)(N,[E,v.Bk]),L=(0,v.Bk)(),[A,H]=D(N),[B,_]=D(N),V=e=>{let{__scopeSelect:t,children:r,open:l,defaultOpen:o,onOpenChange:a,value:i,defaultValue:s,onValueChange:d,dir:c,name:p,autoComplete:f,disabled:m,required:w,form:g}=e,x=L(t),[S,C]=n.useState(null),[j,T]=n.useState(null),[k,R]=n.useState(!1),P=(0,u.jH)(c),[E,D]=(0,y.i)({prop:l,defaultProp:o??!1,onChange:a,caller:N}),[M,H]=(0,y.i)({prop:i,defaultProp:s,onChange:d,caller:N}),_=n.useRef(null),V=!S||g||!!S.closest("form"),[G,O]=n.useState(new Set),F=Array.from(G).map(e=>e.props.value).join(";");return(0,b.jsx)(v.bL,{...x,children:(0,b.jsxs)(A,{required:w,scope:t,trigger:S,onTriggerChange:C,valueNode:j,onValueNodeChange:T,valueNodeHasChildren:k,onValueNodeHasChildrenChange:R,contentId:(0,h.B)(),value:M,onValueChange:H,open:E,onOpenChange:D,dir:P,triggerPointerDownPosRef:_,disabled:m,children:[(0,b.jsx)(I.Provider,{scope:t,children:(0,b.jsx)(B,{scope:e.__scopeSelect,onNativeOptionAdd:n.useCallback(e=>{O(t=>new Set(t).add(e))},[]),onNativeOptionRemove:n.useCallback(e=>{O(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),V?(0,b.jsxs)(eb,{"aria-hidden":!0,required:w,tabIndex:-1,name:p,autoComplete:f,value:M,onChange:e=>H(e.target.value),disabled:m,form:g,children:[void 0===M?(0,b.jsx)("option",{value:""}):null,Array.from(G)]},F):null]})})};V.displayName=N;var G="SelectTrigger",O=n.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:l=!1,...o}=e,i=L(r),d=H(G,r),u=d.disabled||l,c=(0,s.s)(t,d.onTriggerChange),p=P(r),f=n.useRef("touch"),[h,m,g]=ej(e=>{let t=p().filter(e=>!e.disabled),r=t.find(e=>e.value===d.value),n=eT(t,e,r);void 0!==n&&d.onValueChange(n.value)}),x=e=>{u||(d.onOpenChange(!0),g()),e&&(d.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,b.jsx)(v.Mz,{asChild:!0,...i,children:(0,b.jsx)(w.sG.button,{type:"button",role:"combobox","aria-controls":d.contentId,"aria-expanded":d.open,"aria-required":d.required,"aria-autocomplete":"none",dir:d.dir,"data-state":d.open?"open":"closed",disabled:u,"data-disabled":u?"":void 0,"data-placeholder":eC(d.value)?"":void 0,...o,ref:c,onClick:(0,a.m)(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==f.current&&x(e)}),onPointerDown:(0,a.m)(o.onPointerDown,e=>{f.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(x(e),e.preventDefault())}),onKeyDown:(0,a.m)(o.onKeyDown,e=>{let t=""!==h.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&k.includes(e.key)&&(x(),e.preventDefault())})})})});O.displayName=G;var F="SelectValue",K=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:l,children:o,placeholder:a="",...i}=e,d=H(F,r),{onValueNodeHasChildrenChange:u}=d,c=void 0!==o,p=(0,s.s)(t,d.onValueNodeChange);return(0,S.N)(()=>{u(c)},[u,c]),(0,b.jsx)(w.sG.span,{...i,ref:p,style:{pointerEvents:"none"},children:eC(d.value)?(0,b.jsx)(b.Fragment,{children:a}):o})});K.displayName=F;var W=n.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...l}=e;return(0,b.jsx)(w.sG.span,{"aria-hidden":!0,...l,ref:t,children:n||"▼"})});W.displayName="SelectIcon";var U=e=>(0,b.jsx)(m.Z,{asChild:!0,...e});U.displayName="SelectPortal";var z="SelectContent",q=n.forwardRef((e,t)=>{let r=H(z,e.__scopeSelect),[o,a]=n.useState();return((0,S.N)(()=>{a(new DocumentFragment)},[]),r.open)?(0,b.jsx)(J,{...e,ref:t}):o?l.createPortal((0,b.jsx)(Z,{scope:e.__scopeSelect,children:(0,b.jsx)(I.Slot,{scope:e.__scopeSelect,children:(0,b.jsx)("div",{children:e.children})})}),o):null});q.displayName=z;var[Z,X]=D(z),Y=(0,g.TL)("SelectContent.RemoveScroll"),J=n.forwardRef((e,t)=>{let{__scopeSelect:r,position:l="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:i,onPointerDownOutside:d,side:u,sideOffset:h,align:v,alignOffset:m,arrowPadding:w,collisionBoundary:g,collisionPadding:x,sticky:y,hideWhenDetached:S,avoidCollisions:C,...k}=e,R=H(z,r),[N,I]=n.useState(null),[E,D]=n.useState(null),M=(0,s.s)(t,e=>I(e)),[L,A]=n.useState(null),[B,_]=n.useState(null),V=P(r),[G,O]=n.useState(!1),F=n.useRef(!1);n.useEffect(()=>{if(N)return(0,j.Eq)(N)},[N]),(0,p.Oh)();let K=n.useCallback(e=>{let[t,...r]=V().map(e=>e.ref.current),[n]=r.slice(-1),l=document.activeElement;for(let r of e)if(r===l||(r?.scrollIntoView({block:"nearest"}),r===t&&E&&(E.scrollTop=0),r===n&&E&&(E.scrollTop=E.scrollHeight),r?.focus(),document.activeElement!==l))return},[V,E]),W=n.useCallback(()=>K([L,N]),[K,L,N]);n.useEffect(()=>{G&&W()},[G,W]);let{onOpenChange:U,triggerPointerDownPosRef:q}=R;n.useEffect(()=>{if(N){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(q.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(q.current?.y??0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():N.contains(r.target)||U(!1),document.removeEventListener("pointermove",t),q.current=null};return null!==q.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[N,U,q]),n.useEffect(()=>{let e=()=>U(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[U]);let[X,J]=ej(e=>{let t=V().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=eT(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),ee=n.useCallback((e,t,r)=>{let n=!F.current&&!r;(void 0!==R.value&&R.value===t||n)&&(A(e),n&&(F.current=!0))},[R.value]),et=n.useCallback(()=>N?.focus(),[N]),er=n.useCallback((e,t,r)=>{let n=!F.current&&!r;(void 0!==R.value&&R.value===t||n)&&_(e)},[R.value]),en="popper"===l?$:Q,el=en===$?{side:u,sideOffset:h,align:v,alignOffset:m,arrowPadding:w,collisionBoundary:g,collisionPadding:x,sticky:y,hideWhenDetached:S,avoidCollisions:C}:{};return(0,b.jsx)(Z,{scope:r,content:N,viewport:E,onViewportChange:D,itemRefCallback:ee,selectedItem:L,onItemLeave:et,itemTextRefCallback:er,focusSelectedItem:W,selectedItemText:B,position:l,isPositioned:G,searchRef:X,children:(0,b.jsx)(T.A,{as:Y,allowPinchZoom:!0,children:(0,b.jsx)(f.n,{asChild:!0,trapped:R.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,a.m)(o,e=>{R.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,b.jsx)(c.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:d,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>R.onOpenChange(!1),children:(0,b.jsx)(en,{role:"listbox",id:R.contentId,"data-state":R.open?"open":"closed",dir:R.dir,onContextMenu:e=>e.preventDefault(),...k,...el,onPlaced:()=>O(!0),ref:M,style:{display:"flex",flexDirection:"column",outline:"none",...k.style},onKeyDown:(0,a.m)(k.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||J(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=V().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>K(t)),e.preventDefault()}})})})})})})});J.displayName="SelectContentImpl";var Q=n.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:l,...a}=e,i=H(z,r),d=X(z,r),[u,c]=n.useState(null),[p,f]=n.useState(null),h=(0,s.s)(t,e=>f(e)),v=P(r),m=n.useRef(!1),g=n.useRef(!0),{viewport:x,selectedItem:y,selectedItemText:C,focusSelectedItem:j}=d,T=n.useCallback(()=>{if(i.trigger&&i.valueNode&&u&&p&&x&&y&&C){let e=i.trigger.getBoundingClientRect(),t=p.getBoundingClientRect(),r=i.valueNode.getBoundingClientRect(),n=C.getBoundingClientRect();if("rtl"!==i.dir){let l=n.left-t.left,a=r.left-l,i=e.left-a,s=e.width+i,d=Math.max(s,t.width),c=o(a,[10,Math.max(10,window.innerWidth-10-d)]);u.style.minWidth=s+"px",u.style.left=c+"px"}else{let l=t.right-n.right,a=window.innerWidth-r.right-l,i=window.innerWidth-e.right-a,s=e.width+i,d=Math.max(s,t.width),c=o(a,[10,Math.max(10,window.innerWidth-10-d)]);u.style.minWidth=s+"px",u.style.right=c+"px"}let a=v(),s=window.innerHeight-20,d=x.scrollHeight,c=window.getComputedStyle(p),f=parseInt(c.borderTopWidth,10),h=parseInt(c.paddingTop,10),w=parseInt(c.borderBottomWidth,10),g=f+h+d+parseInt(c.paddingBottom,10)+w,S=Math.min(5*y.offsetHeight,g),b=window.getComputedStyle(x),j=parseInt(b.paddingTop,10),T=parseInt(b.paddingBottom,10),k=e.top+e.height/2-10,R=y.offsetHeight/2,N=f+h+(y.offsetTop+R);if(N<=k){let e=a.length>0&&y===a[a.length-1].ref.current;u.style.bottom="0px";let t=Math.max(s-k,R+(e?T:0)+(p.clientHeight-x.offsetTop-x.offsetHeight)+w);u.style.height=N+t+"px"}else{let e=a.length>0&&y===a[0].ref.current;u.style.top="0px";let t=Math.max(k,f+x.offsetTop+(e?j:0)+R);u.style.height=t+(g-N)+"px",x.scrollTop=N-k+x.offsetTop}u.style.margin="10px 0",u.style.minHeight=S+"px",u.style.maxHeight=s+"px",l?.(),requestAnimationFrame(()=>m.current=!0)}},[v,i.trigger,i.valueNode,u,p,x,y,C,i.dir,l]);(0,S.N)(()=>T(),[T]);let[k,R]=n.useState();(0,S.N)(()=>{p&&R(window.getComputedStyle(p).zIndex)},[p]);let N=n.useCallback(e=>{e&&!0===g.current&&(T(),j?.(),g.current=!1)},[T,j]);return(0,b.jsx)(ee,{scope:r,contentWrapper:u,shouldExpandOnScrollRef:m,onScrollButtonChange:N,children:(0,b.jsx)("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:k},children:(0,b.jsx)(w.sG.div,{...a,ref:h,style:{boxSizing:"border-box",maxHeight:"100%",...a.style}})})})});Q.displayName="SelectItemAlignedPosition";var $=n.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:l=10,...o}=e,a=L(r);return(0,b.jsx)(v.UC,{...a,...o,ref:t,align:n,collisionPadding:l,style:{boxSizing:"border-box",...o.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});$.displayName="SelectPopperPosition";var[ee,et]=D(z,{}),er="SelectViewport",en=n.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:l,...o}=e,i=X(er,r),d=et(er,r),u=(0,s.s)(t,i.onViewportChange),c=n.useRef(0);return(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),(0,b.jsx)(I.Slot,{scope:r,children:(0,b.jsx)(w.sG.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:u,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:(0,a.m)(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=d;if(n?.current&&r){let e=Math.abs(c.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,l=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(l<n){let o=l+e,a=Math.min(n,o),i=o-a;r.style.height=a+"px","0px"===r.style.bottom&&(t.scrollTop=i>0?i:0,r.style.justifyContent="flex-end")}}}c.current=t.scrollTop})})})]})});en.displayName=er;var el="SelectGroup",[eo,ea]=D(el);n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=(0,h.B)();return(0,b.jsx)(eo,{scope:r,id:l,children:(0,b.jsx)(w.sG.div,{role:"group","aria-labelledby":l,...n,ref:t})})}).displayName=el;var ei="SelectLabel";n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=ea(ei,r);return(0,b.jsx)(w.sG.div,{id:l.id,...n,ref:t})}).displayName=ei;var es="SelectItem",[ed,eu]=D(es),ec=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:l,disabled:o=!1,textValue:i,...d}=e,u=H(es,r),c=X(es,r),p=u.value===l,[f,v]=n.useState(i??""),[m,g]=n.useState(!1),x=(0,s.s)(t,e=>c.itemRefCallback?.(e,l,o)),y=(0,h.B)(),S=n.useRef("touch"),C=()=>{o||(u.onValueChange(l),u.onOpenChange(!1))};if(""===l)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,b.jsx)(ed,{scope:r,value:l,disabled:o,textId:y,isSelected:p,onItemTextChange:n.useCallback(e=>{v(t=>t||(e?.textContent??"").trim())},[]),children:(0,b.jsx)(I.ItemSlot,{scope:r,value:l,disabled:o,textValue:f,children:(0,b.jsx)(w.sG.div,{role:"option","aria-labelledby":y,"data-highlighted":m?"":void 0,"aria-selected":p&&m,"data-state":p?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...d,ref:x,onFocus:(0,a.m)(d.onFocus,()=>g(!0)),onBlur:(0,a.m)(d.onBlur,()=>g(!1)),onClick:(0,a.m)(d.onClick,()=>{"mouse"!==S.current&&C()}),onPointerUp:(0,a.m)(d.onPointerUp,()=>{"mouse"===S.current&&C()}),onPointerDown:(0,a.m)(d.onPointerDown,e=>{S.current=e.pointerType}),onPointerMove:(0,a.m)(d.onPointerMove,e=>{S.current=e.pointerType,o?c.onItemLeave?.():"mouse"===S.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,a.m)(d.onPointerLeave,e=>{e.currentTarget===document.activeElement&&c.onItemLeave?.()}),onKeyDown:(0,a.m)(d.onKeyDown,e=>{(c.searchRef?.current===""||" "!==e.key)&&(R.includes(e.key)&&C()," "===e.key&&e.preventDefault())})})})})});ec.displayName=es;var ep="SelectItemText",ef=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:o,style:a,...i}=e,d=H(ep,r),u=X(ep,r),c=eu(ep,r),p=_(ep,r),[f,h]=n.useState(null),v=(0,s.s)(t,e=>h(e),c.onItemTextChange,e=>u.itemTextRefCallback?.(e,c.value,c.disabled)),m=f?.textContent,g=n.useMemo(()=>(0,b.jsx)("option",{value:c.value,disabled:c.disabled,children:m},c.value),[c.disabled,c.value,m]),{onNativeOptionAdd:x,onNativeOptionRemove:y}=p;return(0,S.N)(()=>(x(g),()=>y(g)),[x,y,g]),(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(w.sG.span,{id:c.textId,...i,ref:v}),c.isSelected&&d.valueNode&&!d.valueNodeHasChildren?l.createPortal(i.children,d.valueNode):null]})});ef.displayName=ep;var eh="SelectItemIndicator",ev=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return eu(eh,r).isSelected?(0,b.jsx)(w.sG.span,{"aria-hidden":!0,...n,ref:t}):null});ev.displayName=eh;var em="SelectScrollUpButton",ew=n.forwardRef((e,t)=>{let r=X(em,e.__scopeSelect),l=et(em,e.__scopeSelect),[o,a]=n.useState(!1),i=(0,s.s)(t,l.onScrollButtonChange);return(0,S.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){a(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,b.jsx)(ey,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});ew.displayName=em;var eg="SelectScrollDownButton",ex=n.forwardRef((e,t)=>{let r=X(eg,e.__scopeSelect),l=et(eg,e.__scopeSelect),[o,a]=n.useState(!1),i=(0,s.s)(t,l.onScrollButtonChange);return(0,S.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;a(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,b.jsx)(ey,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});ex.displayName=eg;var ey=n.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:l,...o}=e,i=X("SelectScrollButton",r),s=n.useRef(null),d=P(r),u=n.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return n.useEffect(()=>()=>u(),[u]),(0,S.N)(()=>{let e=d().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[d]),(0,b.jsx)(w.sG.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:(0,a.m)(o.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(l,50))}),onPointerMove:(0,a.m)(o.onPointerMove,()=>{i.onItemLeave?.(),null===s.current&&(s.current=window.setInterval(l,50))}),onPointerLeave:(0,a.m)(o.onPointerLeave,()=>{u()})})});n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,b.jsx)(w.sG.div,{"aria-hidden":!0,...n,ref:t})}).displayName="SelectSeparator";var eS="SelectArrow";n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=L(r),o=H(eS,r),a=X(eS,r);return o.open&&"popper"===a.position?(0,b.jsx)(v.i3,{...l,...n,ref:t}):null}).displayName=eS;var eb=n.forwardRef(({__scopeSelect:e,value:t,...r},l)=>{let o=n.useRef(null),a=(0,s.s)(l,o),i=function(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(t);return n.useEffect(()=>{let e=o.current;if(!e)return;let r=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(i!==t&&r){let n=new Event("change",{bubbles:!0});r.call(e,t),e.dispatchEvent(n)}},[i,t]),(0,b.jsx)(w.sG.select,{...r,style:{...C,...r.style},ref:a,defaultValue:t})});function eC(e){return""===e||void 0===e}function ej(e){let t=(0,x.c)(e),r=n.useRef(""),l=n.useRef(0),o=n.useCallback(e=>{let n=r.current+e;t(n),function e(t){r.current=t,window.clearTimeout(l.current),""!==t&&(l.current=window.setTimeout(()=>e(""),1e3))}(n)},[t]),a=n.useCallback(()=>{r.current="",window.clearTimeout(l.current)},[]);return n.useEffect(()=>()=>window.clearTimeout(l.current),[]),[r,o,a]}function eT(e,t,r){var n,l;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=r?e.indexOf(r):-1,i=(n=e,l=Math.max(a,0),n.map((e,t)=>n[(l+t)%n.length]));1===o.length&&(i=i.filter(e=>e!==r));let s=i.find(e=>e.textValue.toLowerCase().startsWith(o.toLowerCase()));return s!==r?s:void 0}eb.displayName="SelectBubbleInput";var ek=V,eR=O,eN=K,eI=W,eP=U,eE=q,eD=en,eM=ec,eL=ef,eA=ev,eH=ew,eB=ex},78272:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(18962).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])}};