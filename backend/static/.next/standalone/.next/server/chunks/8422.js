exports.id=8422,exports.ids=[8422],exports.modules={4780:(e,t,r)=>{"use strict";r.d(t,{cn:()=>i});var o=r(49384),n=r(82348);function i(...e){return(0,n.QP)((0,o.$)(e))}},29523:(e,t,r)=>{"use strict";r.d(t,{$:()=>c,r:()=>a});var o=r(60687);r(43210);var n=r(8730),i=r(24224),s=r(4780);let a=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 [&_svg]:shrink-0 outline-none focus-visible:ring-1 focus-visible:ring-primary/30",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background text-foreground hover:bg-muted/50",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"text-foreground hover:bg-muted/50",link:"text-primary hover:text-primary/80 underline-offset-4 hover:underline p-0 h-auto",subtle:"bg-primary/10 text-primary hover:bg-primary/20","primary-lendwise":"bg-primary text-primary-foreground hover:bg-primary/90 font-medium","secondary-lendwise":"bg-white border border-border text-foreground hover:bg-muted/30 font-medium","create-new":"bg-primary text-primary-foreground hover:bg-primary/90 font-medium rounded-md"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3 text-xs",lg:"h-11 rounded-md px-6 text-base",icon:"h-9 w-9",lendwise:"h-9 px-3 py-2 text-sm","create-new":"h-8 px-3 py-1 text-sm"}},defaultVariants:{variant:"default",size:"default"}});function c({className:e,variant:t,size:r,asChild:i=!1,...c}){let d=i?n.DX:"button";return(0,o.jsx)(d,{"data-slot":"button",className:(0,s.cn)(a({variant:t,size:r,className:e})),...c})}},41777:()=>{},42449:()=>{},61135:()=>{},62185:(e,t,r)=>{"use strict";r.d(t,{B3:()=>i,ZQ:()=>l,ru:()=>d,x0:()=>p,zG:()=>h});let o="http://localhost:3000",n=()=>{if("undefined"==typeof document)return null;let e=document.cookie.split(";").find(e=>e.trim().startsWith("auth-token="));return e?e.split("=")[1].trim():null},i=()=>s=n(),s=n();class a extends Error{constructor(e,t){super(t),this.statusCode=e,this.name="ApiError"}}async function c(e){let t=e.headers.get("content-type");if(!e.ok){let r="An error occurred";if(t?.includes("application/json")){let t=await e.json();r=t.error?.message||t.error||r}throw new a(e.status,r)}if(!t?.includes("application/json"))throw Error("Invalid response format");return e.json()}let d=(e={})=>s?{...e,Authorization:`Bearer ${s}`}:e,l={login:async(e,t)=>{try{let r=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:t}),credentials:"include"}),o=await c(r);return o.success&&o.token&&(s=o.token),o}catch(e){throw console.error("Error during login:",e),e instanceof a?e:Error("Login failed")}},logout:()=>{s=null,document.cookie="auth-token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT"},getSettings:async()=>{try{let e=await fetch("/api/auth/settings",{headers:{Accept:"application/json"}});return c(e)}catch(e){throw console.error("Error fetching settings:",e),e instanceof a?e:Error("Failed to fetch settings")}},updateSettings:async e=>{try{let t=await fetch("/api/auth/settings",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});return c(t)}catch(e){throw console.error("Error updating settings:",e),e instanceof a?e:Error("Failed to update settings")}},devGetPassword:async e=>{try{let t=await fetch(`/api/auth/dev-get-password?email=${encodeURIComponent(e)}`);return await c(t)}catch(e){return console.error("Error fetching dev password:",e),{success:!1,error:"Failed to fetch password"}}},resetPassword:async e=>{try{let t=await fetch("/api/auth/reset-password",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e})});return c(t)}catch(e){return console.error("Error sending password reset:",e),{success:!1,error:"Failed to send password reset email"}}}},p={getItems:async()=>{try{let e=await fetch("/api/checklist/items",{headers:{Accept:"application/json"}});return c(e)}catch(e){throw console.error("Error fetching checklist items:",e),e instanceof a?e:Error("Failed to fetch checklist items")}},updateItem:async(e,t)=>{try{let r=await fetch(`/api/checklist/items/${e}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});return c(r)}catch(e){throw console.error("Error updating checklist item:",e),e instanceof a?e:Error("Failed to update checklist item")}},bulkUpdate:async e=>{try{let t=await fetch("/api/checklist/bulk",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});return c(t)}catch(e){throw console.error("Error bulk updating checklist items:",e),e instanceof a?e:Error("Failed to bulk update checklist items")}}},h={uploadInvoice:async(e,t)=>{try{let r=new FormData;r.append("file",e);let o=new Promise((e,o)=>{let n=new XMLHttpRequest,i=("undefined"!=typeof process&&process.env,"http://localhost:3000");n.open("POST",`${i}/api/invoices/process`),n.withCredentials=!0,n.upload.addEventListener("progress",e=>{if(e.lengthComputable&&t){let r=Math.round(e.loaded/e.total*100);t(r)}}),n.onreadystatechange=()=>{if(n.readyState===XMLHttpRequest.DONE)if(n.status>=200&&n.status<300)try{e(JSON.parse(n.responseText))}catch(t){e({})}else{let e=n.responseText||"Upload failed";"string"==typeof e&&(e.startsWith("<!DOCTYPE")||e.startsWith("<html"))&&(e="Upload failed: server returned an unexpected response (404 or HTML error)"),o(Error(e))}},n.addEventListener("error",()=>{o(Error("Network error during upload"))}),n.send(r)});return await o}catch(e){throw console.error("Error uploading invoice:",e),e}},processEmail:async()=>{try{let e=await fetch("/api/emails/process",{method:"POST",headers:{Accept:"application/json"}}),t=await c(e);return{message:t.message,processed:t.processed||0,errors:t.errors||[],details:t.details||[]}}catch(e){throw console.error("Error processing email:",e),e instanceof a?e:Error("Failed to process email")}},getInvoice:async e=>{try{if(!o)throw Error("API URL is not configured");let t=await fetch(`/api/invoices/${e}`,{method:"GET",headers:d({Accept:"application/json"}),credentials:"include"}),r=await c(t);return{...r,orderReconciled:r.orderReconciled||!1,statementReconciled:r.statementReconciled||!1,Items:r.Items||[],InvoiceDate:r.InvoiceDate,DueDate:r.DueDate,id:r.id||r.InvoiceId,VendorName:r.VendorName||"Unknown Vendor",CustomerName:r.CustomerName||"",InvoiceTotal:r.InvoiceTotal||0}}catch(e){throw console.error("Error fetching invoice:",e),e instanceof a?e:Error("Failed to fetch invoice")}},getInvoices:async()=>{try{let e=await fetch("/api/invoices",{method:"GET",headers:{Accept:"application/json"}});return(await c(e)).map(e=>({...e,orderReconciled:e.orderReconciled||!1,statementReconciled:e.statementReconciled||!1,Items:e.Items||[],InvoiceDate:e.InvoiceDate,DueDate:e.DueDate,id:e.id||e.InvoiceId,VendorName:e.VendorName||"Unknown Vendor",CustomerName:e.CustomerName||"",InvoiceTotal:e.InvoiceTotal||0}))}catch(e){throw console.error("Error fetching invoices:",e),e instanceof a?e:Error("Failed to fetch invoices")}},deleteInvoices:async e=>{try{if(!o)throw Error("API URL is not configured");let t=await fetch(`${o}/api/invoices/delete`,{method:"POST",headers:d({"Content-Type":"application/json",Accept:"application/json"}),body:JSON.stringify({ids:e})});return c(t)}catch(e){throw console.error("Error deleting invoices:",e),e instanceof a?e:Error("Failed to delete invoices")}}}},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var o=r(31658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,o.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},71224:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},80952:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i,metadata:()=>n});var o=r(37413);r(61135);let n={title:"Pharmaccounts Admin",description:"Invoice Management System"};function i({children:e}){return(0,o.jsx)("html",{lang:"en",children:(0,o.jsx)("body",{className:"bg-background",children:(0,o.jsx)("main",{className:"min-h-screen",children:(0,o.jsx)("div",{className:"max-w-[1400px] mx-auto px-8 py-6",children:e})})})})}}};