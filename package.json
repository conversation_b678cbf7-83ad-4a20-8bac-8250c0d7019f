{"name": "pharmaccounts-monorepo", "version": "1.0.0", "private": true, "type": "module", "description": "PharmAccounts - Unified pharmaceutical accounting and reconciliation system", "workspaces": ["backend", "frontend"], "scripts": {"dev": "node scripts/dev.js", "dev:legacy": "concurrently \"npm run dev --workspace backend\" \"npm run dev --workspace frontend\"", "build": "node scripts/build.js", "build:frontend": "npm run build --workspace frontend", "build:backend": "npm install --workspace backend", "start": "npm run start --workspace backend", "start:production": "cd backend && npm start", "install:all": "npm install && npm install --workspace backend && npm install --workspace frontend", "clean": "rm -rf backend/static frontend/.next frontend/out backend/node_modules frontend/node_modules node_modules", "test": "npm run test --workspace frontend", "lint": "npm run lint --workspace frontend", "type-check": "npm run type-check --workspace frontend"}, "devDependencies": {"concurrently": "^8.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["pharmaceutical", "accounting", "reconciliation", "monorepo", "nextjs", "express"], "author": "PharmAccounts Team", "license": "MIT"}